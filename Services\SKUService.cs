using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TabakGokturk.Data;
using TabakGokturk.Entity;
using TabakGokturk.Repositories;

namespace TabakGokturk.Services
{
    public class SKUService : Service<SKU>, ISKUService
    {
        private readonly ApplicationDbContext _context;

        public SKUService(IUnitOfWork unitOfWork, ApplicationDbContext context) : base(unitOfWork)
        {
            _context = context;
        }

        public async Task<IEnumerable<SKU>> GetSKUsWithVariantsAsync()
        {
            return await _context.SKUs
                .Include(s => s.Variants)
                .Include(s => s.SKUCategories)
                    .ThenInclude(sc => sc.Category)
                .ToListAsync();
        }

        public async Task<SKU> GetSKUWithVariantsAsync(int id)
        {
            return await _context.SKUs
                .Include(s => s.Variants)
                .Include(s => s.SKUCategories)
                    .ThenInclude(sc => sc.Category)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<SKU>> GetSKUsByCategoryAsync(int categoryId)
        {
            return await _context.SKUs
                .Include(s => s.Variants)
                .Include(s => s.SKUCategories)
                    .ThenInclude(sc => sc.Category)
                .Where(s => s.SKUCategories.Any(sc => sc.CategoryId == categoryId))
                .ToListAsync();
        }

        public async Task<IEnumerable<SKU>> GetEnabledSKUsAsync()
        {
            return await _context.SKUs
                .Include(s => s.Variants)
                .Include(s => s.SKUCategories)
                    .ThenInclude(sc => sc.Category)
                .Where(s => s.Enabled)
                .ToListAsync();
        }
    }
}
