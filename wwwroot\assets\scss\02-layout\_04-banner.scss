/*----------------------------------------*/
/*  02.04 - Banner CSS
/*----------------------------------------*/

// Banner Section
.banner-section {
}

// Banner Wrapper
.banner-wrapper {
    padding-top: 30px;

    @media #{$tablet-device, $large-mobile} {
        padding-top: 10px;
    }
}

// Banner Item
.banner-item {
    position: relative;
    margin-top: 30px;

    @media #{$laptop-device, $desktop-device} {
        margin-top: 20px;
    }

    &__badge {
        position: absolute;
        top: 25px;
        left: 25px;
        width: 65px;
        height: 65px;
        border-radius: 50%;
        font-size: 22px;
        font-weight: 400;
        line-height: 1em;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $white;
        text-align: center;
        z-index: 3;

        @media #{$laptop-device, $desktop-device} {
            width: 50px;
            height: 50px;
            font-size: 18px;
            top: 15px;
            left: 15px;
        }
        @media #{$tablet-device} {
            width: 50px;
            height: 50px;
            font-size: 16px;
            top: 15px;
            left: 15px;
        }
        @media #{$large-mobile} {
            width: 56px;
            height: 56px;
            font-size: 16px;
            top: 20px;
            left: 20px;
        }

        &.trend {
            background-color: #60bcb5;
        }
        &.hot {
            background-color: #fd4e4e;
        }
        &.sale {
            background-color: #8e74d3;
        }
    }
    &__image {
        overflow: hidden;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(34, 34, 34, 0.7);
            z-index: 2;
            opacity: 0;
            visibility: hidden;
            transition: $transition-base;
        }

        & img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: $transition-base;
        }
    }
    &__content {
        max-width: 400px;
        width: 100%;
        text-align: center;
        margin: 0 auto;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        right: 0;
        z-index: 3;
        opacity: 0;
        visibility: hidden;
        transition: $transition-base;

        @media #{$small-mobile} {
            padding: 0 15px;
        }
    }
    &__title {
        font-size: 42px;
        font-weight: 400;
        font-family: $headings-font-family-02;
        color: $white;
        line-height: 1.2;
        margin-bottom: 20px;

        @media #{$laptop-device, $desktop-device} {
            font-size: 26px;
        }
        @media #{$tablet-device, $large-mobile} {
            font-size: 24px;
        }
    }
    &__divider {
        width: 80px;
        height: 2px;
        background-color: $white;
        margin: 0 auto;
    }
    &__description {
        color: $white;
        font-size: 22px;
        line-height: 1.5;
        margin: 20px 0px 40px;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 16px;
            margin: 15px 0px 20px;
        }
        @media #{$tablet-device, $large-mobile} {
            font-size: 18px;
            margin: 15px 0px 20px;
        }
        @media #{$small-mobile} {
            font-size: 16px;
            margin: 15px 0px 20px;
        }
    }
    &__btn {
        padding: 15px 45px;
        font-size: 16px;
        font-weight: normal;
        line-height: 20px;
        color: $white;
        border: 1px solid $white;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            padding: 12px 25px;
            font-size: 14px;
        }
    }

    &:hover {
        & .banner-item {
            &__image {
                & img {
                    transform: scale(1.1);
                }
                &::before {
                    opacity: 1;
                    visibility: visible;
                }
            }
            &__content {
                opacity: 1;
                visibility: visible;
            }
        }
    }
}

// Banner Item 02
.banner-item-2 {
    padding: 40px 0;
    height: 100%;

    &.banner-bg-01 {
        background-color: #f5e7dc;
    }
    &.banner-bg-02 {
        background-color: #efddc8;
        background-size: contain;
        background-position: center left;
        background-repeat: no-repeat;
    }

    &__inner {
        max-width: 700px;
        margin: 0 auto;
    }

    &__image {
        width: 40%;

        @media #{$large-mobile} {
            width: 100%;
        }

        & img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 50%;
            margin: 0 auto;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                width: 130px;
                height: 130px;
            }
            @media #{$large-mobile} {
                width: 130px;
                height: 130px;
                margin-bottom: 20px;
            }
        }
    }
    &__content {
        width: 60%;

        @media #{$large-mobile} {
            width: 100%;
        }

        & > * {
            margin-top: 10px;

            &:first-child {
                margin-top: 0;
            }
        }
    }
    &__sub-title {
        color: $heading-color;
        font-family: $headings-font-family-02;
        font-size: 26px;
        font-weight: 400;
        line-height: 1;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 20px;
        }
    }
    &__title {
        color: #ed8e52;
        font-family: $headings-font-family-02;
        font-size: 100px;
        font-weight: 400;
        line-height: 1;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 60px;
        }
    }
    &__discount {
        display: block;
        color: #ed8e52;
        font-family: $headings-font-family-02;
        font-weight: 400;
        line-height: 1.2;
        display: flex;
        justify-content: center;
        align-items: center;

        & .discount-sale {
            font-size: 24px;
            font-style: italic;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 20px;
            }
        }
        & .discount-count {
            font-size: 120px;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 80px;
            }
        }
    }
    &__btn {
        font-size: 16px;
        line-height: 20px;
        color: $heading-color;
        background-color: $white;
        padding: 10px 25px;
        display: inline-block;
        transition: $transition-base;

        &:hover {
            background-color: $heading-color;
            border-color: $heading-color;
            color: $white;
        }
    }
}
