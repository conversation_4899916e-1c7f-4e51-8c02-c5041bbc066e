using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TabakGokturk.Entity
{
    public class SKUVariant
    {
        [Key]
        public int Id { get; set; }
        
        public int SKUId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        public int Stock { get; set; }
        
        // Navigation properties
        [ForeignKey("SKUId")]
        public virtual SKU SKU { get; set; }
    }
}
