@model IEnumerable<TabakGokturk.Entity.SKU>
@{
    ViewData["Title"] = "Ürün<PERSON>";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5 pt-4">
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="mb-0"><PERSON>rün<PERSON></h3>
            <a asp-action="Create" class="btn btn-primary"><PERSON><PERSON></a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>@Html.DisplayNameFor(model => model.ProductNumber)</th>
                            <th>@Html.DisplayNameFor(model => model.Name)</th>
                            <th>@Html.DisplayNameFor(model => model.Title)</th>
                            <th>@Html.DisplayNameFor(model => model.Price)</th>
                            <th>@Html.DisplayNameFor(model => model.AvailableItems)</th>
                            <th>@Html.DisplayNameFor(model => model.Enabled)</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@Html.DisplayFor(modelItem => item.ProductNumber)</td>
                                <td>@Html.DisplayFor(modelItem => item.Name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Title)</td>
                                <td>@Html.DisplayFor(modelItem => item.Price)</td>
                                <td>@Html.DisplayFor(modelItem => item.AvailableItems)</td>
                                <td>
                                    @if (item.Enabled)
                                    {
                                        <span class="badge bg-success">Aktif</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Pasif</span>
                                    }
                                </td>
                                <td>
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning me-1">Düzenle</a>
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info me-1">Detaylar</a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Sil</a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
