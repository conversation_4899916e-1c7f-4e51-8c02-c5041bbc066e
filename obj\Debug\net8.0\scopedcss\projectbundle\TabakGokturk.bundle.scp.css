/* _content/TabakGokturk/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-yhdr62qzug] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-yhdr62qzug] {
  color: #0077cc;
}

.btn-primary[b-yhdr62qzug] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-yhdr62qzug], .nav-pills .show > .nav-link[b-yhdr62qzug] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-yhdr62qzug] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-yhdr62qzug] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-yhdr62qzug] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-yhdr62qzug] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-yhdr62qzug] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
