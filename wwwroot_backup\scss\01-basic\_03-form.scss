/*----------------------------------------*/
/*  02.03 - Single Form CSS
/*----------------------------------------*/

// Single Form
.single-form {
    margin-top: 25px;

    &__label {
        font-size: 16px;
        line-height: 1.3;
        margin-bottom: 10px;
    }
    &__input {
        width: 100%;
        font-size: 18px;
        line-height: 20px;
        padding: 20px;
        background-color: transparent;
        border: 1px solid $border-color;
        transition: $transition-base;
        display: block;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            font-size: 16px;
            padding: 15px;
        }
        @media #{$tablet-device} {
            font-size: 16px;
            padding: 12px 15px;
        }

        &:focus {
            border-color: #bbb;
        }
    }
    &__btn {
        background-color: $heading-color;
        border-color: $heading-color;
        color: $white;

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
    &__select {
        background-color: transparent;
        border-radius: 0;
        border: solid 1px $border-color;
        float: none;
        font-size: 18px;
        width: 100%;
        height: auto;
        line-height: 20px;
        outline: none;
        padding: 20px;
        transition: $transition-base;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            font-size: 16px;
            padding: 15px;
        }
        @media #{$tablet-device} {
            font-size: 16px;
            padding: 12px 15px;
        }

        &::after {
            border-bottom: 2px solid #888;
            border-right: 2px solid #888;
            height: 8px;
            right: 20px;
            width: 8px;
        }

        & .nice-select-dropdown {
            border-radius: 0;
            width: 100%;
        }

        & .nice-select-search-box {
            padding: 10px;
        }

        & .nice-select-search {
            background-color: $white;
            border: 1px solid $border-color;
            border-radius: 0;
            color: $heading-color;
            padding: 7px 12px;
            width: 100%;
            min-height: 40px;
            line-height: 30px;
            font-size: 16px;
        }

        & .list {
            border-radius: 0;
        }

        & .option {
            color: $body-color;

            &:hover,
            &.focus,
            &.selected.focus {
                background-color: $brand-color;
                color: $white;
            }
        }
    }

    & select {
        &.select2 {
            display: none;
        }
    }

    & textarea {
        height: 150px;
    }

    & .checkbox-label {
        margin-bottom: 0;

        & span {
            position: relative;
            display: inline-block;
            background-color: #fff;
            border: 1px solid #bbb;
            line-height: 0;
            width: 16px;
            height: 16px;
            margin: -4px 10px 0 0;
            outline: 0;
            text-align: center;
            vertical-align: middle;
            clear: none;
            cursor: pointer;
            -webkit-appearance: none;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: $transition-base;

            &::before {
                position: absolute;
                content: "\ea20";
                font-family: LaStudioIcons;
                font-size: 10px;
                width: 16px;
                height: 16px;
                line-height: 14px;
                text-align: center;
                color: $white;
                top: 0;
                left: 0;
                opacity: 0;
                visibility: hidden;
                transition: $transition-base;
            }
        }
    }

    & input[type="checkbox"] {
        display: none;

        &:checked {
            & + .checkbox-label {
                & span {
                    background-color: $heading-color;
                    border-color: $heading-color;

                    &::before {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }
        }
    }

    & .radio-label {
        margin-bottom: 0;

        & span {
            position: relative;
            display: inline-block;
            background-color: #fff;
            border: 1px solid #bbb;
            line-height: 0;
            width: 14px;
            height: 14px;
            margin: -4px 5px 0 0;
            border-radius: 50%;
            outline: 0;
            text-align: center;
            vertical-align: middle;
            clear: none;
            cursor: pointer;
            -webkit-appearance: none;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: $transition-base;

            &::before {
                position: absolute;
                content: "";
                width: 14px;
                height: 14px;
                background-color: $white;
                border-radius: 50%;
                top: -1px;
                left: -1px;
                transform: scale(0);
                transition: $transition-base;
            }
        }
    }

    & input[type="radio"] {
        display: none;

        &:checked {
            & + .radio-label {
                & span {
                    background-color: $heading-color;
                    border-color: $heading-color;

                    &::before {
                        transform: scale(0.5);
                    }
                }
            }
        }
    }
}
