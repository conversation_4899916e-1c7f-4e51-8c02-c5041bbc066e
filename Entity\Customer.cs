using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TabakGokturk.Entity
{
    public class Customer
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Surname { get; set; }
        
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        [StringLength(100)]
        public string City { get; set; }
        
        [StringLength(100)]
        public string Country { get; set; }
        
        [StringLength(20)]
        public string ZipCode { get; set; }
        
        // Navigation properties
        public virtual ICollection<Order> Orders { get; set; }
        public virtual ICollection<CustomerAddress> Addresses { get; set; }
        
        public Customer()
        {
            Orders = new HashSet<Order>();
            Addresses = new HashSet<CustomerAddress>();
        }
    }
}
