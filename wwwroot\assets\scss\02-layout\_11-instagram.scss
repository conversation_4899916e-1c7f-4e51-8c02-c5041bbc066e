/*----------------------------------------*/
/*  02.11 - Instagram CSS
/*----------------------------------------*/

// Instagram Section
.instagram-section {
}

// Instagram
.instagram-wrapper {
    margin-top: 60px;

    @media #{$laptop-device, $desktop-device} {
        margin-top: 50px;
    }
    @media #{$tablet-device, $large-mobile} {
        margin-top: 40px;
    }
}

// Instagram Item
.instagram-item {
    position: relative;
    overflow: hidden;
    margin-top: 30px;

    @media #{$laptop-device, $desktop-device} {
        margin-top: 22px;
    }
    @media #{$tablet-device} {
        margin-top: 15px;
    }

    &:first-child {
        margin-top: 0;
    }

    & a {
        display: block;
        position: relative;
    }
    &__image {
        position: relative;

        &::before {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            content: "";
            opacity: 0;
            transition: opacity 0.5s cubic-bezier(0.21, 0.75, 0.27, 0.96);
            pointer-events: none;
            background-color: $heading-color;
            z-index: 1;
        }

        & img {
            transition: $transition-base;
            width: 100%;
            height: auto;
            object-fit: cover;
        }
    }
    &__icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transition: opacity 0.5s cubic-bezier(0.21, 0.75, 0.27, 0.96);
        z-index: 2;
        opacity: 0;

        & i {
            font-size: 30px;
            color: $white;
        }
    }

    &:hover {
        & .instagram-item {
            &__image {
                &::before {
                    opacity: 0.2;
                }
                & img {
                    transform: scale(1.1);
                }
            }
            &__icon {
                opacity: 1;
            }
        }
    }
}

// Instagram Item
.instagram-active {
    & .swiper {
        overflow: visible;
    }
}

// Instagram Item
.instagram-col {
    width: 50%;
    padding: 5px;

    @media #{$small-mobile} {
        width: 100%;
    }
}

// Instagram Wrapper
.instagram-wrapper-2 {
    margin-left: -5px;
    margin-right: -5px;
    margin-top: -5px;
    margin-bottom: -5px;
}
