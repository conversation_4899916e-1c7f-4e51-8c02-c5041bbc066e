using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TabakGokturk.Entity
{
    public class Order
    {
        [Key]
        public int Id { get; set; }
        
        public int CustomerId { get; set; }
        
        public int OrderStatusId { get; set; }
        
        public string Note { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        
        public bool IsPaid { get; set; }
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; }
        
        [ForeignKey("OrderStatusId")]
        public virtual OrderStatus OrderStatus { get; set; }
        
        public virtual ICollection<OrderItem> OrderItems { get; set; }
        
        public Order()
        {
            CreatedAt = DateTime.Now;
            OrderItems = new HashSet<OrderItem>();
        }
    }
}
