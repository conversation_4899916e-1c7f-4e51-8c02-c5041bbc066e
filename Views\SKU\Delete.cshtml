@model TabakGokturk.Entity.SKU

@{
    ViewData["Title"] = "Ü<PERSON>ün <PERSON>";
}

<h1><PERSON><PERSON><PERSON><PERSON></h1>

<h3><PERSON>u <PERSON><PERSON><PERSON><PERSON><PERSON> silmek istediğinizden emin misiniz?</h3>
<div>
    <h4>@Model.Title</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.ProductNumber)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.ProductNumber)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Title)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Title)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Price)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Price)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Image)
        </dt>
        <dd class="col-sm-10">
            @if (!string.IsNullOrEmpty(Model.Image))
            {
                <img src="@Model.Image" alt="@Model.Title" style="max-width: 200px;" />
            }
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Sil" class="btn btn-danger" />
        <a asp-action="Index" class="btn btn-secondary">Listeye Dön</a>
    </form>
</div>
