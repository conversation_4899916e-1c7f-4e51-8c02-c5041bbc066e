/*----------------------------------------*/
/*  03.02 Product Single CSS
/*----------------------------------------*/

// Product Single Section
.product-single-section {
}

// Product Single Wrapper
.product-single-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: -30px;
}

// Product Single image
.product-single-col-1 {
    width: 48%;
    margin-top: 30px;

    @media #{$tablet-device, $large-mobile} {
        width: 100%;
    }
}
.product-single-col-2 {
    width: 47%;
    margin-top: 30px;

    @media #{$tablet-device, $large-mobile} {
        width: 100%;
    }
}

// Product Single image
.product-single-image {
}

// Product Single Slide & carousel
.product-single-carousel,
.product-single-slide {
    & .swiper-button-next {
        right: 0;
    }
    & .swiper-button-prev {
        left: 0;
    }
}

// Product Single Slide Item
.product-single-slide-item {
    padding-bottom: 105%;
    position: relative;

    & img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: top;
        background-position: top;
        transition: $transition-base;
        background-color: #f9f9f9;
    }
}

// Product Single Zoom
.product-single-zoom {
    & .zoom {
        position: absolute;
        font-size: 2em;
        z-index: 1;
        text-indent: -9999px;
        border-radius: 100%;
        box-sizing: content-box;
        bottom: 30px;
        right: 30px;
        width: 46px;
        height: 46px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.328' height='16.328'%3E%3Cg fill='none' stroke='%23212121' stroke-miterlimit='10'%3E%3Cpath d='m15.5.828-6 6'/%3E%3Cpath d='M9.697.5h6.131v6.131' stroke-linecap='square'/%3E%3Cpath d='m.5 15.828 6-6'/%3E%3Cpath d='M.5 9.697v6.131h6.131' stroke-linecap='square'/%3E%3C/g%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        background-color: #fff;
        box-shadow: 0 0 6px 0 #00000021;
        cursor: pointer;
    }
    & a {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
    }
}

// Product Single Thumb
.product-single-thumb {
    padding: 30px 15px 0;

    @media #{$tablet-device, $large-mobile} {
        padding: 10px 5px 0;
    }

    & .swiper {
    }
    & .swiper-slide {
        opacity: 0.5;
        transition: $transition-base;
        border: 1px solid transparent;

        &.swiper-slide-thumb-active,
        &:hover {
            opacity: 1;
            border-color: $brand-color;
        }
    }
}

// Product Single Thumb Item
.product-single-thumb-item {
    padding-bottom: 23%;
    position: relative;
    cursor: pointer;

    & img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: top;
        background-position: top;
        transition: $transition-base;
        background-color: #f9f9f9;
    }
}

// Product Single Carousel
.product-single-carousel {
    // Product Single Slide Item
    & .product-single-slide-item {
        padding-bottom: 0;

        & img {
            position: relative;
            width: 100%;
            height: auto;
            object-fit: cover;
        }
    }
}

// Product Single Content
.product-single-content {
    &__title {
        font-size: 36px;
        font-weight: 600;
        font-family: $headings-font-family-02;
        line-height: 1.15;
        color: $heading-color;
        margin-bottom: 40px;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            font-size: 26px;
            margin-bottom: 20px;
        }
    }
    &__price-stock {
        display: flex;
        align-items: center;
        margin-bottom: 25px;

        & > * {
            margin-right: 20px;

            &:last-child {
                margin-right: 0;
            }
        }

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 15px;
        }
    }
    &__price {
        color: $heading-color;
        font-size: 36px;
        font-weight: 400;
        line-height: 1;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            font-size: 26px;
        }

        & del {
            font-size: 28px;
            color: $body-color;
            text-decoration: line-through;

            @media #{$laptop-device, $desktop-device, $large-mobile} {
                font-size: 22px;
            }
        }
        & ins {
            text-decoration: none;
        }
    }
    &__stock {
        display: flex;
        align-items: center;
        gap: 6px;
        color: $heading-color;
        font-size: 14px;
        line-height: 1;

        & .stock-icon {
            color: #80c685;
            font-size: 20px;
        }
        & .stock-text {
        }

        &.out-of-stock {
            & .stock-icon {
                color: #e01a1a;
                font-size: 20px;
            }
        }
    }
    &__short-description {
        margin-bottom: 60px;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 30px;
        }
    }
    &__countdown {
        max-width: 470px;
        margin-bottom: 40px;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 20px;
        }

        & > * {
            margin-top: 10px;

            &:first-child {
                margin-top: 0;
            }
        }

        &--title {
            color: $heading-color;
            font-size: 18px;
            font-weight: 600;
            text-transform: uppercase;

            @media #{$laptop-device, $desktop-device, $large-mobile} {
                font-size: 16px;
            }
        }

        &--progress {
        }
        &--stock {
            display: flex;
            justify-content: space-between;
            align-items: center;

            & .stock-text {
                font-size: 16px;
                color: $body-color;
            }
        }
    }
    &__variable {
        margin-bottom: 25px;
        position: relative;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 15px;
        }

        & .table {
            margin-bottom: 0;

            & > :not(caption) {
                & > * {
                    > * {
                        border: 0;
                        padding: 4px 0;
                        font-weight: 400;
                        font-size: 16px;
                        color: $body-color;
                    }
                }
            }
        }
    }
    &__add-to-cart-wrapper {
        margin-bottom: 80px;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 40px;
        }

        & .product-add-compare,
        & .product-add-wishlist {
            background: none;
            color: $body-color;
            padding: 0;
            margin: 0 2em 0.5em 0;
            font-size: 14px;
            font-weight: 400;
            display: inline-flex;
            align-items: center;
        }
    }
    &__quantity-add-to-cart {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 16px;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 10px;
        }
    }
    &__quantity {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        border: 1px solid rgba($black, 0.1);
        width: 100px;
        text-align: center;
        margin-right: 10px;

        & button {
            width: 20px;
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            font-size: 20px;
            line-height: 1;
            padding: 0;
            border: 0;
            background: none;
        }
        & input {
            width: 33px;
            display: inline-block;
            height: 58px;
            appearance: none;
            color: inherit;
            text-align: center;
            padding: 0px;
            border: 0;
            background: none;

            @media #{$laptop-device, $desktop-device, $large-mobile} {
                height: 56px;
            }
            @media #{$small-mobile} {
                height: 50px;
            }
        }
    }
    &__add-to-cart {
        font-size: 16px;
        padding: 22px 50px;
        line-height: 1;
        background-color: $heading-color;
        border-color: $heading-color;
        color: $white;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            font-size: 14px;
        }
        @media #{$small-mobile} {
            padding: 17px 40px;
        }

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }

    &__meta {
        margin-bottom: 60px;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            margin-bottom: 30px;
        }

        &--item {
            display: flex;
            align-items: center;
            padding: calc(5px / 2) 0;

            @media #{$laptop-device, $desktop-device, $large-mobile} {
                padding: 0;
            }

            &:first-child {
                padding-top: 0;
            }
            &:last-child {
                padding-bottom: 0;
            }

            & .label {
                font-size: 18px;
                line-height: 1.8;
                color: $heading-color;
                white-space: nowrap;
                margin-right: 4px;

                @media #{$laptop-device, $desktop-device, $large-mobile} {
                    font-size: 16px;
                }
            }
            & .content {
                font-size: 18px;
                color: $heading-color;

                & a {
                    color: $body-color;

                    &:hover {
                        color: $brand-color;
                    }
                }
            }
        }
    }

    &__social {
        display: flex;
        align-items: center;
        gap: 20px;

        & .label {
            font-size: 18px;
            line-height: 1.8;
            color: $heading-color;
            white-space: nowrap;
        }
        & .socail-icon {
            display: flex;

            & li {
                margin-right: 15px;

                &:last-child {
                    margin-right: 0;
                }

                & a {
                    width: 40px;
                    height: 40px;
                    line-height: 42px;
                    border-radius: 50%;
                    text-align: center;
                    background-color: $white;
                    font-size: 18px;
                    box-shadow: 0 0 10px 0 rgba($black, 0.1);
                    transition: $transition-base;

                    &:hover {
                        background-color: $heading-color;
                        color: $white;
                    }
                }
            }
        }
    }
}

// Variable Color
.variable-color {
    display: flex;
    flex-wrap: wrap;

    & > * {
        margin: 0 4px;
        border-radius: 50%;

        &:first-child {
            margin-left: 0;
        }
        &:last-child {
            margin-right: 0;
        }

        & span {
            display: block;
            width: 30px;
            height: 30px;
            cursor: pointer;
            transition: $transition-base;
            border-radius: 50%;
        }
    }

    &__color {
        transition: $transition-base;

        &.active {
            box-shadow: 0 0 0 1px $brand-color;

            & span {
                transform: scale(0.85);
            }
        }
    }

    & .blue {
        background: linear-gradient(
            to right,
            rgb(30, 115, 190) 50%,
            rgb(130, 36, 227) 50%
        );
    }
    & .green {
        background: linear-gradient(
            to right,
            rgb(164, 221, 123) 50%,
            rgb(129, 215, 66) 50%
        );
    }
    & .red {
        background: linear-gradient(
            to right,
            rgb(198, 0, 0) 50%,
            rgb(221, 0, 0) 50%
        );
    }
}

// Variable Size
.variable-size {
    display: flex;
    flex-wrap: wrap;

    & > * {
        margin: 0 4px;
        border-radius: 50%;

        &:first-child {
            margin-left: 0;
        }
        &:last-child {
            margin-right: 0;
        }

        & span {
            display: block;
            width: 30px;
            height: 30px;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            transition: $transition-base;
            border-radius: 50%;
        }
    }
    &__size {
        box-shadow: 0 0 1px;

        &.active {
            box-shadow: 0 0 0 1px $brand-color;
        }
    }
}

// Product Single countdown
.product-single-countdown {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
}

// Product Single countdown
.product-single-countdown-item {
    text-align: center;
    margin: 5px;

    &__value {
        color: $heading-color;
        font-size: 40px;
        font-weight: 600;
        line-height: 1;

        @media #{$laptop-device, $desktop-device, $large-mobile} {
            font-size: 36px;
        }
    }
    &__label {
        color: $body-color;
        font-size: 16px;
        line-height: 1;
    }
}

// Product Progress
.product-progress {
    position: relative;
    background-color: #e0e0e0;
    border-radius: 2px;
    height: 15px;
    overflow: hidden;
    margin: 10px 0 5px;

    &__bar {
        position: absolute;
        top: 0;
        left: 0;
        background-color: #e5643e;
        height: 100%;
        border-radius: 2px;
    }
}

// Reset Variable
.reset-variable {
    font-size: 12px;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0;
    margin: 0;
    background: none;
    border: 0;
    opacity: 0;
    visibility: hidden;
    transition: $transition-base;

    &::before {
        content: "";
        font-family: LaStudioIcons;
        padding-right: 5px;
        speak: none;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        vertical-align: middle;
        font-weight: 400;
        display: inline-block;
        line-height: 1;
        position: relative;
        top: -1px;
    }

    &.visible {
        opacity: 1;
        visibility: visible;
    }
}

// Product Single Tabs Section
.product-single-tabs-section {
}

// Product Single Tabs
.product-single-tabs {
    & .nav {
        border-bottom: 1px solid #cbcbcb;

        & li {
            & button {
                padding: 10px 45px;
                font-size: 28px;
                color: $body-color;
                border: 0;
                background: none;
                transition: $transition-base;

                @media #{$large-mobile} {
                    font-size: 22px;
                }

                &:hover,
                &.active {
                    color: $heading-color;
                }
            }
        }
    }

    & .tab-content {
        max-width: 980px;
        width: 100%;
        margin: 0 auto;
        padding-top: 70px;
    }
}

// Product Single Accordion
.product-single-accordion {
    & .accordion {
        border-radius: 0;

        & .accordion-item {
            border-radius: 0;
            margin-top: 30px;
            border: 1px solid #cbcbcb;

            & > button {
                font-size: 20px;
                font-weight: 400;
                line-height: 1;
                padding: 20px 30px;
                color: $heading-color;
                display: block;
                width: 100%;
                border: 0;
                background: none;
                text-align: left;
                position: relative;

                @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                    font-size: 18px;
                    padding: 18px;
                }

                &::after {
                    position: absolute;
                    content: "−";
                    top: 50%;
                    transform: translateY(-50%);
                    right: 30px;
                    color: $heading-color;
                    font-size: 20px;
                    font-weight: 400;
                    line-height: 1;
                    transition: $transition-base;

                    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                        font-size: 18px;
                        right: 18px;
                    }
                }

                &.collapsed {
                    color: $body-color;

                    &::after {
                        content: "+";
                        color: $body-color;
                    }
                }
            }

            &:first-child {
                margin-top: 0;
            }

            & .accordion-collapse {
                padding: 20px 30px 50px;

                @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                    padding: 18px 18px 40px;
                }
            }
        }
    }
}

// Product Single tab Description
.product-single-tab-description {
}

// Product Single tab Description Item
.product-single-tab-description-item {
    margin-top: 26px;

    &:first-child {
        margin-top: 0;
    }

    &__title {
        font-size: 16px;
        font-weight: 600;
        color: $heading-color;
        margin-bottom: 16px;
    }
    & p {
        margin-top: 16px;

        &:first-child {
            margin-top: 0;
        }

        & img {
            width: 150px;
            height: auto;
            object-fit: cover;
        }
    }
}

// Product Single tab Image
.product-single-tab-image {
    text-align: center;

    &__title {
        font-size: 14px;
        font-weight: 400;
        color: $body-color;
        margin-bottom: 16px;
    }
    &__image {
        position: relative;
        overflow: hidden;
        max-width: 350px;
        margin: 0 auto;

        & img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transform: scale(1);
            transition: $transition-base;
        }

        & a {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M356.5 235.5C363.6 239.9 368 247.6 368 256C368 264.4 363.6 272.1 356.5 276.5L212.5 364.5C205.1 369 195.8 369.2 188.3 364.9C180.7 360.7 176 352.7 176 344V167.1C176 159.3 180.7 151.3 188.3 147.1C195.8 142.8 205.1 142.1 212.5 147.5L356.5 235.5zM192 168V344C192 346.9 193.6 349.6 196.1 350.1C198.6 352.4 201.7 352.3 204.2 350.8L348.2 262.8C350.6 261.4 352 258.8 352 256C352 253.2 350.6 250.6 348.2 249.2L204.2 161.2C201.7 159.7 198.6 159.6 196.1 161C193.6 162.4 192 165.1 192 168V168zM0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 496C388.5 496 496 388.5 496 256C496 123.5 388.5 16 256 16C123.5 16 16 123.5 16 256C16 388.5 123.5 496 256 496z' fill='%23fff'/%3E%3C/svg%3E");
            width: 50px;
            height: 50px;
            position: absolute;
            background-size: contain;
            background-position: center;
            left: calc(50% - 25px);
            top: calc(50% - 25px);
            z-index: 1;
        }

        &:hover {
            & img {
                transform: scale(1.1);
            }
        }
    }
}

// Product Single Table
.product-single-table {
    & .table {
        margin-bottom: 0;

        & > :not(caption) {
            & > * {
                > * {
                    border: 1px solid #dedede;
                    padding: 10px;
                    font-weight: 400;
                    font-size: 16px;
                    color: $body-color;
                }
            }
        }
    }
}

// Product Single Review
.product-single-review {
    & > * {
        margin-top: 80px;

        &:first-child {
            margin-top: 0;
        }
    }
}

// Related Product Active
.related-product-active {
    margin-top: 30px;

    @media #{$large-mobile} {
        margin-top: 10px;
    }
}
