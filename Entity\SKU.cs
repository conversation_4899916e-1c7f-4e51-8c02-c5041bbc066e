using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TabakGokturk.Entity
{
    public class SKU
    {
        [Key]
        public int Id { get; set; }
        
        public Guid Guid { get; set; }
        
        public int? ParentId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string ProductNumber { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } // for URL
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; }
        
        public string Description { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OldPrice { get; set; }
        
        [StringLength(255)]
        public string Image { get; set; }
        
        public string Category { get; set; }
        
        public int AvailableItems { get; set; }
        
        public bool PatchEnable { get; set; }
        
        public string PatchText { get; set; }
        
        public bool IsSingleSKU { get; set; }
        
        public bool Enabled { get; set; }
        
        // Navigation properties
        [ForeignKey("ParentId")]
        public virtual SKU Parent { get; set; }
        
        public virtual ICollection<SKU> ChildSKUs { get; set; }
        public virtual ICollection<SKUVariant> Variants { get; set; }
        public virtual ICollection<SKUCategory> SKUCategories { get; set; }
        public virtual ICollection<OrderItem> OrderItems { get; set; }
        
        public SKU()
        {
            Guid = Guid.NewGuid();
            ChildSKUs = new HashSet<SKU>();
            Variants = new HashSet<SKUVariant>();
            SKUCategories = new HashSet<SKUCategory>();
            OrderItems = new HashSet<OrderItem>();
        }
    }
}
