/*----------------------------------------*/
/*  01.01 - Template default CSS
/*----------------------------------------*/

@mixin placeholder {
    &::-webkit-input-placeholder {
        @content;
    }

    &:-moz-placeholder {
        @content;
    }

    &::-moz-placeholder {
        @content;
    }

    &:-ms-input-placeholder {
        @content;
    }
}

/* Common Style */
*,
*::after,
*::before {
    box-sizing: border-box;
}

html,
body {
    height: 100%;
}

body {
    font-family: $font-family-base;
    font-size: 16px;
    font-weight: 400;
    font-style: normal;
    line-height: 1.5;
    overflow-x: hidden;
    color: $body-color;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: $font-family-base;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    color: var(--heading-color);
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    line-height: inherit;
    margin: 0;
}

p {
    margin: 0;
}

a,
button {
    line-height: inherit;
    display: inline-block;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

*:focus {
    outline: none;
}

button:focus,
a:focus {
    text-decoration: none;

    color: inherit;
    outline: none;
}

a:hover {
    text-decoration: none;
    color: var(--hover-color);
}

button,
input[type="submit"] {
    cursor: pointer;
}

img {
    max-width: 100%;
}

input,
textarea {
    @include placeholder {
        opacity: 1;
    }
}

ul,
ol {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

hr {
    border-top-width: 2px;
}
