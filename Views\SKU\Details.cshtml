@model TabakGokturk.Entity.SKU

@{
    ViewData["Title"] = "<PERSON><PERSON>ün Detayları";
}

<h1><PERSON><PERSON><PERSON><PERSON></h1>

<div>
    <h4>@Model.Title</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.ProductNumber)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.ProductNumber)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Title)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Title)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Description)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Description)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Price)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Price)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.OldPrice)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.OldPrice)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Image)
        </dt>
        <dd class="col-sm-10">
            @if (!string.IsNullOrEmpty(Model.Image))
            {
                <img src="@Model.Image" alt="@Model.Title" style="max-width: 200px;" />
            }
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Category)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Category)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.AvailableItems)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.AvailableItems)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.PatchEnable)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.PatchEnable)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.PatchText)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.PatchText)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.IsSingleSKU)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.IsSingleSKU)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Enabled)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Enabled)
        </dd>
    </dl>
</div>

@if (Model.Variants != null && Model.Variants.Any())
{
    <h4>Varyantlar</h4>
    <table class="table">
        <thead>
            <tr>
                <th>Adı</th>
                <th>Stok</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var variant in Model.Variants)
            {
                <tr>
                    <td>@variant.Name</td>
                    <td>@variant.Stock</td>
                </tr>
            }
        </tbody>
    </table>
}

<div>
    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">Düzenle</a>
    <a asp-action="Index" class="btn btn-secondary">Listeye Dön</a>
</div>
