using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TabakGokturk.Entity
{
    public class CustomerAddress
    {
        [Key]
        public int Id { get; set; }
        
        public int CustomerId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string AddressLine1 { get; set; }
        
        [StringLength(200)]
        public string AddressLine2 { get; set; }
        
        [Required]
        [StringLength(100)]
        public string City { get; set; }
        
        [StringLength(100)]
        public string State { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Country { get; set; }
        
        [Required]
        [StringLength(20)]
        public string ZipCode { get; set; }
        
        [StringLength(50)]
        public string AddressType { get; set; } // Shipping, Billing, etc.
        
        public bool IsDefault { get; set; }
        
        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; }
    }
}
