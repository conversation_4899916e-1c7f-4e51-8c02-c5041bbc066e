﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/bootstrap.min.b80niqe365.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b80niqe365"},{"Name":"integrity","Value":"sha256-FQUWY0/B7qHGJIdxzZ1gjjKOi3EcPYiLg9jsalaPovI="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/bootstrap.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"235269"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022FQUWY0/B7qHGJIdxzZ1gjjKOi3EcPYiLg9jsalaPovI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/bootstrap.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FQUWY0/B7qHGJIdxzZ1gjjKOi3EcPYiLg9jsalaPovI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"235269"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022FQUWY0/B7qHGJIdxzZ1gjjKOi3EcPYiLg9jsalaPovI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/glightbox.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\glightbox.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bT9i1NF5afnHDpQ4z2cQBHJQGehoEj8uvClaAG\u002BNXS0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"13749"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022bT9i1NF5afnHDpQ4z2cQBHJQGehoEj8uvClaAG\u002BNXS0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/glightbox.min.jc95ic8utn.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\glightbox.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jc95ic8utn"},{"Name":"integrity","Value":"sha256-bT9i1NF5afnHDpQ4z2cQBHJQGehoEj8uvClaAG\u002BNXS0="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/glightbox.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"13749"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022bT9i1NF5afnHDpQ4z2cQBHJQGehoEj8uvClaAG\u002BNXS0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/lastudioicon.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\lastudioicon.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6H0ZEbGst3o92AfpAcPkGMcloG29QuT4welfSRuc7nk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"12271"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00226H0ZEbGst3o92AfpAcPkGMcloG29QuT4welfSRuc7nk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/lastudioicon.sfz6d6cnrc.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\lastudioicon.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sfz6d6cnrc"},{"Name":"integrity","Value":"sha256-6H0ZEbGst3o92AfpAcPkGMcloG29QuT4welfSRuc7nk="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/lastudioicon.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"12271"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00226H0ZEbGst3o92AfpAcPkGMcloG29QuT4welfSRuc7nk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/nice-select2.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\nice-select2.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Abq11oAuCDkGMrgtjMxXX9lfqcX9h/Ct9\u002BCKs03m5jI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3493"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Abq11oAuCDkGMrgtjMxXX9lfqcX9h/Ct9\u002BCKs03m5jI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/nice-select2.hs43fcarsc.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\nice-select2.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hs43fcarsc"},{"Name":"integrity","Value":"sha256-Abq11oAuCDkGMrgtjMxXX9lfqcX9h/Ct9\u002BCKs03m5jI="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/nice-select2.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3493"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Abq11oAuCDkGMrgtjMxXX9lfqcX9h/Ct9\u002BCKs03m5jI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/style.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JbO\u002BzRU5LQZveiGgsmsy223lf/sOxReki0qZlhZJI10="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"319805"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022JbO\u002BzRU5LQZveiGgsmsy223lf/sOxReki0qZlhZJI10=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/style.hds6a913kz.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hds6a913kz"},{"Name":"integrity","Value":"sha256-JbO\u002BzRU5LQZveiGgsmsy223lf/sOxReki0qZlhZJI10="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/style.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"319805"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022JbO\u002BzRU5LQZveiGgsmsy223lf/sOxReki0qZlhZJI10=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/style.min.1nqq97xdvw.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\style.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1nqq97xdvw"},{"Name":"integrity","Value":"sha256-o4GSfL8pl4Gu7z1t6CrQCCY3kXexnegrm8H9LYpQp/c="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/style.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"256745"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022o4GSfL8pl4Gu7z1t6CrQCCY3kXexnegrm8H9LYpQp/c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/style.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\style.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-o4GSfL8pl4Gu7z1t6CrQCCY3kXexnegrm8H9LYpQp/c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"256745"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022o4GSfL8pl4Gu7z1t6CrQCCY3kXexnegrm8H9LYpQp/c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/swiper-bundle.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\swiper-bundle.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Mi0V2Z77eSyUGlIC\u002Bo/H7p6TKEcic4P/lgUWMzigjqw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16493"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Mi0V2Z77eSyUGlIC\u002Bo/H7p6TKEcic4P/lgUWMzigjqw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/css/swiper-bundle.min.eoitr99uni.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\css\swiper-bundle.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eoitr99uni"},{"Name":"integrity","Value":"sha256-Mi0V2Z77eSyUGlIC\u002Bo/H7p6TKEcic4P/lgUWMzigjqw="},{"Name":"label","Value":"_content/TabakGokturk/assets/css/swiper-bundle.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"16493"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Mi0V2Z77eSyUGlIC\u002Bo/H7p6TKEcic4P/lgUWMzigjqw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.2wpg0ojz6c.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2wpg0ojz6c"},{"Name":"integrity","Value":"sha256-7siNwB98CuSV4gxPP6cRt\u002BvwZJxVRZxj56B6Cts2IOs="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/LaStudioIcons.eot"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"67200"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u00227siNwB98CuSV4gxPP6cRt\u002BvwZJxVRZxj56B6Cts2IOs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.bwlxh0apv6.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bwlxh0apv6"},{"Name":"integrity","Value":"sha256-5YGCgQ4RWZvNEFZIUDh7oZPqsoOtjaZ0rq25m8958oQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/LaStudioIcons.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"67012"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u00225YGCgQ4RWZvNEFZIUDh7oZPqsoOtjaZ0rq25m8958oQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.e6q1vq5xx0.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"e6q1vq5xx0"},{"Name":"integrity","Value":"sha256-xvECJtQloBZDRcc7z7iAvPgw65haDwSGswy9F2CrX5w="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/LaStudioIcons.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"41692"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022xvECJtQloBZDRcc7z7iAvPgw65haDwSGswy9F2CrX5w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7siNwB98CuSV4gxPP6cRt\u002BvwZJxVRZxj56B6Cts2IOs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"67200"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u00227siNwB98CuSV4gxPP6cRt\u002BvwZJxVRZxj56B6Cts2IOs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.s5how9zoy7.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"s5how9zoy7"},{"Name":"integrity","Value":"sha256-4ICILFCylyE\u002BNqUtSbjKtYdytUYMSAUMXoGtUsMKABA="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/LaStudioIcons.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"106028"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00224ICILFCylyE\u002BNqUtSbjKtYdytUYMSAUMXoGtUsMKABA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4ICILFCylyE\u002BNqUtSbjKtYdytUYMSAUMXoGtUsMKABA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"106028"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00224ICILFCylyE\u002BNqUtSbjKtYdytUYMSAUMXoGtUsMKABA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5YGCgQ4RWZvNEFZIUDh7oZPqsoOtjaZ0rq25m8958oQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"67012"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u00225YGCgQ4RWZvNEFZIUDh7oZPqsoOtjaZ0rq25m8958oQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/LaStudioIcons.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\LaStudioIcons.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xvECJtQloBZDRcc7z7iAvPgw65haDwSGswy9F2CrX5w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"41692"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022xvECJtQloBZDRcc7z7iAvPgw65haDwSGswy9F2CrX5w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.5nfj0kj4q4.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5nfj0kj4q4"},{"Name":"integrity","Value":"sha256-8dVTMlXD0MdI\u002B5TnPDCRD7S6A0/58NtLrnvPslDW5/g="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/nucleo-outline.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"251316"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u00228dVTMlXD0MdI\u002B5TnPDCRD7S6A0/58NtLrnvPslDW5/g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.ft8czip3u7.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ft8czip3u7"},{"Name":"integrity","Value":"sha256-o5J2LqsCXPxjh4r9/XxwxOHwt3Nht6//LB9jdvUu29o="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/nucleo-outline.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"582472"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022o5J2LqsCXPxjh4r9/XxwxOHwt3Nht6//LB9jdvUu29o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.pgjl3zef66.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pgjl3zef66"},{"Name":"integrity","Value":"sha256-N7voM3g/shHAcrMBqvtNaoESd3PK4\u002BxpstnReTCsAQE="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/nucleo-outline.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1485683"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022N7voM3g/shHAcrMBqvtNaoESd3PK4\u002BxpstnReTCsAQE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-N7voM3g/shHAcrMBqvtNaoESd3PK4\u002BxpstnReTCsAQE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1485683"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022N7voM3g/shHAcrMBqvtNaoESd3PK4\u002BxpstnReTCsAQE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.tgteyju90f.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tgteyju90f"},{"Name":"integrity","Value":"sha256-rTqMV\u002BsfnioydTZg6PcboaQ4YcofQDNNgc0HyByiKX8="},{"Name":"label","Value":"_content/TabakGokturk/assets/fonts/nucleo-outline.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"178236"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022rTqMV\u002BsfnioydTZg6PcboaQ4YcofQDNNgc0HyByiKX8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-o5J2LqsCXPxjh4r9/XxwxOHwt3Nht6//LB9jdvUu29o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"582472"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022o5J2LqsCXPxjh4r9/XxwxOHwt3Nht6//LB9jdvUu29o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8dVTMlXD0MdI\u002B5TnPDCRD7S6A0/58NtLrnvPslDW5/g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"251316"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u00228dVTMlXD0MdI\u002B5TnPDCRD7S6A0/58NtLrnvPslDW5/g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/fonts/nucleo-outline.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\fonts\nucleo-outline.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rTqMV\u002BsfnioydTZg6PcboaQ4YcofQDNNgc0HyByiKX8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"178236"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022rTqMV\u002BsfnioydTZg6PcboaQ4YcofQDNNgc0HyByiKX8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/404.9le061txi2.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\404.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9le061txi2"},{"Name":"integrity","Value":"sha256-Izwm9E2sF9vz04wLz7npVQLtMaiP0EShZ2e1crhvSMw="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/404.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1196"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Izwm9E2sF9vz04wLz7npVQLtMaiP0EShZ2e1crhvSMw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/404.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\404.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Izwm9E2sF9vz04wLz7npVQLtMaiP0EShZ2e1crhvSMw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1196"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Izwm9E2sF9vz04wLz7npVQLtMaiP0EShZ2e1crhvSMw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about-bg-1.85i4ocx5x4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"85i4ocx5x4"},{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/about-bg-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about-bg-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-bg-1.7qwonqo30b.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7qwonqo30b"},{"Name":"integrity","Value":"sha256-UeWbSUTH\u002B\u002BLIBsVszFL/wn1vzIUlFpx\u002BYRfUKQ2Xr0c="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/about/about-bg-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"14324"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UeWbSUTH\u002B\u002BLIBsVszFL/wn1vzIUlFpx\u002BYRfUKQ2Xr0c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-bg-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UeWbSUTH\u002B\u002BLIBsVszFL/wn1vzIUlFpx\u002BYRfUKQ2Xr0c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"14324"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UeWbSUTH\u002B\u002BLIBsVszFL/wn1vzIUlFpx\u002BYRfUKQ2Xr0c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-img-1.66rquqdl5u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-img-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"66rquqdl5u"},{"Name":"integrity","Value":"sha256-8l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/about/about-img-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-img-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-img-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-img-2.66rquqdl5u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-img-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"66rquqdl5u"},{"Name":"integrity","Value":"sha256-8l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/about/about-img-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-img-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-img-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-img-3.66rquqdl5u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-img-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"66rquqdl5u"},{"Name":"integrity","Value":"sha256-8l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/about/about-img-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-img-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-img-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228l1r3v/vVL\u002BDyHzbVBbFNLQwJIzHvUsdaRUthDvuc3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-shape.3wmmhd1gal.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-shape.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3wmmhd1gal"},{"Name":"integrity","Value":"sha256-nc0QpEUvkRTAxS51AUzM/DAlDc/CH\u002BaZpg1Ab6jZX7Y="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/about/about-shape.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4688"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nc0QpEUvkRTAxS51AUzM/DAlDc/CH\u002BaZpg1Ab6jZX7Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/about/about-shape.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\about\about-shape.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nc0QpEUvkRTAxS51AUzM/DAlDc/CH\u002BaZpg1Ab6jZX7Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4688"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nc0QpEUvkRTAxS51AUzM/DAlDc/CH\u002BaZpg1Ab6jZX7Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/apple-store.gggta7970h.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\apple-store.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gggta7970h"},{"Name":"integrity","Value":"sha256-cPEQuKDgX8ivEdRP/Kqn4NUfV3IRuXCn3LbbOd1AmFk="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/apple-store.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6613"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022cPEQuKDgX8ivEdRP/Kqn4NUfV3IRuXCn3LbbOd1AmFk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/apple-store.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\apple-store.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cPEQuKDgX8ivEdRP/Kqn4NUfV3IRuXCn3LbbOd1AmFk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6613"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022cPEQuKDgX8ivEdRP/Kqn4NUfV3IRuXCn3LbbOd1AmFk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/author/m2-testimonial-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\author\m2-testimonial-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/author/m2-testimonial-1.zf05229o9b.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\author\m2-testimonial-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zf05229o9b"},{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/author/m2-testimonial-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/author/m2-testimonial-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\author\m2-testimonial-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9ELoOcIA4tRtCY5KExvx2EKz\u002Be4RWVi6MnxNRjrglN0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"391"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229ELoOcIA4tRtCY5KExvx2EKz\u002Be4RWVi6MnxNRjrglN0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/author/m2-testimonial-2.wp93fs5163.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\author\m2-testimonial-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wp93fs5163"},{"Name":"integrity","Value":"sha256-9ELoOcIA4tRtCY5KExvx2EKz\u002Be4RWVi6MnxNRjrglN0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/author/m2-testimonial-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"391"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229ELoOcIA4tRtCY5KExvx2EKz\u002Be4RWVi6MnxNRjrglN0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/background-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\background-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-R2Z0NP4hP8fesOjk3XhkHf91K4Xnf1kefUnNYse/xzY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"64795"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022R2Z0NP4hP8fesOjk3XhkHf91K4Xnf1kefUnNYse/xzY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/background-1.trlm6fq6xd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\background-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"trlm6fq6xd"},{"Name":"integrity","Value":"sha256-R2Z0NP4hP8fesOjk3XhkHf91K4Xnf1kefUnNYse/xzY="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/background-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"64795"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022R2Z0NP4hP8fesOjk3XhkHf91K4Xnf1kefUnNYse/xzY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/background-2.g0nstp3gof.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\background-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g0nstp3gof"},{"Name":"integrity","Value":"sha256-gCtl91tLPDjVoZstar5/KeL02y5rV0Zh594FxXsJNe4="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/background-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"196886"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gCtl91tLPDjVoZstar5/KeL02y5rV0Zh594FxXsJNe4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/background-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\background-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gCtl91tLPDjVoZstar5/KeL02y5rV0Zh594FxXsJNe4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"196886"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gCtl91tLPDjVoZstar5/KeL02y5rV0Zh594FxXsJNe4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pyu5MduVoLhgA9t8JHUHUaDsJQmzg9jk2yzeynn\u002BAXY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4424"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pyu5MduVoLhgA9t8JHUHUaDsJQmzg9jk2yzeynn\u002BAXY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-1.vy8pe1gn95.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vy8pe1gn95"},{"Name":"integrity","Value":"sha256-pyu5MduVoLhgA9t8JHUHUaDsJQmzg9jk2yzeynn\u002BAXY="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/banner/banner-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4424"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pyu5MduVoLhgA9t8JHUHUaDsJQmzg9jk2yzeynn\u002BAXY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9741"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-2.yhdq5uj70d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yhdq5uj70d"},{"Name":"integrity","Value":"sha256-KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/banner/banner-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"9741"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9741"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-3.yhdq5uj70d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yhdq5uj70d"},{"Name":"integrity","Value":"sha256-KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/banner/banner-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"9741"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KuxHZe59W2jPGUJMJtsDL8uSagv3kmt4lVAHvxRwUfQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HvJGLq6Tn9bxJtuyYBoFbgvQhframssegvVPGAfZ6vs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"10063"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022HvJGLq6Tn9bxJtuyYBoFbgvQhframssegvVPGAfZ6vs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-4.ygsrhdkogx.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ygsrhdkogx"},{"Name":"integrity","Value":"sha256-HvJGLq6Tn9bxJtuyYBoFbgvQhframssegvVPGAfZ6vs="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/banner/banner-4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"10063"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022HvJGLq6Tn9bxJtuyYBoFbgvQhframssegvVPGAfZ6vs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NIR1vqFwDePqrujSUHT5\u002Btdtq0TisX6Y7i6G6s/goEg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1857"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022NIR1vqFwDePqrujSUHT5\u002Btdtq0TisX6Y7i6G6s/goEg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-5.wbisc9ax2p.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wbisc9ax2p"},{"Name":"integrity","Value":"sha256-NIR1vqFwDePqrujSUHT5\u002Btdtq0TisX6Y7i6G6s/goEg="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/banner/banner-5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1857"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022NIR1vqFwDePqrujSUHT5\u002Btdtq0TisX6Y7i6G6s/goEg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdyPaeK8pJbeqYShxd6q7NpqSA2Z4gqhlD93cbMA40M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdyPaeK8pJbeqYShxd6q7NpqSA2Z4gqhlD93cbMA40M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/banner/banner-6.v9uenjfk7a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\banner\banner-6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v9uenjfk7a"},{"Name":"integrity","Value":"sha256-PdyPaeK8pJbeqYShxd6q7NpqSA2Z4gqhlD93cbMA40M="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/banner/banner-6.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdyPaeK8pJbeqYShxd6q7NpqSA2Z4gqhlD93cbMA40M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog-page-header.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog-page-header.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cIoMpc/5mTF/3kOneGHOfF5emLscbqqQLlXTkUU8\u002B3c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13280"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022cIoMpc/5mTF/3kOneGHOfF5emLscbqqQLlXTkUU8\u002B3c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog-page-header.o2hkjl5l92.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog-page-header.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o2hkjl5l92"},{"Name":"integrity","Value":"sha256-cIoMpc/5mTF/3kOneGHOfF5emLscbqqQLlXTkUU8\u002B3c="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog-page-header.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"13280"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022cIoMpc/5mTF/3kOneGHOfF5emLscbqqQLlXTkUU8\u002B3c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5897"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-1.njwlq7p0lf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"njwlq7p0lf"},{"Name":"integrity","Value":"sha256-A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/blog-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5897"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5897"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-2.njwlq7p0lf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"njwlq7p0lf"},{"Name":"integrity","Value":"sha256-A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/blog-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5897"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A/4NXG21RJd5geyXPk/zlUoFkwP9RSbC003BcmxsxW8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-3.6a58s6pf1u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6a58s6pf1u"},{"Name":"integrity","Value":"sha256-Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/blog-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5673"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5673"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-4.6a58s6pf1u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6a58s6pf1u"},{"Name":"integrity","Value":"sha256-Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/blog-4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5673"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5673"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-5.6a58s6pf1u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6a58s6pf1u"},{"Name":"integrity","Value":"sha256-Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/blog-5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5673"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/blog-5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\blog-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5673"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qm8VDavk/JOlhSSUDnYgPcz/rWJtUIwXGmp2VtG0cWE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/content-single-post-1a.gahl8n8td4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\content-single-post-1a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gahl8n8td4"},{"Name":"integrity","Value":"sha256-ODGsxDfidh3IgcMU11ESX/\u002BNSHeiBqWMXo/AB50EU6U="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/content-single-post-1a.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5719"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ODGsxDfidh3IgcMU11ESX/\u002BNSHeiBqWMXo/AB50EU6U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/content-single-post-1a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\content-single-post-1a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ODGsxDfidh3IgcMU11ESX/\u002BNSHeiBqWMXo/AB50EU6U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5719"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ODGsxDfidh3IgcMU11ESX/\u002BNSHeiBqWMXo/AB50EU6U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/home-1/blog-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\home-1\blog-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4460"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/home-1/blog-1.wogp9vl3py.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\home-1\blog-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wogp9vl3py"},{"Name":"integrity","Value":"sha256-QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/home-1/blog-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4460"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/home-1/blog-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\home-1\blog-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4460"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/blog/home-1/blog-2.wogp9vl3py.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\blog\home-1\blog-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wogp9vl3py"},{"Name":"integrity","Value":"sha256-QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/blog/home-1/blog-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4460"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022QEolglkz2nfhLb4CZaK3y3AvuqDUt70V3Ok/jEoK76c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/booking.86ifrgazx8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\booking.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"86ifrgazx8"},{"Name":"integrity","Value":"sha256-wKSD54gR0wZm6TpiDBXOo3a7w4OfXIk3jHTIz0LqIDM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/booking.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5916"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wKSD54gR0wZm6TpiDBXOo3a7w4OfXIk3jHTIz0LqIDM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/booking.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\booking.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wKSD54gR0wZm6TpiDBXOo3a7w4OfXIk3jHTIz0LqIDM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5916"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wKSD54gR0wZm6TpiDBXOo3a7w4OfXIk3jHTIz0LqIDM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-1.ewczeni0xd.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-1.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ewczeni0xd"},{"Name":"integrity","Value":"sha256-8ilnV3GPYG/ge6kb094FFBekKvGraxZGCXv1arIjs3o="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-1.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"16255"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00228ilnV3GPYG/ge6kb094FFBekKvGraxZGCXv1arIjs3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-1.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-1.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8ilnV3GPYG/ge6kb094FFBekKvGraxZGCXv1arIjs3o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16255"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00228ilnV3GPYG/ge6kb094FFBekKvGraxZGCXv1arIjs3o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wiJmpwI05jcgLXt7pLhi6Q8Fmbd0UZAYoMAE9fW1gKc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3607"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wiJmpwI05jcgLXt7pLhi6Q8Fmbd0UZAYoMAE9fW1gKc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-1.ywe11ws1v5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ywe11ws1v5"},{"Name":"integrity","Value":"sha256-wiJmpwI05jcgLXt7pLhi6Q8Fmbd0UZAYoMAE9fW1gKc="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-2-1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3607"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wiJmpwI05jcgLXt7pLhi6Q8Fmbd0UZAYoMAE9fW1gKc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-2.2dyiyukz26.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2dyiyukz26"},{"Name":"integrity","Value":"sha256-iueBHCt\u002BSqqbHcbC53H7eAd\u002BVqmQKGi8T8D9eaE\u002BrH4="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-2-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3600"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iueBHCt\u002BSqqbHcbC53H7eAd\u002BVqmQKGi8T8D9eaE\u002BrH4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iueBHCt\u002BSqqbHcbC53H7eAd\u002BVqmQKGi8T8D9eaE\u002BrH4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3600"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iueBHCt\u002BSqqbHcbC53H7eAd\u002BVqmQKGi8T8D9eaE\u002BrH4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-3.31y0ehn2pc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"31y0ehn2pc"},{"Name":"integrity","Value":"sha256-R048pV5ik8xb\u002B4XNEBXxEVo/VRWg8pRETEhHP0ZtJCw="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-2-3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5577"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022R048pV5ik8xb\u002B4XNEBXxEVo/VRWg8pRETEhHP0ZtJCw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-R048pV5ik8xb\u002B4XNEBXxEVo/VRWg8pRETEhHP0ZtJCw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5577"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022R048pV5ik8xb\u002B4XNEBXxEVo/VRWg8pRETEhHP0ZtJCw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-4.dworoun4hp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dworoun4hp"},{"Name":"integrity","Value":"sha256-M4ntC53yrsbgpjwWXCSnKYyMYn0me6deoKG\u002B/qKXkcE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-2-4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2943"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022M4ntC53yrsbgpjwWXCSnKYyMYn0me6deoKG\u002B/qKXkcE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2-4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2-4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-M4ntC53yrsbgpjwWXCSnKYyMYn0me6deoKG\u002B/qKXkcE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2943"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022M4ntC53yrsbgpjwWXCSnKYyMYn0me6deoKG\u002B/qKXkcE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2.90mjlcsrjm.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"90mjlcsrjm"},{"Name":"integrity","Value":"sha256-16SGKMHk3b/KyCVSpRHb11E2uKtsZ6f\u002BX1quBCAOyCM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-2.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"15348"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u002216SGKMHk3b/KyCVSpRHb11E2uKtsZ6f\u002BX1quBCAOyCM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-2.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-2.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-16SGKMHk3b/KyCVSpRHb11E2uKtsZ6f\u002BX1quBCAOyCM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"15348"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u002216SGKMHk3b/KyCVSpRHb11E2uKtsZ6f\u002BX1quBCAOyCM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-3.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-3.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nyusKW2RTr/IJPqmjk0btPnyCoYdNmsjKMo\u002BDoD3e8E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3883"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022nyusKW2RTr/IJPqmjk0btPnyCoYdNmsjKMo\u002BDoD3e8E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-3.x43i0lg2uf.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-3.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x43i0lg2uf"},{"Name":"integrity","Value":"sha256-nyusKW2RTr/IJPqmjk0btPnyCoYdNmsjKMo\u002BDoD3e8E="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-3.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3883"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022nyusKW2RTr/IJPqmjk0btPnyCoYdNmsjKMo\u002BDoD3e8E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-4.mx9jznhfw4.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-4.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mx9jznhfw4"},{"Name":"integrity","Value":"sha256-9reGqRj9Hia3X00QsQ54w6AGqXfaEfPiBz0Bjp//NW4="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-4.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"18102"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00229reGqRj9Hia3X00QsQ54w6AGqXfaEfPiBz0Bjp//NW4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-4.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-4.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9reGqRj9Hia3X00QsQ54w6AGqXfaEfPiBz0Bjp//NW4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"18102"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00229reGqRj9Hia3X00QsQ54w6AGqXfaEfPiBz0Bjp//NW4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-5.7kqdm5lio4.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-5.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7kqdm5lio4"},{"Name":"integrity","Value":"sha256-t6HnV9UTo0lc2BkvagwoSdb2CykeEbU85pUzk6Wp9ZU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/brand/brand-5.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"9661"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022t6HnV9UTo0lc2BkvagwoSdb2CykeEbU85pUzk6Wp9ZU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/brand/brand-5.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\brand\brand-5.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-t6HnV9UTo0lc2BkvagwoSdb2CykeEbU85pUzk6Wp9ZU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9661"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022t6HnV9UTo0lc2BkvagwoSdb2CykeEbU85pUzk6Wp9ZU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/cart-empty.1yx6k7k7e8.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\cart-empty.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1yx6k7k7e8"},{"Name":"integrity","Value":"sha256-p/5wIQjHDzfbyT1ylCIt4qf86elsE6Pi5vR6oddJeI0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/cart-empty.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1229"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022p/5wIQjHDzfbyT1ylCIt4qf86elsE6Pi5vR6oddJeI0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/cart-empty.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\cart-empty.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p/5wIQjHDzfbyT1ylCIt4qf86elsE6Pi5vR6oddJeI0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1229"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022p/5wIQjHDzfbyT1ylCIt4qf86elsE6Pi5vR6oddJeI0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-1.ha9588r2dx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ha9588r2dx"},{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/category/category-1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-2.ha9588r2dx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ha9588r2dx"},{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/category/category-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-3.ha9588r2dx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ha9588r2dx"},{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/category/category-3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-4.ha9588r2dx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ha9588r2dx"},{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/category/category-4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/category/category-4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\category\category-4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2188"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SRYqyZFvcHktL7t35uQzyGFN7hEVPGwc86bEFMYdO/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/coming-soon.8htx6im90k.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\coming-soon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8htx6im90k"},{"Name":"integrity","Value":"sha256-hP4icNrMbt30jw/aW07c8xuytUAaKeoGIe7h012MwAg="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/coming-soon.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3792"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hP4icNrMbt30jw/aW07c8xuytUAaKeoGIe7h012MwAg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/coming-soon.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\coming-soon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hP4icNrMbt30jw/aW07c8xuytUAaKeoGIe7h012MwAg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3792"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hP4icNrMbt30jw/aW07c8xuytUAaKeoGIe7h012MwAg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/content-single-product-image-1.8gaybjqhy3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\content-single-product-image-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8gaybjqhy3"},{"Name":"integrity","Value":"sha256-aGA0g3tGG/RVB8uSJ78Qt\u002BvfbVBMk7dOcDkKam0d5ao="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/content-single-product-image-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2657"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022aGA0g3tGG/RVB8uSJ78Qt\u002BvfbVBMk7dOcDkKam0d5ao=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/content-single-product-image-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\content-single-product-image-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aGA0g3tGG/RVB8uSJ78Qt\u002BvfbVBMk7dOcDkKam0d5ao="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2657"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022aGA0g3tGG/RVB8uSJ78Qt\u002BvfbVBMk7dOcDkKam0d5ao=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/content-single-product-image-2.0svztg3r50.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\content-single-product-image-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0svztg3r50"},{"Name":"integrity","Value":"sha256-oPgNgXzSo1QzBVxaMelHhCsa4YNsoOxorQdQLVV7pFU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/content-single-product-image-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2841"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oPgNgXzSo1QzBVxaMelHhCsa4YNsoOxorQdQLVV7pFU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/content-single-product-image-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\content-single-product-image-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oPgNgXzSo1QzBVxaMelHhCsa4YNsoOxorQdQLVV7pFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2841"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oPgNgXzSo1QzBVxaMelHhCsa4YNsoOxorQdQLVV7pFU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/cta-1.l3u0qcrrvk.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\cta-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l3u0qcrrvk"},{"Name":"integrity","Value":"sha256-n\u002B1uk34BPT28/n6f21rPG9R4erQARmFVGsKd773XLSI="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/cta-1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"8778"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022n\u002B1uk34BPT28/n6f21rPG9R4erQARmFVGsKd773XLSI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/cta-1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\cta-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-n\u002B1uk34BPT28/n6f21rPG9R4erQARmFVGsKd773XLSI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"8778"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022n\u002B1uk34BPT28/n6f21rPG9R4erQARmFVGsKd773XLSI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/cta-2.2kijtb1gao.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\cta-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2kijtb1gao"},{"Name":"integrity","Value":"sha256-sj2npOicujo50eAzZHC6cB2LnSv1Xadmbc9fslWW93M="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/cta-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5725"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sj2npOicujo50eAzZHC6cB2LnSv1Xadmbc9fslWW93M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/cta-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\cta-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sj2npOicujo50eAzZHC6cB2LnSv1Xadmbc9fslWW93M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5725"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sj2npOicujo50eAzZHC6cB2LnSv1Xadmbc9fslWW93M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/dukkan-dis-cekim.bu8h9v06m8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\dukkan-dis-cekim.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bu8h9v06m8"},{"Name":"integrity","Value":"sha256-A0yIEnF1ef0Rst3Wrq6OC6J5KwslYffK8ay9LTPVIaI="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/dukkan-dis-cekim.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"329865"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A0yIEnF1ef0Rst3Wrq6OC6J5KwslYffK8ay9LTPVIaI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 18:48:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/dukkan-dis-cekim.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\dukkan-dis-cekim.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A0yIEnF1ef0Rst3Wrq6OC6J5KwslYffK8ay9LTPVIaI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"329865"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A0yIEnF1ef0Rst3Wrq6OC6J5KwslYffK8ay9LTPVIaI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 18:48:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/facebook.57k5oea3vo.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\facebook.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"57k5oea3vo"},{"Name":"integrity","Value":"sha256-f5p/IoySSyik\u002BdJNnC0Xz5rzVsUoPFD4S1W4t9n/epM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/facebook.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"305"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022f5p/IoySSyik\u002BdJNnC0Xz5rzVsUoPFD4S1W4t9n/epM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/facebook.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\facebook.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-f5p/IoySSyik\u002BdJNnC0Xz5rzVsUoPFD4S1W4t9n/epM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"305"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022f5p/IoySSyik\u002BdJNnC0Xz5rzVsUoPFD4S1W4t9n/epM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/favicon.owpj62vszj.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\favicon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"owpj62vszj"},{"Name":"integrity","Value":"sha256-6AdOyud2Glczm8P3r716DAaOgnABOqFuYsJGc7CwYzM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/favicon.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"447"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00226AdOyud2Glczm8P3r716DAaOgnABOqFuYsJGc7CwYzM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/favicon.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\favicon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6AdOyud2Glczm8P3r716DAaOgnABOqFuYsJGc7CwYzM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"447"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00226AdOyud2Glczm8P3r716DAaOgnABOqFuYsJGc7CwYzM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/footer-payment-1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\footer-payment-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4sWlYroOI5sf4z9wpb7idtiNb59c34DTEo5zDgm5mg0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7530"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224sWlYroOI5sf4z9wpb7idtiNb59c34DTEo5zDgm5mg0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/footer-payment-1.udblxi9zrl.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\footer-payment-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"udblxi9zrl"},{"Name":"integrity","Value":"sha256-4sWlYroOI5sf4z9wpb7idtiNb59c34DTEo5zDgm5mg0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/footer-payment-1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7530"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224sWlYroOI5sf4z9wpb7idtiNb59c34DTEo5zDgm5mg0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/footer-payment-white.jf2vc6glal.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\footer-payment-white.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jf2vc6glal"},{"Name":"integrity","Value":"sha256-D8IDGMqACAcpiKMyH/OLP3ZQBNG6Qu/sT4\u002B4Zwy8Gqw="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/footer-payment-white.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"14388"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022D8IDGMqACAcpiKMyH/OLP3ZQBNG6Qu/sT4\u002B4Zwy8Gqw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/footer-payment-white.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\footer-payment-white.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-D8IDGMqACAcpiKMyH/OLP3ZQBNG6Qu/sT4\u002B4Zwy8Gqw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"14388"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022D8IDGMqACAcpiKMyH/OLP3ZQBNG6Qu/sT4\u002B4Zwy8Gqw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/footer-payment.g60uv3108o.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\footer-payment.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g60uv3108o"},{"Name":"integrity","Value":"sha256-mCiZ5NXq0v2wEegrS64yoWMwqDGyKSxzjar\u002B0/VA5cM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/footer-payment.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"9683"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mCiZ5NXq0v2wEegrS64yoWMwqDGyKSxzjar\u002B0/VA5cM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/footer-payment.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\footer-payment.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mCiZ5NXq0v2wEegrS64yoWMwqDGyKSxzjar\u002B0/VA5cM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9683"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mCiZ5NXq0v2wEegrS64yoWMwqDGyKSxzjar\u002B0/VA5cM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/gallery/gallery-01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\gallery\gallery-01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xr\u002BzBtwHXn/vY85UNwfO3jC\u002BS/Jx4\u002B8t2KNhETPclRk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6790"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022xr\u002BzBtwHXn/vY85UNwfO3jC\u002BS/Jx4\u002B8t2KNhETPclRk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/gallery/gallery-01.uvg7nmeede.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\gallery\gallery-01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uvg7nmeede"},{"Name":"integrity","Value":"sha256-xr\u002BzBtwHXn/vY85UNwfO3jC\u002BS/Jx4\u002B8t2KNhETPclRk="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/gallery/gallery-01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6790"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022xr\u002BzBtwHXn/vY85UNwfO3jC\u002BS/Jx4\u002B8t2KNhETPclRk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/gallery/gallery-02.75t8aeyohs.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\gallery\gallery-02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"75t8aeyohs"},{"Name":"integrity","Value":"sha256-dVjdcS/ddjzyNKpqyzehZWzzbA2OESbXSGwNhnhwxts="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/gallery/gallery-02.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6849"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022dVjdcS/ddjzyNKpqyzehZWzzbA2OESbXSGwNhnhwxts=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/gallery/gallery-02.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\gallery\gallery-02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dVjdcS/ddjzyNKpqyzehZWzzbA2OESbXSGwNhnhwxts="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6849"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022dVjdcS/ddjzyNKpqyzehZWzzbA2OESbXSGwNhnhwxts=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/gallery/gallery-03.3rpner0uvj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\gallery\gallery-03.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3rpner0uvj"},{"Name":"integrity","Value":"sha256-4YMC/wI\u002Be9K26USgH1uYGeBRKpsNpAowjWdzQifINL0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/gallery/gallery-03.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7515"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00224YMC/wI\u002Be9K26USgH1uYGeBRKpsNpAowjWdzQifINL0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/gallery/gallery-03.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\gallery\gallery-03.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4YMC/wI\u002Be9K26USgH1uYGeBRKpsNpAowjWdzQifINL0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7515"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00224YMC/wI\u002Be9K26USgH1uYGeBRKpsNpAowjWdzQifINL0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/google-store.0f3caz1se8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\google-store.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0f3caz1se8"},{"Name":"integrity","Value":"sha256-tJ1GLfaBa\u002BC1IJssdB\u002BiaBdA2Eun4OhF0PSg2yoC30w="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/google-store.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5768"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022tJ1GLfaBa\u002BC1IJssdB\u002BiaBdA2Eun4OhF0PSg2yoC30w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/google-store.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\google-store.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tJ1GLfaBa\u002BC1IJssdB\u002BiaBdA2Eun4OhF0PSg2yoC30w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5768"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022tJ1GLfaBa\u002BC1IJssdB\u002BiaBdA2Eun4OhF0PSg2yoC30w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/google.hmk58abrk8.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\google.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hmk58abrk8"},{"Name":"integrity","Value":"sha256-Vyga0IKcY9vhdSAU6KkBmn/dPcXlZudt1/JtQ3fZO2Q="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/google.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"283"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Vyga0IKcY9vhdSAU6KkBmn/dPcXlZudt1/JtQ3fZO2Q=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/google.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\google.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Vyga0IKcY9vhdSAU6KkBmn/dPcXlZudt1/JtQ3fZO2Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"283"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Vyga0IKcY9vhdSAU6KkBmn/dPcXlZudt1/JtQ3fZO2Q=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/coupon.r710pkj3lt.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\coupon.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r710pkj3lt"},{"Name":"integrity","Value":"sha256-J3\u002B39P5bFThEyU2NfeHWMLBP\u002B6e8uJ3LZ68wKN4gJTw="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/coupon.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"354"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022J3\u002B39P5bFThEyU2NfeHWMLBP\u002B6e8uJ3LZ68wKN4gJTw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/coupon.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\coupon.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-J3\u002B39P5bFThEyU2NfeHWMLBP\u002B6e8uJ3LZ68wKN4gJTw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"354"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022J3\u002B39P5bFThEyU2NfeHWMLBP\u002B6e8uJ3LZ68wKN4gJTw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-1.eastxm1m5h.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-1.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eastxm1m5h"},{"Name":"integrity","Value":"sha256-imIhgro2PZqWBcpqpuYRmG\u002Bo5GRi2UETHMIV52d72ko="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/icon-1.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"306"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022imIhgro2PZqWBcpqpuYRmG\u002Bo5GRi2UETHMIV52d72ko=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-1.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-1.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-imIhgro2PZqWBcpqpuYRmG\u002Bo5GRi2UETHMIV52d72ko="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"306"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022imIhgro2PZqWBcpqpuYRmG\u002Bo5GRi2UETHMIV52d72ko=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-2.9dyrwevycu.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-2.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9dyrwevycu"},{"Name":"integrity","Value":"sha256-vW4IRYsPQNpJy92fvgGnBrN0x\u002BrUbFBld/COtTDoajA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/icon-2.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"402"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022vW4IRYsPQNpJy92fvgGnBrN0x\u002BrUbFBld/COtTDoajA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-2.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-2.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vW4IRYsPQNpJy92fvgGnBrN0x\u002BrUbFBld/COtTDoajA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"402"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022vW4IRYsPQNpJy92fvgGnBrN0x\u002BrUbFBld/COtTDoajA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-3.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-3.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Wt1Q0K8CRsvvkzXtOIRm1aglspHj8hpWlMHvys7lTaI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"360"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Wt1Q0K8CRsvvkzXtOIRm1aglspHj8hpWlMHvys7lTaI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-3.uuxhhzobce.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-3.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uuxhhzobce"},{"Name":"integrity","Value":"sha256-Wt1Q0K8CRsvvkzXtOIRm1aglspHj8hpWlMHvys7lTaI="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/icon-3.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"360"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022Wt1Q0K8CRsvvkzXtOIRm1aglspHj8hpWlMHvys7lTaI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-4.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-4.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rfPc0im/tgVCHEOUuSlRGup4MDXgAZwxf2zi4KHH3t4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"588"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022rfPc0im/tgVCHEOUuSlRGup4MDXgAZwxf2zi4KHH3t4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/icon-4.tkso56duwb.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\icon-4.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tkso56duwb"},{"Name":"integrity","Value":"sha256-rfPc0im/tgVCHEOUuSlRGup4MDXgAZwxf2zi4KHH3t4="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/icon-4.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"588"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022rfPc0im/tgVCHEOUuSlRGup4MDXgAZwxf2zi4KHH3t4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/quote-icon.lye6xtr67l.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\quote-icon.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lye6xtr67l"},{"Name":"integrity","Value":"sha256-PRb\u002B3bCgAnAkmecgkZqHoBFT97OfmoRuovd2jYZh\u002BcU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/quote-icon.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"572"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022PRb\u002B3bCgAnAkmecgkZqHoBFT97OfmoRuovd2jYZh\u002BcU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/quote-icon.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\quote-icon.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PRb\u002B3bCgAnAkmecgkZqHoBFT97OfmoRuovd2jYZh\u002BcU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"572"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022PRb\u002B3bCgAnAkmecgkZqHoBFT97OfmoRuovd2jYZh\u002BcU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/TabakLogoGokturk.m5q57obi6t.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\TabakLogoGokturk.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"m5q57obi6t"},{"Name":"integrity","Value":"sha256-qgpXKbQHxn9j2uJuRakrDXKqg9Cvyli/GF6lPu\u002BtL6s="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/icon/TabakLogoGokturk.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"82175"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qgpXKbQHxn9j2uJuRakrDXKqg9Cvyli/GF6lPu\u002BtL6s=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Jul 2025 22:21:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/icon/TabakLogoGokturk.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\icon\TabakLogoGokturk.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qgpXKbQHxn9j2uJuRakrDXKqg9Cvyli/GF6lPu\u002BtL6s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"82175"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qgpXKbQHxn9j2uJuRakrDXKqg9Cvyli/GF6lPu\u002BtL6s=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Jul 2025 22:21:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6138"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-1.tkl9s1v79n.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tkl9s1v79n"},{"Name":"integrity","Value":"sha256-gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/about-instagram-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6138"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-2.1gxuus8af5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1gxuus8af5"},{"Name":"integrity","Value":"sha256-0dJatKsY41ti0rxOtSlhLsu87aXFqPrQTYhDoGDJocQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/about-instagram-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5205"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00220dJatKsY41ti0rxOtSlhLsu87aXFqPrQTYhDoGDJocQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0dJatKsY41ti0rxOtSlhLsu87aXFqPrQTYhDoGDJocQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5205"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00220dJatKsY41ti0rxOtSlhLsu87aXFqPrQTYhDoGDJocQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6138"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-3.tkl9s1v79n.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tkl9s1v79n"},{"Name":"integrity","Value":"sha256-gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/about-instagram-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6138"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gzq1254Qd/6x5N2e78oiVYk4EEm8iF8c7r6FEsmepdM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-4.dqfl1y2ai4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dqfl1y2ai4"},{"Name":"integrity","Value":"sha256-G7SuKCMYnA/DAV5oBcW10JWNb3YgE6yuHJDuCJbJEyI="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/about-instagram-4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5789"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022G7SuKCMYnA/DAV5oBcW10JWNb3YgE6yuHJDuCJbJEyI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/about-instagram-4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\about-instagram-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G7SuKCMYnA/DAV5oBcW10JWNb3YgE6yuHJDuCJbJEyI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5789"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022G7SuKCMYnA/DAV5oBcW10JWNb3YgE6yuHJDuCJbJEyI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-1.iygdg6dlt5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iygdg6dlt5"},{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/instagram-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-2.iygdg6dlt5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iygdg6dlt5"},{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/instagram-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-3.iygdg6dlt5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iygdg6dlt5"},{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/instagram-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-4.iygdg6dlt5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iygdg6dlt5"},{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/instagram-4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-5.iygdg6dlt5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iygdg6dlt5"},{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/instagram-5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-6.iygdg6dlt5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iygdg6dlt5"},{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/instagram/instagram-6.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/instagram/instagram-6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\instagram\instagram-6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225nAsQfiXkW\u002BhfCnaGTDEuL54U96WLVGVZr5CGtTwrKU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/logo-white.fyjac1vw6q.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\logo-white.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fyjac1vw6q"},{"Name":"integrity","Value":"sha256-h6N\u002BqJpZEVdms6qgaBVRU3BpV6tXpF\u002BvQovZX1ivP2w="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/logo-white.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1216"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022h6N\u002BqJpZEVdms6qgaBVRU3BpV6tXpF\u002BvQovZX1ivP2w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/logo-white.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\logo-white.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-h6N\u002BqJpZEVdms6qgaBVRU3BpV6tXpF\u002BvQovZX1ivP2w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1216"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022h6N\u002BqJpZEVdms6qgaBVRU3BpV6tXpF\u002BvQovZX1ivP2w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nyEqZWYYu2posQAISmrn4fZslMtzmHTlPuyJREqBX1k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1226"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nyEqZWYYu2posQAISmrn4fZslMtzmHTlPuyJREqBX1k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/logo.rl9s6zhnc1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rl9s6zhnc1"},{"Name":"integrity","Value":"sha256-nyEqZWYYu2posQAISmrn4fZslMtzmHTlPuyJREqBX1k="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1226"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nyEqZWYYu2posQAISmrn4fZslMtzmHTlPuyJREqBX1k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/megamenu-fashion-01.8zchravk82.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\megamenu-fashion-01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8zchravk82"},{"Name":"integrity","Value":"sha256-DFHVhg6Eg2\u002B2TsxM59T8\u002B2EfMhmmY1vjRnDpoNwZ5YE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/megamenu-fashion-01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2209"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022DFHVhg6Eg2\u002B2TsxM59T8\u002B2EfMhmmY1vjRnDpoNwZ5YE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/megamenu-fashion-01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\megamenu-fashion-01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DFHVhg6Eg2\u002B2TsxM59T8\u002B2EfMhmmY1vjRnDpoNwZ5YE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2209"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022DFHVhg6Eg2\u002B2TsxM59T8\u002B2EfMhmmY1vjRnDpoNwZ5YE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/newsletter-bg-1.e2qxx1h96l.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\newsletter-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"e2qxx1h96l"},{"Name":"integrity","Value":"sha256-NrdqJ4BH8j5NuSPyoO0fx6Tag5Kpwj7c8MjyO6clWoc="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/newsletter-bg-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"10394"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022NrdqJ4BH8j5NuSPyoO0fx6Tag5Kpwj7c8MjyO6clWoc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/newsletter-bg-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\newsletter-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NrdqJ4BH8j5NuSPyoO0fx6Tag5Kpwj7c8MjyO6clWoc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"10394"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022NrdqJ4BH8j5NuSPyoO0fx6Tag5Kpwj7c8MjyO6clWoc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/newsletter-bg-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\newsletter-bg-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AH3Pv4jvqmVC8DMLsKx1Rh9jmTP9QmJqc4CTY7Wz0oM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"10483"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AH3Pv4jvqmVC8DMLsKx1Rh9jmTP9QmJqc4CTY7Wz0oM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/newsletter-bg-2.k3gyz5csxf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\newsletter-bg-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k3gyz5csxf"},{"Name":"integrity","Value":"sha256-AH3Pv4jvqmVC8DMLsKx1Rh9jmTP9QmJqc4CTY7Wz0oM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/newsletter-bg-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"10483"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AH3Pv4jvqmVC8DMLsKx1Rh9jmTP9QmJqc4CTY7Wz0oM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/order-tracking.ch3d6v6tt7.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\order-tracking.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ch3d6v6tt7"},{"Name":"integrity","Value":"sha256-4JSt22G0aP9tQwCD4EBBoMVSUB2/Uz5AJjRi/UIgEAo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/order-tracking.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"14427"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00224JSt22G0aP9tQwCD4EBBoMVSUB2/Uz5AJjRi/UIgEAo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/order-tracking.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\order-tracking.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4JSt22G0aP9tQwCD4EBBoMVSUB2/Uz5AJjRi/UIgEAo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"14427"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00224JSt22G0aP9tQwCD4EBBoMVSUB2/Uz5AJjRi/UIgEAo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-1.vxqvlawf5d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxqvlawf5d"},{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/our-team/team-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-2.vxqvlawf5d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxqvlawf5d"},{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/our-team/team-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-3.vxqvlawf5d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxqvlawf5d"},{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/our-team/team-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-4.vxqvlawf5d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxqvlawf5d"},{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/our-team/team-4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-5.vxqvlawf5d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxqvlawf5d"},{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/our-team/team-5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/our-team/team-6.vxqvlawf5d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\our-team\team-6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxqvlawf5d"},{"Name":"integrity","Value":"sha256-I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/our-team/team-6.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4173"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022I/dr8TddakULzi2UoVrG\u002Bm6zMUFzYILHKqUUXOC6GGM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/placeholder.g58ovx7392.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\placeholder.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g58ovx7392"},{"Name":"integrity","Value":"sha256-RBLQQ\u002BS0pvgJIvDU8tK0obHEy7YcsuN9Gaj5Knz38fE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/placeholder.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2861"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RBLQQ\u002BS0pvgJIvDU8tK0obHEy7YcsuN9Gaj5Knz38fE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/placeholder.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\placeholder.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RBLQQ\u002BS0pvgJIvDU8tK0obHEy7YcsuN9Gaj5Knz38fE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2861"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RBLQQ\u002BS0pvgJIvDU8tK0obHEy7YcsuN9Gaj5Knz38fE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/play.aes4syc72x.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\play.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"aes4syc72x"},{"Name":"integrity","Value":"sha256-wkyJhufvzI0QFwBeezES42zN2YnpSV3mD2h\u002Bz15EtFU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/play.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"794"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022wkyJhufvzI0QFwBeezES42zN2YnpSV3mD2h\u002Bz15EtFU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/play.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\play.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wkyJhufvzI0QFwBeezES42zN2YnpSV3mD2h\u002Bz15EtFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"794"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022wkyJhufvzI0QFwBeezES42zN2YnpSV3mD2h\u002Bz15EtFU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/popup-bg.ijft5dvuk8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\popup-bg.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ijft5dvuk8"},{"Name":"integrity","Value":"sha256-EiO\u002B1QgQrrPg4xY1sHfguY2Qb7IQKB8o9gq/1FM6ayM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/popup-bg.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2753"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EiO\u002B1QgQrrPg4xY1sHfguY2Qb7IQKB8o9gq/1FM6ayM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/popup-bg.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\popup-bg.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EiO\u002B1QgQrrPg4xY1sHfguY2Qb7IQKB8o9gq/1FM6ayM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2753"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EiO\u002B1QgQrrPg4xY1sHfguY2Qb7IQKB8o9gq/1FM6ayM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-01.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-02.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-02.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-02.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-03.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-03.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-03.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-03.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-03.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-04.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-04.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-04.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-04.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-04.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-05.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-05.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-05.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-05.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-05.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-06.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-06.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-06.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-06.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-06.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-07.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-07.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-07.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-07.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-07.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-08.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-08.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-08.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-08.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-08.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-09.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-09.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-09.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-09.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-09.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-10.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-10.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-10.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-10.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-10.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-11.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-11.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-11.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-11.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-11.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-12.3n433r1br0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-12.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/candles/product-12.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/candles/product-12.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\candles\product-12.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/cheese-board-1.4ptrhl9kx5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\cheese-board-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4ptrhl9kx5"},{"Name":"integrity","Value":"sha256-6Hvfow0BeQ\u002B9FMQF2nS7p6by\u002Bc8Zq/R50B31UbU7pOs="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/cheese-board-1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"390861"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00226Hvfow0BeQ\u002B9FMQF2nS7p6by\u002Bc8Zq/R50B31UbU7pOs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 01:18:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/cheese-board-1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\cheese-board-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6Hvfow0BeQ\u002B9FMQF2nS7p6by\u002Bc8Zq/R50B31UbU7pOs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"390861"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00226Hvfow0BeQ\u002B9FMQF2nS7p6by\u002Bc8Zq/R50B31UbU7pOs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 01:18:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/index-dummy-product.pcceb30s2v.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\index-dummy-product.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pcceb30s2v"},{"Name":"integrity","Value":"sha256-CQ3BOnA\u002BZr4qZ8g4KdmwM0BydHdhD\u002Bmg40qDajeyi0o="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/index-dummy-product.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"192724"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CQ3BOnA\u002BZr4qZ8g4KdmwM0BydHdhD\u002Bmg40qDajeyi0o=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 01:58:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/index-dummy-product.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\index-dummy-product.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CQ3BOnA\u002BZr4qZ8g4KdmwM0BydHdhD\u002Bmg40qDajeyi0o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"192724"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CQ3BOnA\u002BZr4qZ8g4KdmwM0BydHdhD\u002Bmg40qDajeyi0o=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 01:58:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-bg-1.8ns2j7s5w2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8ns2j7s5w2"},{"Name":"integrity","Value":"sha256-hGwI1yQzwfZX0ILor5QG3cCKnIXOiJUcpvSqi0p\u002BANs="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/popular-bg-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3599"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022hGwI1yQzwfZX0ILor5QG3cCKnIXOiJUcpvSqi0p\u002BANs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-bg-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-bg-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hGwI1yQzwfZX0ILor5QG3cCKnIXOiJUcpvSqi0p\u002BANs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3599"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022hGwI1yQzwfZX0ILor5QG3cCKnIXOiJUcpvSqi0p\u002BANs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-bg-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-bg-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DzRBYvF7d2QVbYXxj\u002BHA4FW\u002BRTEQDbXPUhMimm5vNZY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3041"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022DzRBYvF7d2QVbYXxj\u002BHA4FW\u002BRTEQDbXPUhMimm5vNZY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-bg-2.rl8quufmj8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-bg-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rl8quufmj8"},{"Name":"integrity","Value":"sha256-DzRBYvF7d2QVbYXxj\u002BHA4FW\u002BRTEQDbXPUhMimm5vNZY="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/popular-bg-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3041"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022DzRBYvF7d2QVbYXxj\u002BHA4FW\u002BRTEQDbXPUhMimm5vNZY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-bg-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-bg-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GROmNIk5uC5Rb/WP4V\u002BRDxGtu6ggKnZ4xJb5rBpr9PM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3776"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GROmNIk5uC5Rb/WP4V\u002BRDxGtu6ggKnZ4xJb5rBpr9PM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-bg-3.t398b67g9c.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-bg-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t398b67g9c"},{"Name":"integrity","Value":"sha256-GROmNIk5uC5Rb/WP4V\u002BRDxGtu6ggKnZ4xJb5rBpr9PM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/popular-bg-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3776"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GROmNIk5uC5Rb/WP4V\u002BRDxGtu6ggKnZ4xJb5rBpr9PM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-item-1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-item-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zh/ZYWwnui3IbjGeDLuhE4Ub4UiMmTQPk1\u002BB/47hQOY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2044"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022zh/ZYWwnui3IbjGeDLuhE4Ub4UiMmTQPk1\u002BB/47hQOY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-item-1.u1btb2pdtl.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-item-1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u1btb2pdtl"},{"Name":"integrity","Value":"sha256-zh/ZYWwnui3IbjGeDLuhE4Ub4UiMmTQPk1\u002BB/47hQOY="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/popular-item-1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2044"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022zh/ZYWwnui3IbjGeDLuhE4Ub4UiMmTQPk1\u002BB/47hQOY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-item-2.70htdimo4p.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-item-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"70htdimo4p"},{"Name":"integrity","Value":"sha256-VxDLbJZJ5jJ6Vl2psXo9Ebwotv870DDhGUvV32Q0F9I="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/popular-item-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1870"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VxDLbJZJ5jJ6Vl2psXo9Ebwotv870DDhGUvV32Q0F9I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-item-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-item-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VxDLbJZJ5jJ6Vl2psXo9Ebwotv870DDhGUvV32Q0F9I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1870"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VxDLbJZJ5jJ6Vl2psXo9Ebwotv870DDhGUvV32Q0F9I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-item-3.hmwpjie086.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-item-3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hmwpjie086"},{"Name":"integrity","Value":"sha256-lzPkSLnzv6mVl0w9h0pxejccR5bqXxhc2Fm\u002BZ5oDv\u002BY="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/popular-item-3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2456"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lzPkSLnzv6mVl0w9h0pxejccR5bqXxhc2Fm\u002BZ5oDv\u002BY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/popular-item-3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\popular-item-3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lzPkSLnzv6mVl0w9h0pxejccR5bqXxhc2Fm\u002BZ5oDv\u002BY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2456"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lzPkSLnzv6mVl0w9h0pxejccR5bqXxhc2Fm\u002BZ5oDv\u002BY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-01.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-01.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-01.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-01.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-01.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-02.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-02.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-02.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-02.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-02.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-03.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-03.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-03.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-03.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-03.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-04.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-04.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-04.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-04.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-04.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-05.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-05.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-05.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-05.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-05.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-06.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-06.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-06.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-06.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-06.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-07.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-07.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-07.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-07.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-07.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-08.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-08.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-08.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-08.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-08.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-09.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-09.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-09.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-09.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-09.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-10.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-10.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-10.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-10.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-10.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-11.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-11.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-11.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-11.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-11.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-12.3n433r1br0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-12.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3n433r1br0"},{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-12.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-12.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-12.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2759"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wcZas25EAPDPNXWO8TLZOTiAPEavUi0FrXqcH22l7fo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-01.859t7uwzbc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-01.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"859t7uwzbc"},{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-single-01.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-01.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-01.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-02.859t7uwzbc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-02.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"859t7uwzbc"},{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-single-02.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-02.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-02.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-03.859t7uwzbc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-03.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"859t7uwzbc"},{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-single-03.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-03.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-03.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-04.859t7uwzbc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-04.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"859t7uwzbc"},{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/products/wines/product-single-04.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/products/wines/product-single-04.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\products\wines\product-single-04.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4309"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dM04AFq2INuPlyWOefi/QpivWwagC3OJj5JCqHQYfDA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/section-shape-1.bqco7api86.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\section-shape-1.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bqco7api86"},{"Name":"integrity","Value":"sha256-czW4I7BlHHI5Rhq3zkEAtYlyjyk5xIvVU7bf8gReRPc="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/section-shape-1.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7773"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022czW4I7BlHHI5Rhq3zkEAtYlyjyk5xIvVU7bf8gReRPc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/section-shape-1.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\section-shape-1.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-czW4I7BlHHI5Rhq3zkEAtYlyjyk5xIvVU7bf8gReRPc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7773"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022czW4I7BlHHI5Rhq3zkEAtYlyjyk5xIvVU7bf8gReRPc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/section-shape-2.cwmmyi1uac.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\section-shape-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cwmmyi1uac"},{"Name":"integrity","Value":"sha256-lGhkjc1/ThenmBVSeDMIUpCm7uXWGEqRKH8ntK4p3fU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/section-shape-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5310"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lGhkjc1/ThenmBVSeDMIUpCm7uXWGEqRKH8ntK4p3fU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/section-shape-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\section-shape-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lGhkjc1/ThenmBVSeDMIUpCm7uXWGEqRKH8ntK4p3fU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5310"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lGhkjc1/ThenmBVSeDMIUpCm7uXWGEqRKH8ntK4p3fU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/shop-sidebar-banner.h8k25x3hhv.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\shop-sidebar-banner.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h8k25x3hhv"},{"Name":"integrity","Value":"sha256-D3TCoUIzzaaowlkpDObjsT1h9s5LuCmTh3yYic8P140="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/shop-sidebar-banner.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3683"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022D3TCoUIzzaaowlkpDObjsT1h9s5LuCmTh3yYic8P140=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/shop-sidebar-banner.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\shop-sidebar-banner.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-D3TCoUIzzaaowlkpDObjsT1h9s5LuCmTh3yYic8P140="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3683"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022D3TCoUIzzaaowlkpDObjsT1h9s5LuCmTh3yYic8P140=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider-2/slider-1.85i4ocx5x4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider-2\slider-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"85i4ocx5x4"},{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider-2/slider-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider-2/slider-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider-2\slider-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider-2/slider-2.85i4ocx5x4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider-2\slider-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"85i4ocx5x4"},{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider-2/slider-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider-2/slider-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider-2\slider-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider-2/slider-3.85i4ocx5x4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider-2\slider-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"85i4ocx5x4"},{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider-2/slider-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider-2/slider-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider-2\slider-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12770"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JLNs1W3t2/aJmRSmmbmEhRtqUDGlF/IJ15HUoCaEcrE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/brown-plate.fc4fkcglc8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\brown-plate.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fc4fkcglc8"},{"Name":"integrity","Value":"sha256-v0dDfFknMddDWIRAv8umsSA3FfwuPrH86IRp0T6KTLA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider/brown-plate.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"78866"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022v0dDfFknMddDWIRAv8umsSA3FfwuPrH86IRp0T6KTLA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 18:13:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/brown-plate.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\brown-plate.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-v0dDfFknMddDWIRAv8umsSA3FfwuPrH86IRp0T6KTLA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"78866"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022v0dDfFknMddDWIRAv8umsSA3FfwuPrH86IRp0T6KTLA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 18:13:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/cheese-slider-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\cheese-slider-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pP3BHHx3Kcr1/RrFlOFLl/lExtORYjRpuPB3h1LXUYU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"65555"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pP3BHHx3Kcr1/RrFlOFLl/lExtORYjRpuPB3h1LXUYU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 01:53:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/cheese-slider-1.ohax7qgdsy.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\cheese-slider-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ohax7qgdsy"},{"Name":"integrity","Value":"sha256-pP3BHHx3Kcr1/RrFlOFLl/lExtORYjRpuPB3h1LXUYU="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider/cheese-slider-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"65555"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pP3BHHx3Kcr1/RrFlOFLl/lExtORYjRpuPB3h1LXUYU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 01:53:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/new-slider.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\new-slider.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GqAfimPleGNfiEKui7rNVQUuuWzeTSHRaXyfa7OzO6U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"874630"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022GqAfimPleGNfiEKui7rNVQUuuWzeTSHRaXyfa7OzO6U=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 18:02:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/new-slider.yyvdx70e2h.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\new-slider.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yyvdx70e2h"},{"Name":"integrity","Value":"sha256-GqAfimPleGNfiEKui7rNVQUuuWzeTSHRaXyfa7OzO6U="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider/new-slider.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"874630"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022GqAfimPleGNfiEKui7rNVQUuuWzeTSHRaXyfa7OzO6U=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Apr 2025 18:02:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/slider-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\slider-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13441"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/slider-1.qxcuvsp96t.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\slider-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qxcuvsp96t"},{"Name":"integrity","Value":"sha256-AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider/slider-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"13441"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/slider-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\slider-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13441"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/slider-2.qxcuvsp96t.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\slider-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qxcuvsp96t"},{"Name":"integrity","Value":"sha256-AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider/slider-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"13441"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/slider-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\slider-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13441"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/slider/slider-3.qxcuvsp96t.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\slider\slider-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qxcuvsp96t"},{"Name":"integrity","Value":"sha256-AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/slider/slider-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"13441"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022AiTzgLXSQurVTTYP3jjtfxGV9tO2/yswY9AR3s2oC4U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/special-offer-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\special-offer-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0a6m5DFHWN7l\u002Bv0FsTdI1sxkeSw89cAA6xgUeS3xdVQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5701"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00220a6m5DFHWN7l\u002Bv0FsTdI1sxkeSw89cAA6xgUeS3xdVQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/special-offer-1.najekd1kcf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\special-offer-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"najekd1kcf"},{"Name":"integrity","Value":"sha256-0a6m5DFHWN7l\u002Bv0FsTdI1sxkeSw89cAA6xgUeS3xdVQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/special-offer-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5701"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00220a6m5DFHWN7l\u002Bv0FsTdI1sxkeSw89cAA6xgUeS3xdVQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/special-offer-2.9xnrx5xtmn.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\special-offer-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9xnrx5xtmn"},{"Name":"integrity","Value":"sha256-IwmJIHrh9vTBKYpuno7RymjIdNH1N8S0BS2R/b/2fmA="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/special-offer-2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4885"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IwmJIHrh9vTBKYpuno7RymjIdNH1N8S0BS2R/b/2fmA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/special-offer-2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\special-offer-2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IwmJIHrh9vTBKYpuno7RymjIdNH1N8S0BS2R/b/2fmA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4885"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IwmJIHrh9vTBKYpuno7RymjIdNH1N8S0BS2R/b/2fmA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-1.zf05229o9b.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zf05229o9b"},{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/user/user-1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-2.zf05229o9b.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zf05229o9b"},{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/user/user-2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-3.zf05229o9b.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zf05229o9b"},{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/user/user-3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/user/user-4.zf05229o9b.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\user\user-4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zf05229o9b"},{"Name":"integrity","Value":"sha256-gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/user/user-4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"525"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022gVQh9P99dGXxz/iyBod0XDknyvwT/X/BrqWq7YBRIs0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/zoom.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\zoom.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5ErpBGhGa1\u002BVX4Cnp/e71Sxida/xT9I9uh6K\u002BIsEbNM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"300"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00225ErpBGhGa1\u002BVX4Cnp/e71Sxida/xT9I9uh6K\u002BIsEbNM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/images/zoom.wv6pm4eqh4.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\images\zoom.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wv6pm4eqh4"},{"Name":"integrity","Value":"sha256-5ErpBGhGa1\u002BVX4Cnp/e71Sxida/xT9I9uh6K\u002BIsEbNM="},{"Name":"label","Value":"_content/TabakGokturk/assets/images/zoom.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"300"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00225ErpBGhGa1\u002BVX4Cnp/e71Sxida/xT9I9uh6K\u002BIsEbNM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/bootstrap.bundle.min.60gs7tfmf2.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"60gs7tfmf2"},{"Name":"integrity","Value":"sha256-BqtOum07gYoGosDOMBCLhWUVx0BMKgMT/gUoqRA0cwM="},{"Name":"label","Value":"_content/TabakGokturk/assets/js/bootstrap.bundle.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"80521"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022BqtOum07gYoGosDOMBCLhWUVx0BMKgMT/gUoqRA0cwM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/bootstrap.bundle.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BqtOum07gYoGosDOMBCLhWUVx0BMKgMT/gUoqRA0cwM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"80521"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022BqtOum07gYoGosDOMBCLhWUVx0BMKgMT/gUoqRA0cwM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/glightbox.min.jkq24uvl0b.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\glightbox.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jkq24uvl0b"},{"Name":"integrity","Value":"sha256-A9/h\u002B86SqfF/zRoVASE72CDQUKsIXDOEGasGo2MclFk="},{"Name":"label","Value":"_content/TabakGokturk/assets/js/glightbox.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"55880"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022A9/h\u002B86SqfF/zRoVASE72CDQUKsIXDOEGasGo2MclFk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/glightbox.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\glightbox.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A9/h\u002B86SqfF/zRoVASE72CDQUKsIXDOEGasGo2MclFk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"55880"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022A9/h\u002B86SqfF/zRoVASE72CDQUKsIXDOEGasGo2MclFk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/main.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EyTIn0qY6F3LTinvVscr6Qr70AfIycGHou9I6eZ1\u002B/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"28636"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022EyTIn0qY6F3LTinvVscr6Qr70AfIycGHou9I6eZ1\u002B/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/main.xzybi1azmu.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xzybi1azmu"},{"Name":"integrity","Value":"sha256-EyTIn0qY6F3LTinvVscr6Qr70AfIycGHou9I6eZ1\u002B/w="},{"Name":"label","Value":"_content/TabakGokturk/assets/js/main.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"28636"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022EyTIn0qY6F3LTinvVscr6Qr70AfIycGHou9I6eZ1\u002B/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/masonry.pkgd.min.6dn20cjfou.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\masonry.pkgd.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6dn20cjfou"},{"Name":"integrity","Value":"sha256-Nn1q/fx0H7SNLZMQ5Hw5JLaTRZp0yILA/FRexe19VdI="},{"Name":"label","Value":"_content/TabakGokturk/assets/js/masonry.pkgd.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"24103"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Nn1q/fx0H7SNLZMQ5Hw5JLaTRZp0yILA/FRexe19VdI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/masonry.pkgd.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\masonry.pkgd.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Nn1q/fx0H7SNLZMQ5Hw5JLaTRZp0yILA/FRexe19VdI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"24103"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Nn1q/fx0H7SNLZMQ5Hw5JLaTRZp0yILA/FRexe19VdI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/nice-select2.70p4rdokc6.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\nice-select2.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"70p4rdokc6"},{"Name":"integrity","Value":"sha256-adnD\u002BJprig\u002BlpemeZVN5byhTqp2kKSKt6JMwYiCSM4s="},{"Name":"label","Value":"_content/TabakGokturk/assets/js/nice-select2.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"9413"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022adnD\u002BJprig\u002BlpemeZVN5byhTqp2kKSKt6JMwYiCSM4s=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/nice-select2.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\nice-select2.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-adnD\u002BJprig\u002BlpemeZVN5byhTqp2kKSKt6JMwYiCSM4s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9413"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022adnD\u002BJprig\u002BlpemeZVN5byhTqp2kKSKt6JMwYiCSM4s=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/swiper-bundle.min.69ms61xlmq.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\swiper-bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"69ms61xlmq"},{"Name":"integrity","Value":"sha256-qhmzEuk1biMOE2/EZn4uavJxLUpPRAg\u002BJF7MeUgH6cE="},{"Name":"label","Value":"_content/TabakGokturk/assets/js/swiper-bundle.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"143661"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022qhmzEuk1biMOE2/EZn4uavJxLUpPRAg\u002BJF7MeUgH6cE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/js/swiper-bundle.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\js\swiper-bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qhmzEuk1biMOE2/EZn4uavJxLUpPRAg\u002BJF7MeUgH6cE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"143661"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022qhmzEuk1biMOE2/EZn4uavJxLUpPRAg\u002BJF7MeUgH6cE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/00-helpers/_index.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\00-helpers\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-poSH78oJmc6jt/5oeeDsD4toNXqI3yUgzm8uz5qdFi0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"64"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022poSH78oJmc6jt/5oeeDsD4toNXqI3yUgzm8uz5qdFi0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/00-helpers/_index.ubr05jenvh.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\00-helpers\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ubr05jenvh"},{"Name":"integrity","Value":"sha256-poSH78oJmc6jt/5oeeDsD4toNXqI3yUgzm8uz5qdFi0="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/00-helpers/_index.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"64"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022poSH78oJmc6jt/5oeeDsD4toNXqI3yUgzm8uz5qdFi0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/00-helpers/_variabls.895ocihhsn.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\00-helpers\_variabls.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"895ocihhsn"},{"Name":"integrity","Value":"sha256-nJT\u002BhrqKZRM8LaUSmIdON5AIZLIYq5Yau2K800\u002BAYtc="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/00-helpers/_variabls.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1041"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022nJT\u002BhrqKZRM8LaUSmIdON5AIZLIYq5Yau2K800\u002BAYtc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/00-helpers/_variabls.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\00-helpers\_variabls.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nJT\u002BhrqKZRM8LaUSmIdON5AIZLIYq5Yau2K800\u002BAYtc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1041"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022nJT\u002BhrqKZRM8LaUSmIdON5AIZLIYq5Yau2K800\u002BAYtc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_01-typography.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_01-typography.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Eaily5AibzOKBKkg9/fdtu\u002B28ThE2Ql3uk92gDPdybY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1504"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Eaily5AibzOKBKkg9/fdtu\u002B28ThE2Ql3uk92gDPdybY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_01-typography.zvzlrh9hga.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_01-typography.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zvzlrh9hga"},{"Name":"integrity","Value":"sha256-Eaily5AibzOKBKkg9/fdtu\u002B28ThE2Ql3uk92gDPdybY="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/01-basic/_01-typography.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1504"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Eaily5AibzOKBKkg9/fdtu\u002B28ThE2Ql3uk92gDPdybY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_02-classes.ia1dgdiaym.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_02-classes.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ia1dgdiaym"},{"Name":"integrity","Value":"sha256-ykuV75ady6thv37ExpR8RtpC0tFV1axoZZMvKZEjnH8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/01-basic/_02-classes.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"14955"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ykuV75ady6thv37ExpR8RtpC0tFV1axoZZMvKZEjnH8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_02-classes.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_02-classes.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ykuV75ady6thv37ExpR8RtpC0tFV1axoZZMvKZEjnH8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14955"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ykuV75ady6thv37ExpR8RtpC0tFV1axoZZMvKZEjnH8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_03-form.ems4luwim3.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_03-form.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ems4luwim3"},{"Name":"integrity","Value":"sha256-GqmOGtmYMmfXMhFK794viJl4OZsPOMgDJzsE5YtYdWA="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/01-basic/_03-form.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5983"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022GqmOGtmYMmfXMhFK794viJl4OZsPOMgDJzsE5YtYdWA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_03-form.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_03-form.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GqmOGtmYMmfXMhFK794viJl4OZsPOMgDJzsE5YtYdWA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5983"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022GqmOGtmYMmfXMhFK794viJl4OZsPOMgDJzsE5YtYdWA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_index.2tlh197pe6.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2tlh197pe6"},{"Name":"integrity","Value":"sha256-WmLGMoX2MRq3X9DVo3uFnCzDWnmNTAotgFA15bblhtg="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/01-basic/_index.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"129"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022WmLGMoX2MRq3X9DVo3uFnCzDWnmNTAotgFA15bblhtg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/01-basic/_index.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\01-basic\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WmLGMoX2MRq3X9DVo3uFnCzDWnmNTAotgFA15bblhtg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"129"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022WmLGMoX2MRq3X9DVo3uFnCzDWnmNTAotgFA15bblhtg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_header-main.2kwxqkz4pq.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_header-main.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2kwxqkz4pq"},{"Name":"integrity","Value":"sha256-En0AF\u002BtBwpU5RbhRKLoCj5A//u4SwPRwR4foTKlnIQI="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_header-main.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"18591"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022En0AF\u002BtBwpU5RbhRKLoCj5A//u4SwPRwR4foTKlnIQI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_header-main.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_header-main.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-En0AF\u002BtBwpU5RbhRKLoCj5A//u4SwPRwR4foTKlnIQI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"18591"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022En0AF\u002BtBwpU5RbhRKLoCj5A//u4SwPRwR4foTKlnIQI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_header-middle.gsyvuqoyf3.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_header-middle.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gsyvuqoyf3"},{"Name":"integrity","Value":"sha256-4EfFFZ7IRds/4DrLF8oNa5kAFkelvEvi6lFctVzDwyw="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_header-middle.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2679"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00224EfFFZ7IRds/4DrLF8oNa5kAFkelvEvi6lFctVzDwyw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_header-middle.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_header-middle.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4EfFFZ7IRds/4DrLF8oNa5kAFkelvEvi6lFctVzDwyw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2679"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00224EfFFZ7IRds/4DrLF8oNa5kAFkelvEvi6lFctVzDwyw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_header-top.nig4rlyksz.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_header-top.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nig4rlyksz"},{"Name":"integrity","Value":"sha256-n7tL7vXy6nVNlzri1DEiGTWyZPLjgrUuWSkyNWS0rg4="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_header-top.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2362"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022n7tL7vXy6nVNlzri1DEiGTWyZPLjgrUuWSkyNWS0rg4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_header-top.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_header-top.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-n7tL7vXy6nVNlzri1DEiGTWyZPLjgrUuWSkyNWS0rg4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2362"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022n7tL7vXy6nVNlzri1DEiGTWyZPLjgrUuWSkyNWS0rg4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_mobile-menu.126ub4kyuu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_mobile-menu.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"126ub4kyuu"},{"Name":"integrity","Value":"sha256-KbWBTyDRFaxqPgijQPVAHvi7I1KSt8Fd\u002BadXUMFQR2Y="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_mobile-menu.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5623"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022KbWBTyDRFaxqPgijQPVAHvi7I1KSt8Fd\u002BadXUMFQR2Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_mobile-menu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_mobile-menu.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KbWBTyDRFaxqPgijQPVAHvi7I1KSt8Fd\u002BadXUMFQR2Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5623"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022KbWBTyDRFaxqPgijQPVAHvi7I1KSt8Fd\u002BadXUMFQR2Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_mobile-meta.0f23byg8pu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_mobile-meta.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0f23byg8pu"},{"Name":"integrity","Value":"sha256-BB7mrpx2p6/n7qThuQ63LqQltcxVMUkOmvRJf0F3kWs="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_mobile-meta.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1169"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022BB7mrpx2p6/n7qThuQ63LqQltcxVMUkOmvRJf0F3kWs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_mobile-meta.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_mobile-meta.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BB7mrpx2p6/n7qThuQ63LqQltcxVMUkOmvRJf0F3kWs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1169"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022BB7mrpx2p6/n7qThuQ63LqQltcxVMUkOmvRJf0F3kWs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_offcanvas-menu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_offcanvas-menu.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AuDFCNgL80wLAsJwTQ4G2/XQcYkCn3NfNhqf0SGkl60="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4175"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022AuDFCNgL80wLAsJwTQ4G2/XQcYkCn3NfNhqf0SGkl60=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_offcanvas-menu.y88hsg4uiu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_offcanvas-menu.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y88hsg4uiu"},{"Name":"integrity","Value":"sha256-AuDFCNgL80wLAsJwTQ4G2/XQcYkCn3NfNhqf0SGkl60="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_offcanvas-menu.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4175"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022AuDFCNgL80wLAsJwTQ4G2/XQcYkCn3NfNhqf0SGkl60=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_search.4n3l5j4aoq.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_search.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4n3l5j4aoq"},{"Name":"integrity","Value":"sha256-UOmrDv7nRjnYKGRFv4NuOgFW858\u002BtGOaVYY06rhXZKI="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_search.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1814"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022UOmrDv7nRjnYKGRFv4NuOgFW858\u002BtGOaVYY06rhXZKI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_search.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_search.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UOmrDv7nRjnYKGRFv4NuOgFW858\u002BtGOaVYY06rhXZKI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1814"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022UOmrDv7nRjnYKGRFv4NuOgFW858\u002BtGOaVYY06rhXZKI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_slidedown-menu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_slidedown-menu.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ITCnDEovO\u002Bx5Phy\u002BFto\u002Bb2ZDt5TeexkyxWLQVQZL7es="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4348"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ITCnDEovO\u002Bx5Phy\u002BFto\u002Bb2ZDt5TeexkyxWLQVQZL7es=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/header/_slidedown-menu.t5lwh3m0dx.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\header\_slidedown-menu.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t5lwh3m0dx"},{"Name":"integrity","Value":"sha256-ITCnDEovO\u002Bx5Phy\u002BFto\u002Bb2ZDt5TeexkyxWLQVQZL7es="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/header/_slidedown-menu.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4348"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ITCnDEovO\u002Bx5Phy\u002BFto\u002Bb2ZDt5TeexkyxWLQVQZL7es=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/slider/_home-01.egqw19h5ml.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\slider\_home-01.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"egqw19h5ml"},{"Name":"integrity","Value":"sha256-Ev0d3TIZn\u002BPijtpHYNAqvJVqgUS0Vo\u002Bl/QwXRqgMZX8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/slider/_home-01.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4363"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Ev0d3TIZn\u002BPijtpHYNAqvJVqgUS0Vo\u002Bl/QwXRqgMZX8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/slider/_home-01.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\slider\_home-01.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ev0d3TIZn\u002BPijtpHYNAqvJVqgUS0Vo\u002Bl/QwXRqgMZX8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4363"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Ev0d3TIZn\u002BPijtpHYNAqvJVqgUS0Vo\u002Bl/QwXRqgMZX8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/slider/_home-02.p99x2b723x.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\slider\_home-02.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"p99x2b723x"},{"Name":"integrity","Value":"sha256-LetMA/BjoJ8ceXUQj9jYmpjOJInOz16vefCGi7fAb5w="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/slider/_home-02.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4684"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022LetMA/BjoJ8ceXUQj9jYmpjOJInOz16vefCGi7fAb5w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/slider/_home-02.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\slider\_home-02.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LetMA/BjoJ8ceXUQj9jYmpjOJInOz16vefCGi7fAb5w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4684"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022LetMA/BjoJ8ceXUQj9jYmpjOJInOz16vefCGi7fAb5w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_01-header.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_01-header.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ewAvC6MyKBYZ2Nq929XLQhCyGbUuVv30ebqtzQyMP98="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1300"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ewAvC6MyKBYZ2Nq929XLQhCyGbUuVv30ebqtzQyMP98=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_01-header.vb5eat9qaf.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_01-header.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vb5eat9qaf"},{"Name":"integrity","Value":"sha256-ewAvC6MyKBYZ2Nq929XLQhCyGbUuVv30ebqtzQyMP98="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_01-header.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1300"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ewAvC6MyKBYZ2Nq929XLQhCyGbUuVv30ebqtzQyMP98=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_02-footer.cgi24f0koc.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_02-footer.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cgi24f0koc"},{"Name":"integrity","Value":"sha256-7Om8vU0wVWYbvFKJyRVEfv/yCHu0wHMPzpUzzIO3KLo="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_02-footer.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7842"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00227Om8vU0wVWYbvFKJyRVEfv/yCHu0wHMPzpUzzIO3KLo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_02-footer.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_02-footer.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7Om8vU0wVWYbvFKJyRVEfv/yCHu0wHMPzpUzzIO3KLo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7842"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00227Om8vU0wVWYbvFKJyRVEfv/yCHu0wHMPzpUzzIO3KLo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_03-slider.c999ye0xh4.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_03-slider.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c999ye0xh4"},{"Name":"integrity","Value":"sha256-IDGKWpvCQGFgHMIAuHw40ArRg/9n09T7Cr9M\u002BgINnxQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_03-slider.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1275"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022IDGKWpvCQGFgHMIAuHw40ArRg/9n09T7Cr9M\u002BgINnxQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_03-slider.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_03-slider.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IDGKWpvCQGFgHMIAuHw40ArRg/9n09T7Cr9M\u002BgINnxQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1275"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022IDGKWpvCQGFgHMIAuHw40ArRg/9n09T7Cr9M\u002BgINnxQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_04-banner.cgz4dbeffo.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_04-banner.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cgz4dbeffo"},{"Name":"integrity","Value":"sha256-hJpiaizwrAKgWBQMwMBI3RUeM7U4GGDw7aOU7qr\u002B0dw="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_04-banner.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7520"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022hJpiaizwrAKgWBQMwMBI3RUeM7U4GGDw7aOU7qr\u002B0dw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_04-banner.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_04-banner.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hJpiaizwrAKgWBQMwMBI3RUeM7U4GGDw7aOU7qr\u002B0dw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7520"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022hJpiaizwrAKgWBQMwMBI3RUeM7U4GGDw7aOU7qr\u002B0dw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_05-products.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_05-products.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ccFIg7bVfunDHQ4ZoVDRQXcyfmB4iB7TCzL/NKjnjT4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20542"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ccFIg7bVfunDHQ4ZoVDRQXcyfmB4iB7TCzL/NKjnjT4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_05-products.v4ah14wfvw.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_05-products.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v4ah14wfvw"},{"Name":"integrity","Value":"sha256-ccFIg7bVfunDHQ4ZoVDRQXcyfmB4iB7TCzL/NKjnjT4="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_05-products.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"20542"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ccFIg7bVfunDHQ4ZoVDRQXcyfmB4iB7TCzL/NKjnjT4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_06-features.k91kuneue4.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_06-features.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k91kuneue4"},{"Name":"integrity","Value":"sha256-mMKvp4q3ISS1rL/4olvsvlc7t51Eg6c/2BaQEmWO8Ng="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_06-features.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"785"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022mMKvp4q3ISS1rL/4olvsvlc7t51Eg6c/2BaQEmWO8Ng=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_06-features.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_06-features.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mMKvp4q3ISS1rL/4olvsvlc7t51Eg6c/2BaQEmWO8Ng="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"785"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022mMKvp4q3ISS1rL/4olvsvlc7t51Eg6c/2BaQEmWO8Ng=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_07-special-offer.aprklg0f9k.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_07-special-offer.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"aprklg0f9k"},{"Name":"integrity","Value":"sha256-wgGO/T4Bx6fHQIBcRvjj31gWgQvY8Q38LfCEgRnXN/A="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_07-special-offer.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5647"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wgGO/T4Bx6fHQIBcRvjj31gWgQvY8Q38LfCEgRnXN/A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_07-special-offer.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_07-special-offer.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wgGO/T4Bx6fHQIBcRvjj31gWgQvY8Q38LfCEgRnXN/A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5647"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wgGO/T4Bx6fHQIBcRvjj31gWgQvY8Q38LfCEgRnXN/A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_08-countdown.hee6u1fia4.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_08-countdown.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hee6u1fia4"},{"Name":"integrity","Value":"sha256-aYB8rYg\u002Bw3pw07T2DDtgoOSyrx\u002B4X1NCYoIFj3LQvxk="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_08-countdown.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3110"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022aYB8rYg\u002Bw3pw07T2DDtgoOSyrx\u002B4X1NCYoIFj3LQvxk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_08-countdown.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_08-countdown.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aYB8rYg\u002Bw3pw07T2DDtgoOSyrx\u002B4X1NCYoIFj3LQvxk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3110"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022aYB8rYg\u002Bw3pw07T2DDtgoOSyrx\u002B4X1NCYoIFj3LQvxk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_09-brand.au0zht637b.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_09-brand.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"au0zht637b"},{"Name":"integrity","Value":"sha256-AlQJJDkx2LZ92lZyn6ahojlWK0eAewIvjec54RBXQSA="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_09-brand.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1638"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022AlQJJDkx2LZ92lZyn6ahojlWK0eAewIvjec54RBXQSA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_09-brand.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_09-brand.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AlQJJDkx2LZ92lZyn6ahojlWK0eAewIvjec54RBXQSA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1638"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022AlQJJDkx2LZ92lZyn6ahojlWK0eAewIvjec54RBXQSA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_10-blog.5870k72yha.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_10-blog.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5870k72yha"},{"Name":"integrity","Value":"sha256-ldRmeWWI568gVwYBCaPBc2ExO0tVUTqIxywsuyWz5\u002BE="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_10-blog.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"11546"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ldRmeWWI568gVwYBCaPBc2ExO0tVUTqIxywsuyWz5\u002BE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_10-blog.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_10-blog.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ldRmeWWI568gVwYBCaPBc2ExO0tVUTqIxywsuyWz5\u002BE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11546"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ldRmeWWI568gVwYBCaPBc2ExO0tVUTqIxywsuyWz5\u002BE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_11-instagram.70s8jex1sc.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_11-instagram.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"70s8jex1sc"},{"Name":"integrity","Value":"sha256-x3o98KKSqH0m9yZeUVMn1tMFVM\u002Bp8et9snt6Ej3raaM="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_11-instagram.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2458"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022x3o98KKSqH0m9yZeUVMn1tMFVM\u002Bp8et9snt6Ej3raaM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_11-instagram.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_11-instagram.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-x3o98KKSqH0m9yZeUVMn1tMFVM\u002Bp8et9snt6Ej3raaM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2458"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022x3o98KKSqH0m9yZeUVMn1tMFVM\u002Bp8et9snt6Ej3raaM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_12-our-info.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_12-our-info.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jHYHE0KNw0qpEyTHydGYfqyFm\u002B7Lu4wIjLfzV12tamU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1012"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022jHYHE0KNw0qpEyTHydGYfqyFm\u002B7Lu4wIjLfzV12tamU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_12-our-info.wxrc9qycxe.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_12-our-info.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wxrc9qycxe"},{"Name":"integrity","Value":"sha256-jHYHE0KNw0qpEyTHydGYfqyFm\u002B7Lu4wIjLfzV12tamU="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_12-our-info.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1012"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022jHYHE0KNw0qpEyTHydGYfqyFm\u002B7Lu4wIjLfzV12tamU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_13-about.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_13-about.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kwglFxc7HBZ9VOW\u002B/KXZGQ0AY8qbbyI/S2lr1dX6hEQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9408"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022kwglFxc7HBZ9VOW\u002B/KXZGQ0AY8qbbyI/S2lr1dX6hEQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_13-about.vhdpwuaozg.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_13-about.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vhdpwuaozg"},{"Name":"integrity","Value":"sha256-kwglFxc7HBZ9VOW\u002B/KXZGQ0AY8qbbyI/S2lr1dX6hEQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_13-about.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"9408"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022kwglFxc7HBZ9VOW\u002B/KXZGQ0AY8qbbyI/S2lr1dX6hEQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_14-category.6qqgue561e.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_14-category.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6qqgue561e"},{"Name":"integrity","Value":"sha256-sm7EdUsL6kiOUw0VSmZfmTbj5geebDLVY0yufTiANrA="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_14-category.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3706"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022sm7EdUsL6kiOUw0VSmZfmTbj5geebDLVY0yufTiANrA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_14-category.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_14-category.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sm7EdUsL6kiOUw0VSmZfmTbj5geebDLVY0yufTiANrA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3706"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022sm7EdUsL6kiOUw0VSmZfmTbj5geebDLVY0yufTiANrA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_15-product-popular.2fouz64s9k.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_15-product-popular.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2fouz64s9k"},{"Name":"integrity","Value":"sha256-HvJKi0mVHOZOxfiidKYnBL9ec3DIYj\u002BTPmGJYGgg57g="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_15-product-popular.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6409"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022HvJKi0mVHOZOxfiidKYnBL9ec3DIYj\u002BTPmGJYGgg57g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_15-product-popular.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_15-product-popular.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HvJKi0mVHOZOxfiidKYnBL9ec3DIYj\u002BTPmGJYGgg57g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6409"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022HvJKi0mVHOZOxfiidKYnBL9ec3DIYj\u002BTPmGJYGgg57g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_16-gallery.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_16-gallery.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Up1Hnq/biqI8cLg/4\u002BMT5sapbN13pU19zIc6Hei2PY0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1722"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Up1Hnq/biqI8cLg/4\u002BMT5sapbN13pU19zIc6Hei2PY0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_16-gallery.yss6uunhuu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_16-gallery.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yss6uunhuu"},{"Name":"integrity","Value":"sha256-Up1Hnq/biqI8cLg/4\u002BMT5sapbN13pU19zIc6Hei2PY0="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_16-gallery.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1722"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Up1Hnq/biqI8cLg/4\u002BMT5sapbN13pU19zIc6Hei2PY0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_17-booking.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_17-booking.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-r2zUsm6KvSAFBhdFXgMjzuKNibyXJO7IAwOM6xg7pTk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3760"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022r2zUsm6KvSAFBhdFXgMjzuKNibyXJO7IAwOM6xg7pTk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_17-booking.vc232vamp7.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_17-booking.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vc232vamp7"},{"Name":"integrity","Value":"sha256-r2zUsm6KvSAFBhdFXgMjzuKNibyXJO7IAwOM6xg7pTk="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_17-booking.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3760"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022r2zUsm6KvSAFBhdFXgMjzuKNibyXJO7IAwOM6xg7pTk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_18-client.hupdsi7vv1.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_18-client.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hupdsi7vv1"},{"Name":"integrity","Value":"sha256-GRgdLxqy3go4GKj9/ZB2zKyuEOtGzu/khEOTxCz9HEg="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_18-client.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2302"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022GRgdLxqy3go4GKj9/ZB2zKyuEOtGzu/khEOTxCz9HEg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_18-client.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_18-client.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GRgdLxqy3go4GKj9/ZB2zKyuEOtGzu/khEOTxCz9HEg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2302"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022GRgdLxqy3go4GKj9/ZB2zKyuEOtGzu/khEOTxCz9HEg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_19-call-to-action.126a3d76ve.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_19-call-to-action.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"126a3d76ve"},{"Name":"integrity","Value":"sha256-15tL\u002B\u002BODLWnROgar75WNo7UNFatUmeh8UeyU1s\u002BwVkA="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_19-call-to-action.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2476"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u002215tL\u002B\u002BODLWnROgar75WNo7UNFatUmeh8UeyU1s\u002BwVkA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_19-call-to-action.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_19-call-to-action.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-15tL\u002B\u002BODLWnROgar75WNo7UNFatUmeh8UeyU1s\u002BwVkA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2476"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u002215tL\u002B\u002BODLWnROgar75WNo7UNFatUmeh8UeyU1s\u002BwVkA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_20-testimonial.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_20-testimonial.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8aGRv7pdRHGmvrfmTXvjOIqWSjYK6tr66GBa/1vVeaI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4279"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00228aGRv7pdRHGmvrfmTXvjOIqWSjYK6tr66GBa/1vVeaI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_20-testimonial.zri03nq7pq.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_20-testimonial.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zri03nq7pq"},{"Name":"integrity","Value":"sha256-8aGRv7pdRHGmvrfmTXvjOIqWSjYK6tr66GBa/1vVeaI="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_20-testimonial.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4279"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00228aGRv7pdRHGmvrfmTXvjOIqWSjYK6tr66GBa/1vVeaI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_21-contact.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_21-contact.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bdFDRNAqrsTxz3iJKs478MO0kGs6KbtnD/ItheOAP\u002BU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6624"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022bdFDRNAqrsTxz3iJKs478MO0kGs6KbtnD/ItheOAP\u002BU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_21-contact.vhx8v1j26k.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_21-contact.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vhx8v1j26k"},{"Name":"integrity","Value":"sha256-bdFDRNAqrsTxz3iJKs478MO0kGs6KbtnD/ItheOAP\u002BU="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_21-contact.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"6624"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022bdFDRNAqrsTxz3iJKs478MO0kGs6KbtnD/ItheOAP\u002BU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_22-coming-soon.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_22-coming-soon.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CfZwcqEI99yAV\u002B2lRdiWB5nbek/5B0OoOGgJ7grvL1U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1487"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022CfZwcqEI99yAV\u002B2lRdiWB5nbek/5B0OoOGgJ7grvL1U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_22-coming-soon.vuaswhyb9z.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_22-coming-soon.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vuaswhyb9z"},{"Name":"integrity","Value":"sha256-CfZwcqEI99yAV\u002B2lRdiWB5nbek/5B0OoOGgJ7grvL1U="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_22-coming-soon.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1487"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022CfZwcqEI99yAV\u002B2lRdiWB5nbek/5B0OoOGgJ7grvL1U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_23-newsletter.k3ss3f1n6a.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_23-newsletter.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k3ss3f1n6a"},{"Name":"integrity","Value":"sha256-wDPqIgzobTnm4SdTbGXNnYT8Ob/8ZJuQpgyi7ZCZ5e4="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_23-newsletter.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5372"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wDPqIgzobTnm4SdTbGXNnYT8Ob/8ZJuQpgyi7ZCZ5e4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_23-newsletter.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_23-newsletter.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wDPqIgzobTnm4SdTbGXNnYT8Ob/8ZJuQpgyi7ZCZ5e4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5372"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wDPqIgzobTnm4SdTbGXNnYT8Ob/8ZJuQpgyi7ZCZ5e4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_24-breadcrumb.iqwgfrxgwl.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_24-breadcrumb.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iqwgfrxgwl"},{"Name":"integrity","Value":"sha256-utTkbt3unkpAyGcV2D92i5QdJIvbob/0vs4MYzO/VeA="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_24-breadcrumb.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3475"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022utTkbt3unkpAyGcV2D92i5QdJIvbob/0vs4MYzO/VeA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_24-breadcrumb.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_24-breadcrumb.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-utTkbt3unkpAyGcV2D92i5QdJIvbob/0vs4MYzO/VeA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3475"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022utTkbt3unkpAyGcV2D92i5QdJIvbob/0vs4MYzO/VeA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_25-our-team.a7cmfgnqlw.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_25-our-team.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a7cmfgnqlw"},{"Name":"integrity","Value":"sha256-RoaM/iRSpZsYPH9esxQQbEnrWNhmQ3DGQjF\u002BLwnI54Y="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_25-our-team.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2810"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022RoaM/iRSpZsYPH9esxQQbEnrWNhmQ3DGQjF\u002BLwnI54Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_25-our-team.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_25-our-team.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RoaM/iRSpZsYPH9esxQQbEnrWNhmQ3DGQjF\u002BLwnI54Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2810"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022RoaM/iRSpZsYPH9esxQQbEnrWNhmQ3DGQjF\u002BLwnI54Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_26-faqs.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_26-faqs.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cWYX1KhUvuLbN/fwkMGqm/8tX\u002BHP8C78X5nbCzFJHBw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3841"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022cWYX1KhUvuLbN/fwkMGqm/8tX\u002BHP8C78X5nbCzFJHBw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_26-faqs.zjsoflhgrm.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_26-faqs.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zjsoflhgrm"},{"Name":"integrity","Value":"sha256-cWYX1KhUvuLbN/fwkMGqm/8tX\u002BHP8C78X5nbCzFJHBw="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_26-faqs.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3841"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022cWYX1KhUvuLbN/fwkMGqm/8tX\u002BHP8C78X5nbCzFJHBw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_27-term-use.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_27-term-use.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3ctGhWw9cR/6y9VtnBVzqS5goscPw6U3e6q\u002BaJJ9fYU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1614"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00223ctGhWw9cR/6y9VtnBVzqS5goscPw6U3e6q\u002BaJJ9fYU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_27-term-use.z8ew8navkn.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_27-term-use.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z8ew8navkn"},{"Name":"integrity","Value":"sha256-3ctGhWw9cR/6y9VtnBVzqS5goscPw6U3e6q\u002BaJJ9fYU="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_27-term-use.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1614"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00223ctGhWw9cR/6y9VtnBVzqS5goscPw6U3e6q\u002BaJJ9fYU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_28-404.jmss5x3lqj.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_28-404.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jmss5x3lqj"},{"Name":"integrity","Value":"sha256-6zjSQqh2yRpML9vbALvXe02rTfB/j4rWDOvEY7VNQ34="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_28-404.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2044"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00226zjSQqh2yRpML9vbALvXe02rTfB/j4rWDOvEY7VNQ34=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_28-404.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_28-404.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6zjSQqh2yRpML9vbALvXe02rTfB/j4rWDOvEY7VNQ34="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2044"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00226zjSQqh2yRpML9vbALvXe02rTfB/j4rWDOvEY7VNQ34=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_29-comment.qtzvn7ec2e.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_29-comment.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qtzvn7ec2e"},{"Name":"integrity","Value":"sha256-4mXJmYLsC4aRovwucMQoywgdvXm95EFHkrsjYJHSVqk="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_29-comment.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"4405"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00224mXJmYLsC4aRovwucMQoywgdvXm95EFHkrsjYJHSVqk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_29-comment.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_29-comment.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4mXJmYLsC4aRovwucMQoywgdvXm95EFHkrsjYJHSVqk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4405"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00224mXJmYLsC4aRovwucMQoywgdvXm95EFHkrsjYJHSVqk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_30-cart-sidebar.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_30-cart-sidebar.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9xw\u002B9giQc3w/3Zr4kPGpJaQtDyRIj//Ij1/LZL/tSX8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5600"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00229xw\u002B9giQc3w/3Zr4kPGpJaQtDyRIj//Ij1/LZL/tSX8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_30-cart-sidebar.zxzgbitf2b.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_30-cart-sidebar.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zxzgbitf2b"},{"Name":"integrity","Value":"sha256-9xw\u002B9giQc3w/3Zr4kPGpJaQtDyRIj//Ij1/LZL/tSX8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_30-cart-sidebar.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5600"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u00229xw\u002B9giQc3w/3Zr4kPGpJaQtDyRIj//Ij1/LZL/tSX8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_31-quickview-modal.nne03aw5o4.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_31-quickview-modal.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nne03aw5o4"},{"Name":"integrity","Value":"sha256-M9U421f3u/Jibg9LKxA3i6ILSWpCk0kOW9jd1PADcEI="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_31-quickview-modal.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3225"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022M9U421f3u/Jibg9LKxA3i6ILSWpCk0kOW9jd1PADcEI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_31-quickview-modal.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_31-quickview-modal.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-M9U421f3u/Jibg9LKxA3i6ILSWpCk0kOW9jd1PADcEI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3225"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022M9U421f3u/Jibg9LKxA3i6ILSWpCk0kOW9jd1PADcEI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_32-popup-modal.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_32-popup-modal.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-01\u002BoasR/5Y4DNOb86q34O4MH2TXNAV7mU41Cfr1UpvM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7580"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u002201\u002BoasR/5Y4DNOb86q34O4MH2TXNAV7mU41Cfr1UpvM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_32-popup-modal.z0ntyhkr9g.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_32-popup-modal.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0ntyhkr9g"},{"Name":"integrity","Value":"sha256-01\u002BoasR/5Y4DNOb86q34O4MH2TXNAV7mU41Cfr1UpvM="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_32-popup-modal.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7580"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u002201\u002BoasR/5Y4DNOb86q34O4MH2TXNAV7mU41Cfr1UpvM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_index.0mz7962tvu.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0mz7962tvu"},{"Name":"integrity","Value":"sha256-\u002BG6Byj5eLa7ddZbffmMWTWwWAbRh3zqa7c7kCru4tFk="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/02-layout/_index.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"840"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022\u002BG6Byj5eLa7ddZbffmMWTWwWAbRh3zqa7c7kCru4tFk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/02-layout/_index.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\02-layout\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BG6Byj5eLa7ddZbffmMWTWwWAbRh3zqa7c7kCru4tFk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"840"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022\u002BG6Byj5eLa7ddZbffmMWTWwWAbRh3zqa7c7kCru4tFk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_01-shop.8s7jiqptrh.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_01-shop.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8s7jiqptrh"},{"Name":"integrity","Value":"sha256-KHjkqme5R1xPuOg0C8qEHm/kWxdIkIl0mjM2xkF1/zQ="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_01-shop.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"28340"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022KHjkqme5R1xPuOg0C8qEHm/kWxdIkIl0mjM2xkF1/zQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_01-shop.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_01-shop.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KHjkqme5R1xPuOg0C8qEHm/kWxdIkIl0mjM2xkF1/zQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"28340"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022KHjkqme5R1xPuOg0C8qEHm/kWxdIkIl0mjM2xkF1/zQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_02-product-single.mjg3hwqqro.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_02-product-single.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mjg3hwqqro"},{"Name":"integrity","Value":"sha256-fjiApA5ris6WpKYsgfxOwPLAf\u002Brkuf//993RXvuViy8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_02-product-single.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"21544"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022fjiApA5ris6WpKYsgfxOwPLAf\u002Brkuf//993RXvuViy8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_02-product-single.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_02-product-single.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fjiApA5ris6WpKYsgfxOwPLAf\u002Brkuf//993RXvuViy8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"21544"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022fjiApA5ris6WpKYsgfxOwPLAf\u002Brkuf//993RXvuViy8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_03-my-account.155wa2lwpj.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_03-my-account.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"155wa2lwpj"},{"Name":"integrity","Value":"sha256-\u002BzvKTJjGvgDwp2HD8vr6enLs/vYxnQuaDMSG6YT0CA4="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_03-my-account.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3684"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022\u002BzvKTJjGvgDwp2HD8vr6enLs/vYxnQuaDMSG6YT0CA4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_03-my-account.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_03-my-account.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BzvKTJjGvgDwp2HD8vr6enLs/vYxnQuaDMSG6YT0CA4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3684"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022\u002BzvKTJjGvgDwp2HD8vr6enLs/vYxnQuaDMSG6YT0CA4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_04-login-register.pf90bm87ah.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_04-login-register.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pf90bm87ah"},{"Name":"integrity","Value":"sha256-O43RGZ1agOOMEQbH33YFRHj\u002BW/IcXz1HGf2ZrwA2A4k="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_04-login-register.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2857"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022O43RGZ1agOOMEQbH33YFRHj\u002BW/IcXz1HGf2ZrwA2A4k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_04-login-register.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_04-login-register.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O43RGZ1agOOMEQbH33YFRHj\u002BW/IcXz1HGf2ZrwA2A4k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2857"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022O43RGZ1agOOMEQbH33YFRHj\u002BW/IcXz1HGf2ZrwA2A4k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_05-cart.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_05-cart.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gOWVYUinzMuAzNca7gwwHD7N0F5DOFmBQLJe01EePQg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"13979"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022gOWVYUinzMuAzNca7gwwHD7N0F5DOFmBQLJe01EePQg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_05-cart.stp3x9jnca.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_05-cart.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"stp3x9jnca"},{"Name":"integrity","Value":"sha256-gOWVYUinzMuAzNca7gwwHD7N0F5DOFmBQLJe01EePQg="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_05-cart.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"13979"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022gOWVYUinzMuAzNca7gwwHD7N0F5DOFmBQLJe01EePQg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_06-order-tracking.7k49lmg59b.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_06-order-tracking.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7k49lmg59b"},{"Name":"integrity","Value":"sha256-qfPXCveXEy\u002Brso2j8sTx12ZCw6BcLMCKFmo3i6Xg85M="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_06-order-tracking.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"3447"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022qfPXCveXEy\u002Brso2j8sTx12ZCw6BcLMCKFmo3i6Xg85M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_06-order-tracking.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_06-order-tracking.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qfPXCveXEy\u002Brso2j8sTx12ZCw6BcLMCKFmo3i6Xg85M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3447"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022qfPXCveXEy\u002Brso2j8sTx12ZCw6BcLMCKFmo3i6Xg85M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_07-wishlist.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_07-wishlist.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CXr76RZwZLQgiOg4gZKwTlOW1F/GriWW0EhKzJ9XXp8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5512"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022CXr76RZwZLQgiOg4gZKwTlOW1F/GriWW0EhKzJ9XXp8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_07-wishlist.tvpo9ukfpq.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_07-wishlist.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tvpo9ukfpq"},{"Name":"integrity","Value":"sha256-CXr76RZwZLQgiOg4gZKwTlOW1F/GriWW0EhKzJ9XXp8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_07-wishlist.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"5512"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022CXr76RZwZLQgiOg4gZKwTlOW1F/GriWW0EhKzJ9XXp8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_08-compare.hfcnlofkjs.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_08-compare.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hfcnlofkjs"},{"Name":"integrity","Value":"sha256-rSNcxjlW/x0oQ3ff1EeqhNO7LdwXUML0rfdvBvxCqMg="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_08-compare.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7033"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022rSNcxjlW/x0oQ3ff1EeqhNO7LdwXUML0rfdvBvxCqMg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_08-compare.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_08-compare.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rSNcxjlW/x0oQ3ff1EeqhNO7LdwXUML0rfdvBvxCqMg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7033"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022rSNcxjlW/x0oQ3ff1EeqhNO7LdwXUML0rfdvBvxCqMg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_09-checkout.0jzb0pomnr.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_09-checkout.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0jzb0pomnr"},{"Name":"integrity","Value":"sha256-nIxoGI2cUGoOPTunVr5QwwdsF0oF1eTCkTzQD/DAJ\u002BY="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_09-checkout.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7767"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022nIxoGI2cUGoOPTunVr5QwwdsF0oF1eTCkTzQD/DAJ\u002BY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_09-checkout.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_09-checkout.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nIxoGI2cUGoOPTunVr5QwwdsF0oF1eTCkTzQD/DAJ\u002BY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7767"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022nIxoGI2cUGoOPTunVr5QwwdsF0oF1eTCkTzQD/DAJ\u002BY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_10-thank-you.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_10-thank-you.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZtzwHzBTHm0QWMpJ09yPigfClcJqmvlLDIBzGm0WKDY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2177"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ZtzwHzBTHm0QWMpJ09yPigfClcJqmvlLDIBzGm0WKDY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_10-thank-you.uep78u28vs.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_10-thank-you.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uep78u28vs"},{"Name":"integrity","Value":"sha256-ZtzwHzBTHm0QWMpJ09yPigfClcJqmvlLDIBzGm0WKDY="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_10-thank-you.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2177"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ZtzwHzBTHm0QWMpJ09yPigfClcJqmvlLDIBzGm0WKDY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_11-blog.ca6szx7t3l.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_11-blog.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ca6szx7t3l"},{"Name":"integrity","Value":"sha256-LBo\u002BRECBKKW02RbZG08usdQHL/uEqBuAGffQkhdR7p8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_11-blog.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"8764"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022LBo\u002BRECBKKW02RbZG08usdQHL/uEqBuAGffQkhdR7p8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_11-blog.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_11-blog.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LBo\u002BRECBKKW02RbZG08usdQHL/uEqBuAGffQkhdR7p8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8764"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022LBo\u002BRECBKKW02RbZG08usdQHL/uEqBuAGffQkhdR7p8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_12-blog-single.jb1td7xz9h.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_12-blog-single.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jb1td7xz9h"},{"Name":"integrity","Value":"sha256-EdsyarJOtZDgvaE0GlmUjFgnF1tQNIjud70\u002B4mnYR9M="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_12-blog-single.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"7629"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022EdsyarJOtZDgvaE0GlmUjFgnF1tQNIjud70\u002B4mnYR9M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_12-blog-single.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_12-blog-single.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EdsyarJOtZDgvaE0GlmUjFgnF1tQNIjud70\u002B4mnYR9M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7629"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022EdsyarJOtZDgvaE0GlmUjFgnF1tQNIjud70\u002B4mnYR9M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_index.2q6z52d49x.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2q6z52d49x"},{"Name":"integrity","Value":"sha256-ykkYWIsmp5EvKQAgAsY7FUPzIQjVl0O8MH5RGMWyeZA="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/03-pages/_index.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"353"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ykkYWIsmp5EvKQAgAsY7FUPzIQjVl0O8MH5RGMWyeZA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/03-pages/_index.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\03-pages\_index.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ykkYWIsmp5EvKQAgAsY7FUPzIQjVl0O8MH5RGMWyeZA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"353"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ykkYWIsmp5EvKQAgAsY7FUPzIQjVl0O8MH5RGMWyeZA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/style.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\style.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-X/CJSBkxSKwtRUWL/voqXiuSzbWiwz9v64cyGu9dPk8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2035"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022X/CJSBkxSKwtRUWL/voqXiuSzbWiwz9v64cyGu9dPk8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/assets/scss/style.v47l59naqy.scss">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\scss\style.scss'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v47l59naqy"},{"Name":"integrity","Value":"sha256-X/CJSBkxSKwtRUWL/voqXiuSzbWiwz9v64cyGu9dPk8="},{"Name":"label","Value":"_content/TabakGokturk/assets/scss/style.scss"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"2035"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022X/CJSBkxSKwtRUWL/voqXiuSzbWiwz9v64cyGu9dPk8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 13 Apr 2025 18:00:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/TabakGokturk.9bpyg93iq8.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\TabakGokturk.9bpyg93iq8.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9bpyg93iq8"},{"Name":"integrity","Value":"sha256-W8eFxB\u002BsYArDLrnHvFL\u002BAwnLUKPEzivzpKR65dHZxvo="},{"Name":"label","Value":"_content/TabakGokturk/TabakGokturk.bundle.scp.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=********, immutable"},{"Name":"Content-Length","Value":"1130"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022W8eFxB\u002BsYArDLrnHvFL\u002BAwnLUKPEzivzpKR65dHZxvo=\u0022"},{"Name":"Last-Modified","Value":"Sat, 12 Jul 2025 22:52:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TabakGokturk/TabakGokturk.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\TabakGokturk.9bpyg93iq8.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-W8eFxB\u002BsYArDLrnHvFL\u002BAwnLUKPEzivzpKR65dHZxvo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1130"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022W8eFxB\u002BsYArDLrnHvFL\u002BAwnLUKPEzivzpKR65dHZxvo=\u0022"},{"Name":"Last-Modified","Value":"Sat, 12 Jul 2025 22:52:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>