using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using TabakGokturk.Data;
using TabakGokturk.Entity;
using TabakGokturk.Repositories;

namespace TabakGokturk.Services
{
    public class CategoryService : Service<Category>, ICategoryService
    {
        private readonly ApplicationDbContext _context;

        public CategoryService(IUnitOfWork unitOfWork, ApplicationDbContext context) : base(unitOfWork)
        {
            _context = context;
        }

        public async Task<IEnumerable<Category>> GetCategoriesWithSKUsAsync()
        {
            return await _context.Categories
                .Include(c => c.SKUCategories)
                    .ThenInclude(sc => sc.SKU)
                .ToListAsync();
        }

        public async Task<Category> GetCategoryWithSKUsAsync(int id)
        {
            return await _context.Categories
                .Include(c => c.SKUCategories)
                    .ThenInclude(sc => sc.SKU)
                .FirstOrDefaultAsync(c => c.Id == id);
        }
    }
}
