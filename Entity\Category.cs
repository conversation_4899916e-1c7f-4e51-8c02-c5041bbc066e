using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TabakGokturk.Entity
{
    public class Category
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } // for URL
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; }
        
        [StringLength(50)]
        public string Code { get; set; }
        
        // Navigation properties
        public virtual ICollection<SKUCategory> SKUCategories { get; set; }
        
        public Category()
        {
            SKUCategories = new HashSet<SKUCategory>();
        }
    }
}
