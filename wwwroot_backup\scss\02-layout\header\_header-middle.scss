/*----------------------------------------*/
/*             Header Middle CSS
/*----------------------------------------*/

// Header
.header {
    &__middle {
        padding: 42px 0;

        @media #{$laptop-device, $desktop-device} {
            padding: 30px 0;
        }
        @media #{$tablet-device, $large-mobile} {
            padding: 25px 0;
        }
    }
}

// Header Middle Logo
.header-mid-logo {
    & a {
        display: inline-block;
    }
    & img {
        width: 100%;
        height: 39px;

        @media #{$laptop-device, $desktop-device} {
            height: 32px;
        }
        @media #{$tablet-device} {
            height: 28px;
        }
        @media #{$large-mobile} {
            height: 25px;
        }
    }
}

// Header Middle Meta
.header-mid-meta {
    &__item {
        display: flex;
        align-items: center;
        margin-left: calc(-15px / 2);
        margin-right: calc(-15px / 2);

        & li {
            padding: 0 calc(15px / 2);

            & a,
            & button {
                color: $heading-color;
                position: relative;
                transition: $transition-base;
                font-size: 20px;
                padding: 0;
                border: 0;
                background: none;
                line-height: 1;

                @media #{$laptop-device, $desktop-device, $tablet-device} {
                    font-size: 18px;
                }
                @media #{$large-mobile} {
                    font-size: 16px;
                }

                & .badge {
                    background-color: $brand-color;
                    color: $white;
                    border-radius: 50%;
                    padding: 0;
                    width: 18px;
                    height: 18px;
                    line-height: 20px;
                    text-align: center;
                    font-weight: 500;
                    font-size: 9px;
                    position: absolute;
                    left: 43%;
                    bottom: 80%;
                }
            }
        }
    }
}

// Header Middle Search
.header-mid-search {
    max-width: 230px;
}

// Header Middle Toggle
.header-mid-toggle {
    margin-left: 20px;

    &__toggle {
        padding: 0;
        border: 0;
        background: none;
        color: $heading-color;
        font-size: 24px;
        line-height: 1;

        @media #{$large-mobile} {
            font-size: 20px;
        }

        &:focus,
        &:hover {
            color: $brand-color;
        }
    }
}
