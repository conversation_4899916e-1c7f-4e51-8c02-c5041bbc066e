/*----------------------------------------*/
/*  02.26 - FAQ’s CSS
/*----------------------------------------*/

// FAQ’s Section
.faq-section {
}

// FAQ’s Wrapper
.faq-container {
    max-width: 1220px;
}

// FAQ’s Wrapper
.faq-wrapper {
    & > * {
        margin-top: 100px;

        @media #{$laptop-device, $desktop-device} {
            margin-top: 80px;
        }
        @media #{$tablet-device, $large-mobile} {
            margin-top: 60px;
        }

        &:first-child {
            margin-top: 0;
        }
    }
}

// FAQ’s Accordion
.faq-accordion {
    &__title {
        font-size: 42px;
        font-weight: 600;
        line-height: 1em;
        padding-bottom: 20px;
        color: $heading-color;
        border-bottom: 2px solid $heading-color;

        @media #{$laptop-device, $desktop-device} {
            font-size: 32px;
            padding-bottom: 15px;
        }
        @media #{$tablet-device} {
            font-size: 28px;
            padding-bottom: 15px;
        }
        @media #{$large-mobile} {
            font-size: 24px;
            padding-bottom: 10px;
        }
    }

    & .accordion {
        margin-top: 20px;
        border-radius: 0;
    }
    & .accordion-item {
        color: $heading-color;
        background-color: transparent;
        border: 0;
        border-bottom: 1px solid #d4d4d4;
        border-radius: 0;

        &:last-child {
            padding-bottom: 0;
        }

        & button {
            display: block;
            width: 100%;
            text-align: left;
            border: 0;
            font-size: 22px;
            font-weight: 600;
            line-height: 1;
            padding: 20px 0;
            background: none;
            padding-right: 10px;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                font-size: 20px;
            }
            @media #{$large-mobile} {
                font-size: 16px;
                padding: 12px 0;
            }

            display: flex;

            & .text {
                flex: 1;
            }
            & .icon {
                flex-shrink: 0;
                font-size: 18px;
                line-height: 0.8;
                transform: rotate(180deg);
                transition: $transition-base;
            }

            &.collapsed {
                & .icon {
                    transform: rotate(0deg);
                }
            }
        }
    }
    & .accordion-collapse {
        padding-bottom: 50px;
        transition: $transition-base;

        @media #{$large-mobile} {
            padding-bottom: 40px;
        }

        & p {
            font-size: 22px;
            line-height: 1.6;
            color: $body-color;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                font-size: 20px;
            }
            @media #{$large-mobile} {
                font-size: 16px;
            }
        }
    }
}

// FAQ’s Button
.faq-button {
    &__btn {
        font-size: 22px;
        line-height: 40px;
        color: $heading-color;
        background-color: $white;
        border: 1px solid #bababa;
        padding: 30px;
        transition: $transition-base;
        width: 100%;
        display: block;
        text-align: center;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 20px;
            padding: 15px;
        }
        @media #{$large-mobile} {
            font-size: 16px;
            padding: 12px;
        }

        &:hover {
            color: $white;
            background-color: $heading-color;
            border-color: $heading-color;
        }
    }
}
