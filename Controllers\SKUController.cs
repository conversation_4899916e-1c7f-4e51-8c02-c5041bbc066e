using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;
using TabakGokturk.Entity;
using TabakGokturk.Services;

namespace TabakGokturk.Controllers
{
    public class SKUController : Controller
    {
        private readonly ISKUService _skuService;
        private readonly ICategoryService _categoryService;

        public SKUController(ISKUService skuService, ICategoryService categoryService)
        {
            _skuService = skuService;
            _categoryService = categoryService;
        }

        // GET: SKU
        public async Task<IActionResult> Index()
        {
            var skus = await _skuService.GetSKUsWithVariantsAsync();
            return View(skus);
        }

        // GET: SKU/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var sku = await _skuService.GetSKUWithVariantsAsync(id);
            if (sku == null)
            {
                return NotFound();
            }

            return View(sku);
        }

        private async Task<SKUFormViewModel> GetSKUFormViewModelAsync(SKU sku = null)
        {
            var categories = await _categoryService.GetAllAsync() ?? new List<Category>();
            var parentSKUs = await _skuService.GetAllAsync() ?? new List<SKU>();

            return new SKUFormViewModel
            {
                SKU = sku ?? new SKU(),
                Categories = new SelectList(categories, "Id", "Title", sku?.Category),
                ParentSKUs = new SelectList(parentSKUs, "Id", "Title", sku?.ParentId)
            };
        }

        // GET: SKU/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = await GetSKUFormViewModelAsync();
            return View(viewModel);
        }

        // POST: SKU/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SKUFormViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                await _skuService.AddAsync(viewModel.SKU);
                await _skuService.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            // Yeniden doldur
            viewModel = await GetSKUFormViewModelAsync(viewModel.SKU);
            return View(viewModel);
        }

        // GET: SKU/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var sku = await _skuService.GetByIdAsync(id);
            if (sku == null)
            {
                return NotFound();
            }
            var viewModel = await GetSKUFormViewModelAsync(sku);
            return View(viewModel);
        }

        // POST: SKU/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, SKUFormViewModel viewModel)
        {
            if (id != viewModel?.SKU?.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                _skuService.Update(viewModel.SKU);
                await _skuService.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            viewModel = await GetSKUFormViewModelAsync(viewModel.SKU);
            return View(viewModel);
        }

        // GET: SKU/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var sku = await _skuService.GetByIdAsync(id);
            if (sku == null)
            {
                return NotFound();
            }

            return View(sku);
        }

        // POST: SKU/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var sku = await _skuService.GetByIdAsync(id);
            if (sku == null)
            {
                return NotFound();
            }

            _skuService.Remove(sku);
            await _skuService.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }
    }
}
