{"runtimeOptions": {"tfm": "net8.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "8.0.0"}, {"name": "Microsoft.AspNetCore.App", "version": "8.0.0"}], "configProperties": {"Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageActionDescriptorChangeProvider.EnableFileSystemChangeDetection": true, "Microsoft.Extensions.DependencyInjection.EnableHotReload": true, "System.GC.Server": true, "System.Reflection.NullabilityInfoContext.IsSupported": true, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false}}}