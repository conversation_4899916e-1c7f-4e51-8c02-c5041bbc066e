{"Files": [{"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\TabakGokturk.bundle.scp.css", "PackagePath": "staticwebassets\\TabakGokturk.9bpyg93iq8.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\assets\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\glightbox.min.css", "PackagePath": "staticwebassets\\assets\\css\\glightbox.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\lastudioicon.css", "PackagePath": "staticwebassets\\assets\\css\\lastudioicon.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\nice-select2.css", "PackagePath": "staticwebassets\\assets\\css\\nice-select2.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\style.css", "PackagePath": "staticwebassets\\assets\\css\\style.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\style.min.css", "PackagePath": "staticwebassets\\assets\\css\\style.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\css\\swiper-bundle.min.css", "PackagePath": "staticwebassets\\assets\\css\\swiper-bundle.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\LaStudioIcons.eot", "PackagePath": "staticwebassets\\assets\\fonts\\LaStudioIcons.eot"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\LaStudioIcons.svg", "PackagePath": "staticwebassets\\assets\\fonts\\LaStudioIcons.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\LaStudioIcons.ttf", "PackagePath": "staticwebassets\\assets\\fonts\\LaStudioIcons.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\LaStudioIcons.woff", "PackagePath": "staticwebassets\\assets\\fonts\\LaStudioIcons.woff"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\nucleo-outline.svg", "PackagePath": "staticwebassets\\assets\\fonts\\nucleo-outline.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\nucleo-outline.ttf", "PackagePath": "staticwebassets\\assets\\fonts\\nucleo-outline.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\nucleo-outline.woff", "PackagePath": "staticwebassets\\assets\\fonts\\nucleo-outline.woff"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\fonts\\nucleo-outline.woff2", "PackagePath": "staticwebassets\\assets\\fonts\\nucleo-outline.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\404.svg", "PackagePath": "staticwebassets\\assets\\images\\404.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\about-bg-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\about-bg-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\about\\about-bg-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\about\\about-bg-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\about\\about-img-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\about\\about-img-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\about\\about-img-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\about\\about-img-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\about\\about-img-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\about\\about-img-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\about\\about-shape.png", "PackagePath": "staticwebassets\\assets\\images\\about\\about-shape.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\apple-store.png", "PackagePath": "staticwebassets\\assets\\images\\apple-store.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\author\\m2-testimonial-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\author\\m2-testimonial-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\author\\m2-testimonial-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\author\\m2-testimonial-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\background-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\background-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\background-2.png", "PackagePath": "staticwebassets\\assets\\images\\background-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\banner\\banner-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\banner\\banner-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\banner\\banner-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\banner\\banner-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\banner\\banner-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\banner\\banner-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\banner\\banner-4.jpg", "PackagePath": "staticwebassets\\assets\\images\\banner\\banner-4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\banner\\banner-5.jpg", "PackagePath": "staticwebassets\\assets\\images\\banner\\banner-5.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\banner\\banner-6.jpg", "PackagePath": "staticwebassets\\assets\\images\\banner\\banner-6.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog-page-header.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog-page-header.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\blog-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\blog-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\blog-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\blog-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\blog-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\blog-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\blog-4.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\blog-4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\blog-5.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\blog-5.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\content-single-post-1a.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\content-single-post-1a.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\home-1\\blog-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\home-1\\blog-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\blog\\home-1\\blog-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\blog\\home-1\\blog-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\booking.png", "PackagePath": "staticwebassets\\assets\\images\\booking.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-1.svg", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-1.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-2-1.png", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-2-1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-2-2.png", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-2-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-2-3.png", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-2-3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-2-4.png", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-2-4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-2.svg", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-2.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-3.svg", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-3.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-4.svg", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-4.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\brand\\brand-5.svg", "PackagePath": "staticwebassets\\assets\\images\\brand\\brand-5.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\cart-empty.svg", "PackagePath": "staticwebassets\\assets\\images\\cart-empty.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\category\\category-1.png", "PackagePath": "staticwebassets\\assets\\images\\category\\category-1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\category\\category-2.png", "PackagePath": "staticwebassets\\assets\\images\\category\\category-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\category\\category-3.png", "PackagePath": "staticwebassets\\assets\\images\\category\\category-3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\category\\category-4.png", "PackagePath": "staticwebassets\\assets\\images\\category\\category-4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\coming-soon.png", "PackagePath": "staticwebassets\\assets\\images\\coming-soon.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\content-single-product-image-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\content-single-product-image-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\content-single-product-image-2.png", "PackagePath": "staticwebassets\\assets\\images\\content-single-product-image-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\cta-1.png", "PackagePath": "staticwebassets\\assets\\images\\cta-1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\cta-2.png", "PackagePath": "staticwebassets\\assets\\images\\cta-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\dukkan-dis-cekim.jpg", "PackagePath": "staticwebassets\\assets\\images\\dukkan-dis-cekim.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\facebook.svg", "PackagePath": "staticwebassets\\assets\\images\\facebook.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\favicon.png", "PackagePath": "staticwebassets\\assets\\images\\favicon.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\footer-payment-1.png", "PackagePath": "staticwebassets\\assets\\images\\footer-payment-1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\footer-payment-white.png", "PackagePath": "staticwebassets\\assets\\images\\footer-payment-white.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\footer-payment.png", "PackagePath": "staticwebassets\\assets\\images\\footer-payment.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\gallery\\gallery-01.jpg", "PackagePath": "staticwebassets\\assets\\images\\gallery\\gallery-01.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\gallery\\gallery-02.jpg", "PackagePath": "staticwebassets\\assets\\images\\gallery\\gallery-02.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\gallery\\gallery-03.jpg", "PackagePath": "staticwebassets\\assets\\images\\gallery\\gallery-03.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\google-store.png", "PackagePath": "staticwebassets\\assets\\images\\google-store.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\google.svg", "PackagePath": "staticwebassets\\assets\\images\\google.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\TabakLogoGokturk.png", "PackagePath": "staticwebassets\\assets\\images\\icon\\TabakLogoGokturk.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\coupon.svg", "PackagePath": "staticwebassets\\assets\\images\\icon\\coupon.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\icon-1.svg", "PackagePath": "staticwebassets\\assets\\images\\icon\\icon-1.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\icon-2.svg", "PackagePath": "staticwebassets\\assets\\images\\icon\\icon-2.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\icon-3.svg", "PackagePath": "staticwebassets\\assets\\images\\icon\\icon-3.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\icon-4.svg", "PackagePath": "staticwebassets\\assets\\images\\icon\\icon-4.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\icon\\quote-icon.svg", "PackagePath": "staticwebassets\\assets\\images\\icon\\quote-icon.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\about-instagram-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\about-instagram-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\about-instagram-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\about-instagram-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\about-instagram-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\about-instagram-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\about-instagram-4.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\about-instagram-4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\instagram-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\instagram-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\instagram-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\instagram-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\instagram-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\instagram-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\instagram-4.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\instagram-4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\instagram-5.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\instagram-5.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\instagram\\instagram-6.jpg", "PackagePath": "staticwebassets\\assets\\images\\instagram\\instagram-6.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\logo-white.png", "PackagePath": "staticwebassets\\assets\\images\\logo-white.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\logo.png", "PackagePath": "staticwebassets\\assets\\images\\logo.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\megamenu-fashion-01.jpg", "PackagePath": "staticwebassets\\assets\\images\\megamenu-fashion-01.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\newsletter-bg-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\newsletter-bg-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\newsletter-bg-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\newsletter-bg-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\order-tracking.jpg", "PackagePath": "staticwebassets\\assets\\images\\order-tracking.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\our-team\\team-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\our-team\\team-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\our-team\\team-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\our-team\\team-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\our-team\\team-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\our-team\\team-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\our-team\\team-4.jpg", "PackagePath": "staticwebassets\\assets\\images\\our-team\\team-4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\our-team\\team-5.jpg", "PackagePath": "staticwebassets\\assets\\images\\our-team\\team-5.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\our-team\\team-6.jpg", "PackagePath": "staticwebassets\\assets\\images\\our-team\\team-6.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\placeholder.png", "PackagePath": "staticwebassets\\assets\\images\\placeholder.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\play.svg", "PackagePath": "staticwebassets\\assets\\images\\play.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\popup-bg.jpg", "PackagePath": "staticwebassets\\assets\\images\\popup-bg.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-01.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-01.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-02.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-02.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-03.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-03.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-04.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-04.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-05.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-05.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-06.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-06.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-07.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-07.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-08.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-08.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-09.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-09.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-10.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-10.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-11.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-11.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\candles\\product-12.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\candles\\product-12.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\cheese-board-1.png", "PackagePath": "staticwebassets\\assets\\images\\products\\cheese-board-1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\index-dummy-product.png", "PackagePath": "staticwebassets\\assets\\images\\products\\index-dummy-product.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\popular-bg-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\popular-bg-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\popular-bg-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\popular-bg-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\popular-bg-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\popular-bg-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\popular-item-1.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\popular-item-1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\popular-item-2.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\popular-item-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\popular-item-3.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\popular-item-3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-01.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-01.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-02.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-02.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-03.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-03.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-04.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-04.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-05.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-05.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-06.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-06.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-07.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-07.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-08.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-08.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-09.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-09.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-10.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-10.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-11.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-11.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-12.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-12.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-single-01.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-single-01.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-single-02.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-single-02.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-single-03.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-single-03.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\products\\wines\\product-single-04.png", "PackagePath": "staticwebassets\\assets\\images\\products\\wines\\product-single-04.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\section-shape-1.svg", "PackagePath": "staticwebassets\\assets\\images\\section-shape-1.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\section-shape-2.png", "PackagePath": "staticwebassets\\assets\\images\\section-shape-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\shop-sidebar-banner.jpg", "PackagePath": "staticwebassets\\assets\\images\\shop-sidebar-banner.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider-2\\slider-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider-2\\slider-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider-2\\slider-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider-2\\slider-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider-2\\slider-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider-2\\slider-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider\\brown-plate.png", "PackagePath": "staticwebassets\\assets\\images\\slider\\brown-plate.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider\\cheese-slider-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider\\cheese-slider-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider\\new-slider.png", "PackagePath": "staticwebassets\\assets\\images\\slider\\new-slider.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider\\slider-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider\\slider-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider\\slider-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider\\slider-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\slider\\slider-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\slider\\slider-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\special-offer-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\special-offer-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\special-offer-2.png", "PackagePath": "staticwebassets\\assets\\images\\special-offer-2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\user\\user-1.jpg", "PackagePath": "staticwebassets\\assets\\images\\user\\user-1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\user\\user-2.jpg", "PackagePath": "staticwebassets\\assets\\images\\user\\user-2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\user\\user-3.jpg", "PackagePath": "staticwebassets\\assets\\images\\user\\user-3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\user\\user-4.jpg", "PackagePath": "staticwebassets\\assets\\images\\user\\user-4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\images\\zoom.svg", "PackagePath": "staticwebassets\\assets\\images\\zoom.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\assets\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\js\\glightbox.min.js", "PackagePath": "staticwebassets\\assets\\js\\glightbox.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\js\\main.js", "PackagePath": "staticwebassets\\assets\\js\\main.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\js\\masonry.pkgd.min.js", "PackagePath": "staticwebassets\\assets\\js\\masonry.pkgd.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\js\\nice-select2.js", "PackagePath": "staticwebassets\\assets\\js\\nice-select2.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\js\\swiper-bundle.min.js", "PackagePath": "staticwebassets\\assets\\js\\swiper-bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\00-helpers\\_index.scss", "PackagePath": "staticwebassets\\assets\\scss\\00-helpers\\_index.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\00-helpers\\_variabls.scss", "PackagePath": "staticwebassets\\assets\\scss\\00-helpers\\_variabls.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\01-basic\\_01-typography.scss", "PackagePath": "staticwebassets\\assets\\scss\\01-basic\\_01-typography.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\01-basic\\_02-classes.scss", "PackagePath": "staticwebassets\\assets\\scss\\01-basic\\_02-classes.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\01-basic\\_03-form.scss", "PackagePath": "staticwebassets\\assets\\scss\\01-basic\\_03-form.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\01-basic\\_index.scss", "PackagePath": "staticwebassets\\assets\\scss\\01-basic\\_index.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_01-header.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_01-header.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_02-footer.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_02-footer.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_03-slider.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_03-slider.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_04-banner.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_04-banner.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_05-products.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_05-products.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_06-features.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_06-features.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_07-special-offer.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_07-special-offer.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_08-countdown.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_08-countdown.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_09-brand.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_09-brand.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_10-blog.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_10-blog.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_11-instagram.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_11-instagram.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_12-our-info.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_12-our-info.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_13-about.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_13-about.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_14-category.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_14-category.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_15-product-popular.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_15-product-popular.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_16-gallery.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_16-gallery.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_17-booking.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_17-booking.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_18-client.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_18-client.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_19-call-to-action.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_19-call-to-action.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_20-testimonial.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_20-testimonial.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_21-contact.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_21-contact.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_22-coming-soon.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_22-coming-soon.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_23-newsletter.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_23-newsletter.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_24-breadcrumb.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_24-breadcrumb.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_25-our-team.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_25-our-team.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_26-faqs.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_26-faqs.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_27-term-use.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_27-term-use.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_28-404.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_28-404.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_29-comment.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_29-comment.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_30-cart-sidebar.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_30-cart-sidebar.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_31-quickview-modal.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_31-quickview-modal.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_32-popup-modal.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_32-popup-modal.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\_index.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\_index.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_header-main.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_header-main.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_header-middle.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_header-middle.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_header-top.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_header-top.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_mobile-menu.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_mobile-menu.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_mobile-meta.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_mobile-meta.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_offcanvas-menu.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_offcanvas-menu.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_search.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_search.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\header\\_slidedown-menu.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\header\\_slidedown-menu.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\slider\\_home-01.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\slider\\_home-01.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\02-layout\\slider\\_home-02.scss", "PackagePath": "staticwebassets\\assets\\scss\\02-layout\\slider\\_home-02.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_01-shop.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_01-shop.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_02-product-single.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_02-product-single.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_03-my-account.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_03-my-account.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_04-login-register.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_04-login-register.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_05-cart.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_05-cart.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_06-order-tracking.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_06-order-tracking.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_07-wishlist.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_07-wishlist.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_08-compare.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_08-compare.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_09-checkout.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_09-checkout.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_10-thank-you.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_10-thank-you.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_11-blog.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_11-blog.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_12-blog-single.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_12-blog-single.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\03-pages\\_index.scss", "PackagePath": "staticwebassets\\assets\\scss\\03-pages\\_index.scss"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\TabakGokturk\\wwwroot\\assets\\scss\\style.scss", "PackagePath": "staticwebassets\\assets\\scss\\style.scss"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.TabakGokturk.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.TabakGokturk.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.TabakGokturk.props", "PackagePath": "build\\TabakGokturk.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.TabakGokturk.props", "PackagePath": "buildMultiTargeting\\TabakGokturk.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.TabakGokturk.props", "PackagePath": "buildTransitive\\TabakGokturk.props"}], "ElementsToRemove": []}