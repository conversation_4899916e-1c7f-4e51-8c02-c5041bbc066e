/*----------------------------------------*/
/*  02.28 - 404 CSS
/*----------------------------------------*/

// Error Section
.error-section {
    padding: 200px 0;

    @media #{$laptop-device, $desktop-device, $tablet-device} {
        padding: 120px 0;
    }
    @media #{$large-mobile} {
        padding: 100px 0;
    }
}

// Error Content
.error-content {
    max-width: 750px;
    margin: 0 auto;

    @media #{$laptop-device, $desktop-device} {
        max-width: 600px;
    }
    @media #{$tablet-device} {
        max-width: 500px;
    }

    &__icon {
        width: 63px;
        height: auto;
        object-fit: cover;

        @media #{$tablet-device, $large-mobile} {
            width: 50px;
        }
    }
    &__title {
        color: $heading-color;
        font-size: 62px;
        font-weight: 600;
        line-height: 1;
        margin: 40px 0px 30px;

        @media #{$laptop-device, $desktop-device} {
            font-size: 46px;
            margin: 20px 0;
        }
        @media #{$tablet-device} {
            font-size: 36px;
            margin: 20px 0;
        }
        @media #{$large-mobile} {
            font-size: 28px;
            margin: 20px 0;
        }
    }
    & p {
        color: $body-color;
        font-size: 24px;
        line-height: 1.7;

        @media #{$laptop-device, $desktop-device} {
            font-size: 20px;
        }
        @media #{$tablet-device} {
            font-size: 18px;
        }
        @media #{$large-mobile} {
            font-size: 16px;
        }
    }
    &__btn {
        margin-top: 60px;
        background-color: $heading-color;
        border-color: $heading-color;
        color: $white;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            margin-top: 50px;
        }

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
}
