/*----------------------------------------*/
/*  02.18 - Client CSS
/*----------------------------------------*/

// Client Wrapper
.client-wrapper {
    padding-top: 60px;

    @media #{$laptop-device, $desktop-device} {
        padding-top: 50px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 40px;
    }
}

// Client Item
.client-item {
    padding: 35px;
    border: 1px solid #cccccc;
    margin-top: 26px;
    height: auto;

    @media #{$large-mobile} {
        padding: 35px 25px;
    }

    &__image {
        position: relative;
        margin-top: -62px;
        z-index: 1;

        & img {
            width: 60px;
            height: auto;
            border-radius: 50%;
        }
    }
    &__content {
    }
    &__description {
        color: $heading-color;
        font-family: $headings-font-family-02;
        font-size: 20px;
        font-style: italic;
        width: 100%;
        margin: 20px 0px 15px;

        @media #{$large-mobile} {
            font-size: 16px;
        }
    }
    &__rating {
        position: relative;

        & span {
            overflow: hidden;
            position: relative;
            height: 1em;
            line-height: 1;
            font-size: 13px;
            color: #b3bdbc;
            display: inline-block;
            vertical-align: middle;
            letter-spacing: 0.2em;

            &::before {
                content: "\ea5e \ea5e \ea5e \ea5e \ea5e";
                display: inline-block;
                font: normal normal normal 1em/1 LaStudioIcons;
                speak: none;
                text-transform: none;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        & .star-rating {
            & > span {
                overflow: hidden;
                float: left;
                top: 0;
                left: 0;
                height: 100%;
                position: absolute;
                color: $heading-color;
            }
        }
    }
    &__name {
        color: $body-color;
        font-size: 18px;
        font-weight: 400;
        line-height: 1;
        margin-top: 20px;
    }
}
