@model IEnumerable<TabakGokturk.Entity.Category>

@{
    ViewData["Title"] = "Kategoriler";
}

<h1><PERSON>goriler</h1>

<p>
    <a asp-action="Create" class="btn btn-primary"><PERSON><PERSON></a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Title)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Code)
            </th>
            <th><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Title)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Code)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">Düzenle</a>
                <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info">Detaylar</a>
                <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Sil</a>
            </td>
        </tr>
}
    </tbody>
</table>
