is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = TabakGokturk
build_property.RootNamespace = TabakGokturk
build_property.ProjectDir = C:\Users\<USER>\source\repos\TabakGokturk\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\source\repos\TabakGokturk
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Category/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ2F0ZWdvcnlcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Home/Index2.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleDIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Product/CheeseBoards.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHJvZHVjdFxDaGVlc2VCb2FyZHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Product/DelicatessenBoards.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHJvZHVjdFxEZWxpY2F0ZXNzZW5Cb2FyZHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Product/Detail.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHJvZHVjdFxEZXRhaWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Product/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHJvZHVjdFxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/SKU/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU0tVXENyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/SKU/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU0tVXERlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/SKU/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU0tVXERldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/SKU/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU0tVXEVkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/SKU/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU0tVXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/TabakGokturk/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-yhdr62qzug
