using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using TabakGokturk.Entity;
using TabakGokturk.Models;
using TabakGokturk.Services;

namespace TabakGokturk.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ISKUService _skuService;

    public HomeController(ILogger<HomeController> logger, ISKUService skuService)
    {
        _logger = logger;
        _skuService = skuService;
    }

    public async Task<IActionResult> Index()
    {
        //test yapmak için db'deki sku'ları burada listele erişebiliyor muyuz kontrol edelim
        var skus = await _skuService.GetAllAsync();
        
        // Verileri ViewBag'e ekleyelim, böylece view'da kullanabiliriz
        ViewBag.SkuCount = skus.Count();
        ViewBag.SkuList = skus;

        return View();
    }

    public IActionResult Index2()
    {
        return View();
    }

    public IActionResult About()
    {
        return View();
    }

    public IActionResult Team()
    {
        return View();
    }

    public IActionResult Contact()
    {
        return View();
    }

    public IActionResult Faq()
    {
        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
