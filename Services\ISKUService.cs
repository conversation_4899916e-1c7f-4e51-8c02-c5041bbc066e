using System.Collections.Generic;
using System.Threading.Tasks;
using TabakGokturk.Entity;

namespace TabakGokturk.Services
{
    public interface ISKUService : IService<SKU>
    {
        Task<IEnumerable<SKU>> GetSKUsWithVariantsAsync();
        Task<SKU> GetSKUWithVariantsAsync(int id);
        Task<IEnumerable<SKU>> GetSKUsByCategoryAsync(int categoryId);
        Task<IEnumerable<SKU>> GetEnabledSKUsAsync();
    }
}
