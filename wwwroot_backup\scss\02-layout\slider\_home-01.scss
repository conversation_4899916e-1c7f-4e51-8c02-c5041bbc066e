/*--------------------------------
  Home Page 01 Style 
--------------------------------*/

// Slider Style 01
.home-1-slider-style-1 {
    background-color: #e5ebe8;
}

// Slider content Style 01
.home-1-slider-content-style-1 {
    position: relative;
    max-width: 580px;
    margin: 0 80px;

    @media #{$laptop-device, $desktop-device} {
        width: 480px;
    }
    @media #{$tablet-device} {
        width: 380px;
        margin: 0;
    }
    @media #{$large-mobile} {
        width: 270px;
        margin: 0;
    }

    @media #{$large-mobile} {
        text-align: center;
    }

    &__sub-title {
        line-height: 1;
        font-size: 36px;
        font-weight: 300;
        font-style: italic;
        font-family: $headings-font-family-02;
        color: $heading-color;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 30px;
        }
        @media #{$large-mobile} {
            font-size: 24px;
        }
    }
    &__title {
        color: $heading-color;
        font-size: 86px;
        font-weight: 300;
        line-height: 1;
        font-family: $headings-font-family-02;
        margin-top: 10px;

        @media #{$laptop-device, $desktop-device} {
            font-size: 60px;
        }
        @media #{$tablet-device} {
            font-size: 56px;
        }
        @media #{$large-mobile} {
            font-size: 30px;
        }
    }
    &__btns {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        margin-left: -15px;
        margin-right: -15px;

        & > * {
            padding: 0 15px;
            margin-top: 15px;
        }

        @media #{$large-mobile} {
            justify-content: center;
            margin-top: 10px;
        }
    }
    &__btn {
        background-color: $white;
        color: $body-color;
        font-weight: 400;
        font-size: 18px;
        transition: $transition-base;
        border: 1px solid $white;
        padding: 17px 50px;
        line-height: 1.223;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 16px;
            padding: 14px 35px;
        }
        @media #{$large-mobile} {
            font-size: 14px;
            padding: 10px 27px;
        }

        &:hover {
            border-color: $brand-color;
            background-color: $brand-color;
            color: $white;
        }
    }

    & .button-01 {
        & .home-1-slider-content-style-1 {
            &__btn {
                background-color: transparent;
                border-color: $heading-color;
                color: $heading-color;

                &:hover {
                    background-color: $white;
                    border-color: $white;
                }
            }
        }
    }
}

// Slider Animation
.home-1-slider-animation {
    & .home-1-slider-content-style-1 {
        opacity: 0;
    }

    &.swiper-slide-active {
        & .home-1-slider-content-style-1 {
            opacity: 1;

            > * {
                &:nth-child(1) {
                    animation-name: ShortFadeInUp;
                    animation-delay: 0.5s;
                    animation-duration: 1.3s;
                    animation-fill-mode: both;
                }
                &:nth-child(2) {
                    animation-name: ShortFadeInUp;
                    animation-delay: 1s;
                    animation-duration: 1.3s;
                    animation-fill-mode: both;
                }
                &:nth-child(3) {
                    & > * {
                        &:nth-child(1) {
                            animation-name: ShortFadeInUp;
                            animation-delay: 1.5s;
                            animation-duration: 1s;
                            animation-fill-mode: both;
                        }
                        &:nth-child(2) {
                            animation-name: ShortFadeInUp;
                            animation-delay: 2s;
                            animation-duration: 1s;
                            animation-fill-mode: both;
                        }
                    }
                }
            }
        }
    }
}
