/*----------------------------------------*/
/*  02.14 - Category CSS
/*----------------------------------------*/

// Category Section
.category-section {
}

// Category Wrapper
.category-wrapper {
    padding-top: 60px;
    position: relative;

    @media #{$laptop-device, $desktop-device} {
        padding-top: 50px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 40px;
    }
}

// Category Item
.category-item {
    position: relative;

    &__image {
        & a {
            position: relative;
            display: block;
            overflow: hidden;

            &::before {
                z-index: -1;
                content: "";
                border: 1px solid #b9b9a9;
                border-radius: 50%;
                position: absolute;
                bottom: 0;
                width: 270px;
                display: flex;
                padding-bottom: 270px;
                left: 50%;
                transform: translateX(-50%);

                @media #{$laptop-device, $desktop-device} {
                    width: 220px;
                    padding-bottom: 220px;
                }
                @media #{$tablet-device} {
                    width: 200px;
                    padding-bottom: 200px;
                }
                @media #{$large-mobile} {
                    width: 180px;
                    padding-bottom: 180px;
                }
                @media #{$small-mobile} {
                    width: 90%;
                    padding-bottom: 88%;
                }
            }
        }
        & img {
            width: 300px;
            object-fit: cover;
            max-height: 340px;
            transition: $transition-base;

            @media #{$laptop-device, $desktop-device} {
                width: 250px;
                height: 290px;
            }
            @media #{$tablet-device} {
                width: 220px;
                height: 250px;
            }
            @media #{$large-mobile} {
                width: 200px;
                height: 230px;
            }
            @media #{$small-mobile} {
                width: 100%;
                height: 140%;
            }
        }
    }
    &__content {
    }
    &__title {
        font-family: $headings-font-family-02;
        font-size: 32px;
        font-weight: normal;
        line-height: 1em;
        color: $heading-color;
        margin-top: 30px;
        margin-bottom: 8px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 28px;
            margin-top: 20px;
        }
        @media #{$small-mobile} {
            font-size: 20px;
            margin-top: 15px;
        }

        & a {
            transition: $transition-base;

            &:hover {
                color: $brand-color;
            }
        }
    }

    &:hover {
        & .category-item {
            &__image {
                & img {
                    transform: scale(1.1);
                }
            }
        }
    }
}

// Category Arrow
.category-arrow {
    display: flex;
    justify-content: center;
    margin-top: 50px;

    @media #{$large-mobile} {
        margin-top: 30px;
    }

    & .swiper-button-next,
    & .swiper-button-prev {
        width: 35px;
        height: 40px;
        font-size: 32px;
        border: 0;
        border-radius: 0;
        background-color: transparent;

        &:hover {
            background-color: transparent;
            color: $brand-color;
            border: 0;
        }
    }
}
