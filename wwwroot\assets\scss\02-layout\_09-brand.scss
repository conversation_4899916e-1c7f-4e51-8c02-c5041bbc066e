/*----------------------------------------*/
/*  02.09 - Popular brands CSS
/*----------------------------------------*/

// Brand Section
.brand-section {
    background-color: #fff5ed;
}

// Brand Active
.brand-active {
    padding-top: 50px;

    @media #{$laptop-device, $desktop-device} {
        padding-top: 40px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 30px;
    }
}

// Brand Item
.brand-item {
    text-align: center;

    & img {
        width: auto;
        height: auto;
        object-fit: cover;
        padding: 10px;
        transition: $transition-base;
        filter: brightness(100%) contrast(100%) saturate(0%) blur(0)
            hue-rotate(0deg);

        @media #{$desktop-device} {
            width: 80%;
        }
        @media #{$large-mobile} {
            width: 80%;
        }
        @media #{$small-mobile} {
            width: 100%;
        }
    }

    &:hover {
        & img {
            transform: scale(1.1);
        }
    }
}

// Brand Wrapper
.brand-wrapper {
    padding-top: 30px;
}

// Brand Item
.brand-item-2 {
    width: 75%;
    padding: 5%;
    margin: 0 auto;
    overflow: hidden;

    @media #{$desktop-device} {
        width: 95%;
    }
    @media #{$tablet-device} {
        width: 85%;
    }
    @media #{$large-mobile} {
        width: 95%;
    }

    & img {
        width: 100%;
        height: auto;
        object-fit: cover;
        transition: $transition-base;
    }

    &:hover {
        & img {
            transform: scale(1.1);
        }
    }
}
