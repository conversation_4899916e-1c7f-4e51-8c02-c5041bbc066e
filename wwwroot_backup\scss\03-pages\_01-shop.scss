/*----------------------------------------*/
/*  03.01 - Shop Page CSS
/*----------------------------------------*/

// Shop Filter
.shop-filter {
    display: flex;
    padding-bottom: 20px;
    position: relative;
}

// Shop Filter Default
.shop-filter-default {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    margin-left: -10px;
    margin-right: -10px;
    margin-top: -10px;
    margin-bottom: -10px;

    @media #{$laptop-device, $desktop-device} {
        margin-left: -5px;
        margin-right: -5px;
        margin-top: -5px;
        margin-bottom: -5px;
    }

    & > * {
        padding: 10px;

        @media #{$laptop-device, $desktop-device} {
            padding: 5px;
        }
        @media #{$tablet-device, $large-mobile} {
            width: 50%;
        }
        @media #{$small-mobile} {
            width: 100%;
        }
    }
}

// Shop Filter Count
.shop-filter-count {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.8;
    color: $heading-color;
    white-space: nowrap;

    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
        font-size: 16px;
    }
}

// Shop Filter Sort By
.shop-filter-sort-by {
    position: relative;

    &__label {
        font-size: 18px;
        font-weight: 400;
        line-height: 1.8;
        color: $heading-color;
        cursor: pointer;
        white-space: nowrap;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 16px;
        }

        & i {
            font-size: 12px;
            margin-left: 10px;
            transition: $transition-base;
        }
    }
    &__dropdown {
        right: 0;
        position: absolute;
        background-color: $white;
        z-index: 3;
        transition: $transition-base;
        border: 1px solid $border-color;
        transition: $transition-base;
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);

        & li {
            display: block;
            border-bottom: 1px solid $border-color;

            & a {
                padding: 7px 15px;
                white-space: nowrap;
                font-size: 15px;
                font-weight: 400;
                line-height: 1.867;
                max-width: 100%;
                display: flex;
                align-items: center;

                & i {
                    margin-left: 10px;
                }
            }

            &:hover,
            &.active {
                & a {
                    background-color: #f1f0f0;
                }
            }
        }
    }

    &:hover {
        & .shop-filter-sort-by {
            &__label {
                & i {
                    transform: rotate(180deg);
                }
            }
            &__dropdown {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
        }
    }
}

// Shop Filter Button
.shop-filter-button {
    flex-shrink: 0;
    padding-left: 20px;

    &__btn {
        font-size: 20px;
        font-weight: 400;
        line-height: 30px;
        color: $heading-color;
        padding: 4px 12px 2px;
        border: 1px solid #d9d9d9;
        background-color: transparent;

        & i {
            margin-left: 3px;
        }

        &:focus {
            color: $heading-color;
        }
    }
}

// Shop Filter Widget
.shop-filter-widget {
    position: absolute;
    right: 0;
    top: 100%;
    width: 100%;
    z-index: 4;
    padding: 40px;
    background-color: $white;
    box-shadow: 0 0 32px 0 rgba($black, 0.05);
    opacity: 0;
    visibility: hidden;
    transition: $transition-base;

    @media #{$desktop-device, $tablet-device, $large-mobile} {
        position: fixed;
        left: 0;
        right: auto;
        width: 320px;
        height: 100%;
        top: 0;
        z-index: 111;
        overflow-y: auto;
        overflow-x: hidden;
        opacity: 1;
        visibility: visible;
        transform: translateX(-100%);
    }

    &.open {
        opacity: 1;
        visibility: visible;

        @media #{$desktop-device, $tablet-device, $large-mobile} {
            transform: translateX(0);
        }
    }
}

// Filter Widget row
.filter-widget-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -13px;
    margin-right: -13px;

    @media #{$desktop-device, $tablet-device, $large-mobile} {
        margin: 0;
    }

    & > * {
        @media #{$desktop-device, $tablet-device, $large-mobile} {
            margin-top: 30px;

            &:first-child {
                margin-top: 0;
            }
        }
    }
}

// Filter Widget col
.filter-widget-col {
    width: 20%;
    padding: 0 13px;

    @media #{$desktop-device, $tablet-device, $large-mobile} {
        width: 100%;
        padding: 0;
    }
}

// Widget Item
.widget-item {
    &__title {
        font-size: 20px;
        font-weight: 500;
        color: $heading-color;
        margin-bottom: 20px;
    }
    &__filter {
        overflow-x: hidden;
        overflow-y: auto;
        max-height: 225px;
        scrollbar-width: auto;
        scrollbar-color: $heading-color #dedede;

        &::-webkit-scrollbar {
            width: 2px;
        }
        &::-webkit-scrollbar-track {
            background-color: transparent;
        }
        &::-webkit-scrollbar-thumb {
            background-color: transparent;
        }

        &:hover {
            &::-webkit-scrollbar-track {
                background-color: #dedede;
            }
            &::-webkit-scrollbar-thumb {
                background-color: $heading-color;
            }
        }
    }
    &__list {
        & li {
            margin-top: 8px;
            position: relative;

            &:first-child {
                margin-top: 0;
            }

            & label {
                font-size: 16px;
                line-height: 1.6;
                transition: $transition-base;
                display: block;

                & span {
                    position: relative;

                    &::before {
                        content: "";
                        height: 16px;
                        width: 16px;
                        background-color: #e2e2e2;
                        margin-right: 0.6em;
                        display: inline-block;
                        transition: $transition-base;
                        vertical-align: middle;
                        position: relative;
                        top: -2px;
                    }
                    &::after {
                        content: "\ea20";
                        font-family: LaStudioIcons;
                        font-size: 10px;
                        position: absolute;
                        left: 3.5px;
                        line-height: 1;
                        top: 2px;
                        color: $white;
                        opacity: 0;
                        transition: $transition-base;
                    }
                }
            }
            & input[type="checkbox"] {
                display: none;

                &:checked +,
                &:hover + {
                    & label {
                        color: $brand-color;

                        & span {
                            &::before {
                                background-color: $heading-color;
                            }
                            &::after {
                                opacity: 1;
                            }
                        }
                    }
                }
            }

            & .narrow {
                position: absolute;
                top: 0;
                right: 0;
                width: 24px;
                height: 24px;
                text-align: center;
                cursor: pointer;
                padding: 0;
                background: none;
                border: 0;

                & i {
                    font-size: 12px;
                    line-height: 1;
                }
            }

            & .children {
                margin-left: 16px;
                margin-top: 6px;
            }
        }
    }
    &__color {
        margin-left: -5px;
        margin-right: -5px;
        margin-top: -5px;
        display: flex;
        flex-wrap: wrap;

        & li {
            padding: 5px;
            width: 50%;

            & label {
                font-size: 16px;
                line-height: 1.6;
                transition: $transition-base;
                display: block;

                & span {
                    position: relative;

                    &::before {
                        content: "";
                        height: 20px;
                        width: 20px;
                        margin-right: 0.6em;
                        display: inline-block;
                        transition: $transition-base;
                        vertical-align: middle;
                        position: relative;
                        top: -2px;
                        border-radius: 50%;
                    }
                    &::after {
                        content: "\ea20";
                        font-family: LaStudioIcons;
                        font-size: 10px;
                        position: absolute;
                        left: 5px;
                        line-height: 1;
                        top: 2.5px;
                        color: $white;
                        opacity: 0;
                        transition: $transition-base;
                    }
                }
            }

            & input[type="checkbox"] {
                display: none;

                &:checked +,
                &:hover + {
                    & label {
                        color: $brand-color;

                        & span {
                            &::after {
                                opacity: 1;
                            }
                        }
                    }
                }
            }

            & .blue {
                &::before {
                    background: linear-gradient(
                        to right,
                        rgb(30, 115, 190) 50%,
                        rgb(130, 36, 227) 50%
                    );
                }
                &::after {
                    color: $white;
                }
            }
            & .bronze {
                &::before {
                    background: rgb(219, 213, 188);
                }
                &::after {
                    color: $white;
                }
            }
            & .green {
                &::before {
                    background: linear-gradient(
                        to right,
                        rgb(164, 221, 123) 50%,
                        rgb(129, 215, 66) 50%
                    );
                }
                &::after {
                    color: $white;
                }
            }
            & .pink {
                &::before {
                    background: rgb(255, 175, 206);
                }
                &::after {
                    color: $white;
                }
            }
            & .polar-blue {
                &::before {
                    background: rgb(184, 211, 203);
                }
                &::after {
                    color: $heading-color;
                }
            }
            & .red {
                &::before {
                    background: linear-gradient(
                        to right,
                        rgb(221, 0, 0) 50%,
                        rgb(198, 0, 0) 50%
                    );
                }
                &::after {
                    color: $white;
                }
            }
            & .white {
                &::before {
                    background: $white;
                    border: 1px solid $border-color;
                }
                &::after {
                    color: $heading-color;
                }
            }
            & .yellow {
                &::before {
                    background: rgb(239, 212, 101);
                }
                &::after {
                    color: $white;
                }
            }
        }
    }
    &__list-2 {
        margin-left: -5px;
        margin-right: -5px;
        margin-top: -5px;
        display: flex;
        flex-wrap: wrap;

        & li {
            padding: 5px;

            & a,
            & label {
                font-size: 14px;
                line-height: 20px;
                border: 1px solid #dedede;
                padding: 10px 14px 8px;
                transition: $transition-base;
                cursor: pointer;

                &:hover {
                    border-color: $brand-color;
                    background-color: $brand-color;
                    color: $white;
                }
            }

            & input[type="checkbox"] {
                display: none;

                &:checked +,
                &:hover + {
                    & label {
                        border-color: $brand-color;
                        background-color: $brand-color;
                        color: $white;
                    }
                }
            }
        }
    }
}

// Sidebar Shop Filter Widget
.sidebar-shop-filter-widget {
    & > * {
        margin-top: 30px;

        &:first-child {
            margin-top: 0;
        }
    }
}

// Sidebar Widget Item
.sidebar-widget-item {
    padding: 30px;
    border: 1px solid #dedede;

    &__title {
        font-size: 24px;
        font-weight: 600;
        color: $heading-color;
        padding-bottom: 12px;
        margin-bottom: 30px;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            width: 32px;
            height: 2px;
            background-color: $heading-color;
            left: 0;
            bottom: 0;
        }
    }
    &__filter {
        scrollbar-width: auto;
        scrollbar-color: $heading-color #dedede;

        &::-webkit-scrollbar {
            width: 2px;
        }
        &::-webkit-scrollbar-track {
            background-color: transparent;
        }
        &::-webkit-scrollbar-thumb {
            background-color: transparent;
        }

        &:hover {
            &::-webkit-scrollbar-track {
                background-color: #dedede;
            }
            &::-webkit-scrollbar-thumb {
                background-color: $heading-color;
            }
        }
    }
    &__list {
        & li {
            margin-top: 8px;
            position: relative;

            &:first-child {
                margin-top: 0;
            }

            & label {
                position: relative;
                font-size: 16px;
                line-height: 1.6;
                transition: $transition-base;
                display: block;

                & span {
                    &::before {
                        content: "";
                        height: 16px;
                        width: 16px;
                        background-color: #e2e2e2;
                        margin-right: 0.6em;
                        display: inline-block;
                        transition: $transition-base;
                        vertical-align: middle;
                        position: relative;
                        top: -2px;
                    }
                    &::after {
                        content: "\ea20";
                        font-family: LaStudioIcons;
                        font-size: 10px;
                        position: absolute;
                        left: 3.5px;
                        line-height: 1;
                        top: 6px;
                        color: $white;
                        opacity: 0;
                        transition: $transition-base;
                    }
                }
            }

            & input[type="checkbox"] {
                display: none;

                &:checked +,
                &:hover + {
                    & label {
                        color: $brand-color;

                        & span {
                            &::before {
                                background-color: $heading-color;
                            }
                            &::after {
                                opacity: 1;
                            }
                        }
                    }
                }
            }

            & .narrow {
                position: absolute;
                top: 0;
                right: 0;
                width: 24px;
                height: 24px;
                text-align: center;
                cursor: pointer;
                padding: 0;
                background: none;
                border: 0;

                & i {
                    font-size: 12px;
                    line-height: 1;
                }
            }

            & .children {
                margin-left: 16px;
                margin-top: 6px;
            }
        }
    }
    &__color {
        margin-left: -5px;
        margin-right: -5px;
        margin-top: -5px;
        display: flex;
        flex-wrap: wrap;

        & li {
            padding: 5px;
            width: 50%;

            & label {
                position: relative;
                font-size: 16px;
                line-height: 1.6;
                transition: $transition-base;
                display: block;

                & span {
                    &::before {
                        content: "";
                        height: 30px;
                        width: 30px;
                        margin-right: 0.6em;
                        display: inline-block;
                        transition: $transition-base;
                        vertical-align: middle;
                        position: relative;
                        top: -2px;
                        border-radius: 50%;
                    }
                    &::after {
                        content: "\ea20";
                        font-family: LaStudioIcons;
                        font-size: 10px;
                        position: absolute;
                        left: 10px;
                        line-height: 1;
                        top: 8px;
                        color: $white;
                        opacity: 0;
                        transition: $transition-base;
                    }
                }
            }
            & input[type="checkbox"] {
                display: none;

                &:checked +,
                &:hover + {
                    & label {
                        color: $brand-color;

                        & span {
                            &::after {
                                opacity: 1;
                            }
                        }
                    }
                }
            }

            & .blue {
                &::before {
                    background: linear-gradient(
                        to right,
                        rgb(30, 115, 190) 50%,
                        rgb(130, 36, 227) 50%
                    );
                }
                &::after {
                    color: $white;
                }
            }
            & .bronze {
                &::before {
                    background: rgb(219, 213, 188);
                }
                &::after {
                    color: $white;
                }
            }
            & .green {
                &::before {
                    background: linear-gradient(
                        to right,
                        rgb(164, 221, 123) 50%,
                        rgb(129, 215, 66) 50%
                    );
                }
                &::after {
                    color: $white;
                }
            }
            & .pink {
                &::before {
                    background: rgb(255, 175, 206);
                }
                &::after {
                    color: $white;
                }
            }
            & .polar-blue {
                &::before {
                    background: rgb(184, 211, 203);
                }
                &::after {
                    color: $heading-color;
                }
            }
            & .red {
                &::before {
                    background: linear-gradient(
                        to right,
                        rgb(221, 0, 0) 50%,
                        rgb(198, 0, 0) 50%
                    );
                }
                &::after {
                    color: $white;
                }
            }
            & .white {
                &::before {
                    background: $white;
                    border: 1px solid $border-color;
                }
                &::after {
                    color: $heading-color;
                }
            }
            & .yellow {
                &::before {
                    background: rgb(239, 212, 101);
                }
                &::after {
                    color: $white;
                }
            }
        }
    }
    &__list-2 {
        margin-left: -5px;
        margin-right: -5px;
        margin-top: -5px;
        display: flex;
        flex-wrap: wrap;

        & li {
            padding: 5px;

            & a,
            & label {
                font-size: 14px;
                line-height: 20px;
                border: 1px solid #dedede;
                padding: 10px 14px 8px 14px;
                transition: $transition-base;
                cursor: pointer;

                &:hover {
                    border-color: $brand-color;
                    background-color: $brand-color;
                    color: $white;
                }
            }

            & input[type="checkbox"] {
                display: none;

                &:checked +,
                &:hover + {
                    & label {
                        border-color: $brand-color;
                        background-color: $brand-color;
                        color: $white;
                    }
                }
            }
        }
    }

    & .price-filter {
    }
    & .filter-slider {
        height: 2px;
        position: relative;
        background: #d8d8d8;
        border-radius: 5px;

        & .filter-progress {
            // left: 25%;
            // right: 25%;
            height: 100%;
            position: absolute;
            border-radius: 5px;
            background: $brand-color;
        }
    }
    & .filter-range-input {
        position: relative;

        & input {
            position: absolute;
            width: 100%;
            height: 2px;
            top: -2px;
            background: none;
            pointer-events: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }
        input[type="range"]::-webkit-slider-thumb {
            z-index: 2;
            width: 8px;
            height: 8px;
            box-shadow: 0 0 0 4px $brand-color;
            background-color: $white;
            cursor: ew-resize;
            border-radius: 50%;
            pointer-events: auto;
            -webkit-appearance: none;
        }
        input[type="range"]::-moz-range-thumb {
            border: none;
            z-index: 2;
            width: 8px;
            height: 8px;
            box-shadow: 0 0 0 4px $brand-color;
            background-color: $white;
            cursor: ew-resize;
            border-radius: 50%;
            pointer-events: auto;
            -webkit-appearance: none;
        }
    }
    & .filter-price-value {
        margin-top: 15px;
        font-size: 14px;
        margin-bottom: 10px;
        color: $body-color;
        display: flex;
        align-items: center;

        & input {
            width: 40px;
            text-align: center;
            border: 0;
            background: none;
            padding: 0;
            font-size: 14px;
            color: $body-color;
            appearance: none;
        }
        & span {
            margin: 0 2px;
        }
    }
    & .filter-price-btn {
        font-size: 14px;
        display: inline-block;
        border: 1px solid $border-color;
        background: transparent;
        color: $heading-color;
        margin: 0;
        cursor: pointer;
        line-height: 1;
        font-weight: 600;
        transition: $transition-base;
        padding: 10px 27px;

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
}

// Sidebar Widget Banner
.sidebar-widget-banner {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 110px 40px 55px;

    &__title {
        color: rgba(198, 32, 32, 0);
        font-size: 80px;
        font-weight: 400;
        line-height: 0.9em;
        -webkit-text-stroke-width: 1px;
        stroke-width: 1px;
        -webkit-text-stroke-color: #c62020;
        stroke: #c62020;
    }
    &__sub-title {
        color: $heading-color;
        font-size: 24px;
        font-weight: 400;
        line-height: 1.5em;
        margin-bottom: 10px;
    }
    &__btn {
        font-size: 14px;
        line-height: 35px;
        color: $heading-color;
        background-color: $white;
        box-shadow: 5px 5px 0 0 $heading-color;
        padding: 0 40px;
        transition: $transition-base;

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
}

// Shop Masonry
.shop-masonry {
    & .single-product {
        height: 545px;

        @media #{$tablet-device, $large-mobile} {
            height: auto;
        }

        &__thumbnail {
            &--holder {
                & a {
                    padding-bottom: 0;

                    @media #{$tablet-device, $large-mobile} {
                        padding-bottom: 108%;
                    }
                }
            }
        }
    }
}

// Shop Filter Dropdown
.shop-filter-dropdown {
    &__btn {
        font-size: 18px;
        font-weight: 500;
        line-height: 20px;
        padding: 15px 20px;
        border: 1px solid #d6d6d6;
        width: 100%;
        min-width: 200px;
        text-align: left;
        background: none;
        color: $heading-color;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media #{$laptop-device, $desktop-device} {
            font-size: 16px;
            padding: 13px;
        }
        @media #{$tablet-device, $large-mobile} {
            min-width: none;
            font-size: 16px;
            padding: 13px;
        }

        & .icon {
            margin-left: 10px;
        }
    }
    & .widget-item {
        border-radius: 0;
        border: 0;
        padding: 0;

        &__filter {
            min-width: 300px;
            overflow-y: auto;
            max-height: 265px;
            background-color: #fff;
            padding: 25px;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        }
    }
}
