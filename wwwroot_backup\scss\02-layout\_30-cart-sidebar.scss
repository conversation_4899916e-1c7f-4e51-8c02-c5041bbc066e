/*----------------------------------------*/
/*  02.30 - Cart Sidebar CSS
/*----------------------------------------*/

// Cart Off Canvas
.cart-offcanvas {
    &.offcanvas-end {
        width: 450px;

        @media #{$small-mobile} {
            width: 320px;
        }
    }

    & .offcanvas-header {
        padding: 40px 40px 15px;
        position: relative;

        @media #{$small-mobile} {
            padding: 20px 20px 10px;
        }

        & .offcanvas-title {
            font-size: 24px;
            font-weight: 600;
            color: $heading-color;
            line-height: 1.2;

            @media #{$large-mobile} {
                font-size: 20px;
            }
        }
        & .btn-close {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid $heading-color;
            border-radius: 50%;
            font-size: 18px;
            color: $heading-color;
            padding: 0;
            margin: 0;
            background: none;
            box-shadow: none;
            position: absolute;
            top: 20px;
            right: 20px;

            @media #{$large-mobile} {
                width: 28px;
                height: 28px;
            }
        }
    }
    & .offcanvas-body {
        padding: 15px 40px;

        @media #{$small-mobile} {
            padding: 10px 20px;
        }
    }
    & .offcanvas-footer {
        padding: 40px 40px 15px;

        @media #{$small-mobile} {
            padding: 20px 20px 10px;
        }
    }
}

// OffCanvas Cart List
.offcanvas-cart-list {
    & li {
        & + li {
            margin-top: 30px;
        }
    }
}

// OffCanvas Cart Item
.offcanvas-cart-item {
    display: flex;
    position: relative;
    padding-right: 20px;

    &__thumbnail {
        margin-right: 20px;
        border: 1px solid #dedede;
        flex-shrink: 0;

        & a {
            display: block;
        }
        & img {
            aspect-ratio: 1/1;
            width: 70px;
            height: 84px;
            object-fit: cover;
            object-position: center;
        }
    }
    &__content {
        flex-grow: 1;
    }
    &__title {
        font-size: 18px;
        font-weight: 400;
        line-height: 1.3;
        margin-bottom: 5px;
        color: $heading-color;

        & a {
            transition: $transition-base;

            &:hover {
                color: $brand-color;
            }
        }
    }
    &__quantity {
        font-size: 15px;
        color: $body-color;
        display: block;
    }
    &__remove {
        width: 20px;
        height: 20px;
        line-height: 17px;
        font-size: 18px;
        font-weight: 400;
        display: block;
        overflow: hidden;
        position: absolute;
        right: 0;
        text-align: right;
        color: $heading-color;
    }
}

// Cart Totals table
.cart-totals-table {
    & .table {
        margin-bottom: 0;

        & tbody {
            & tr {
                display: flex;
                justify-content: space-between;

                & th,
                & td {
                    font-size: 18px;
                    font-weight: 600;
                    color: $body-color;
                    vertical-align: top;
                    padding: 5px 0;
                    border: 0;

                    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                        font-size: 16px;
                    }

                    & strong {
                        font-weight: 400;
                    }
                }
                & td {
                    color: $heading-color;
                    font-size: 20px;

                    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                        font-size: 18px;
                    }
                }
            }
        }
    }

    // shipping Methods
    & .shipping-methods {
        & li {
            margin-top: 0px;

            &:first-child {
                margin-top: 0;
            }

            & label {
                font-size: 18px;

                @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                    font-size: 16px;
                }

                & .price {
                    color: $body-color;
                }
            }
        }
    }
}

.cart-buttons {
    & > * {
        margin-top: 10px;
    }
    & .btn {
        padding: 13px 20px;
        font-size: 18px;
        font-weight: 600;
        display: block;
        width: 100%;
        line-height: 1;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 16px;
        }
    }
    &__btn-1 {
        background-color: $heading-color;
        border-color: $heading-color;
        color: $white;

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
    &__btn-2 {
        background-color: $white;
        border-color: $heading-color;
        color: $heading-color;

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
}
