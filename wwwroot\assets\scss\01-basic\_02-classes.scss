/*----------------------------------------*/
/*  01.02 - Common Classes CSS
/*----------------------------------------*/

@media (min-width: 1400px) {
    .custom-container,
    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl,
    .container-xxl {
        max-width: 1526px;
    }
}

.custom-container,
.container-fluid {
    @media #{$extraBig-device, $laptop-device, $desktop-device, $tablet-device} {
        padding: 0 40px;
    }
    @media #{$large-mobile} {
        padding: 0 20px;
    }
}

// Home 01 Container
.home-container {
    max-width: 1720px;
}

// tooltip
.tooltip {
    font-family: $font-family-base;
    font-size: 14px;
    line-height: 14px;
}
.tooltip-inner {
    padding: 10px 10px 7px;
    border-radius: 3px;
    box-shadow: 4px 4px 8px rgba($black, 0.3);
}

/* tab Content */
.tab-content {
    & .tab-pane {
        display: block;
        overflow: hidden;
        height: 0;
        visibility: hidden;
        max-width: 100%;
        opacity: 0;

        &.active {
            height: auto;
            visibility: visible;
            opacity: 1;
            overflow: visible;
        }
    }
}

// View More Button
.view-more-btn {
    color: #b9b9b9;
    font-size: 18px;
    font-weight: 400;
    line-height: 1;
    letter-spacing: 0.15em;
    transition: $transition-base;
    margin-top: 65px;

    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
        margin-top: 50px;
    }
    @media #{$tablet-device, $large-mobile} {
        font-size: 16px;
    }

    &:hover {
        color: #b28686;
    }
}
.view-more-btn-2 {
    color: $heading-color;

    &:hover {
        color: #b28686;
    }
}

// Related Title
.related-title {
    &__title {
        font-size: 36px;
        font-weight: 600;
        line-height: 1;
        color: $heading-color;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 28px;
        }
        @media #{$large-mobile} {
            font-size: 24px;
        }
    }
}

// Buttons
.btn {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.2;
    color: $body-color;
    background-color: $white;
    border: 2px solid;
    border-radius: 0;
    padding: 14px 33px 13px;
    white-space: nowrap;

    @media #{$laptop-device} {
        padding: 12px 25px;
    }
    @media #{$desktop-device, $tablet-device, $large-mobile} {
        padding: 10px 25px;
    }
}

// Row gutter
.custom-g-1,
.custom-gx-1 {
    --bs-gutter-x: 20px;
}
.custom-g-1,
.custom-gy-1 {
    --bs-gutter-y: 20px;
}

.custom-g-2,
.custom-gx-2 {
    --bs-gutter-x: 40px;
}
.custom-g-2,
.custom-gy-2 {
    --bs-gutter-y: 40px;
}

// Section Title
.section-title {
    position: relative;
    z-index: 1;
    padding: 35px 0;

    @media #{$laptop-device, $desktop-device} {
        padding: 20px 0;
    }
    @media #{$tablet-device} {
        padding: 15px 0;
    }
    @media #{$large-mobile} {
        padding: 10px 0;
    }

    &__title {
        font-size: 66px;
        font-weight: 400;
        font-family: $headings-font-family-02;
        color: $heading-color;
        line-height: 1;

        @media #{$laptop-device, $desktop-device} {
            font-size: 46px;
        }
        @media #{$tablet-device} {
            font-size: 36px;
        }
        @media #{$large-mobile} {
            font-size: 30px;
        }
    }
    &__shape {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        z-index: -1;

        & img {
            width: auto;
            height: auto;

            @media #{$laptop-device, $desktop-device} {
                height: 86px;
            }
            @media #{$tablet-device} {
                height: 66px;
            }
            @media #{$large-mobile} {
                height: 50px;
            }
        }
    }
}

// Section Title 2
.section-title-2 {
    position: relative;
    z-index: 1;

    &__title {
        font-size: 50px;
        font-weight: 500;
        font-family: $headings-font-family-02;
        color: $heading-color;
        line-height: 1.1;

        @media #{$laptop-device, $desktop-device} {
            font-size: 36px;
        }
        @media #{$tablet-device} {
            font-size: 28px;
        }
        @media #{$large-mobile} {
            font-size: 24px;
        }
    }
    &__shape {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        z-index: -1;

        & img {
            width: auto;
            height: auto;

            @media #{$laptop-device, $desktop-device} {
                height: 180px;
            }
            @media #{$tablet-device} {
                height: 140px;
            }
            @media #{$large-mobile} {
                height: 100px;
            }
        }
    }
}

// Section Title 3
.section-title-3 {
    display: flex;
    flex-direction: column;
    gap: 20px;

    &__title {
        font-size: 56px;
        font-weight: 400;
        line-height: 1;
        color: $heading-color;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 36px;
        }
        @media #{$large-mobile} {
            font-size: 24px;
        }
    }

    & p {
        font-size: 20px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 18px;
        }
    }
}

// Section Padding
.section-padding {
    padding-top: 80px;
    padding-bottom: 80px;

    @media #{$laptop-device, $desktop-device,} {
        padding-top: 70px;
        padding-bottom: 70px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 60px;
        padding-bottom: 60px;
    }
}

// Section Padding
.section-padding-1 {
    padding-top: 80px;

    @media #{$laptop-device, $desktop-device,} {
        padding-top: 70px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 60px;
    }
}

// Section Padding
.section-padding-2 {
    padding-bottom: 80px;

    @media #{$laptop-device, $desktop-device,} {
        padding-bottom: 70px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-bottom: 60px;
    }
}

// Section Padding 02.1
.section-padding-02 {
    padding-top: 100px;
    padding-bottom: 100px;

    @media #{$laptop-device, $desktop-device,} {
        padding-top: 80px;
        padding-bottom: 80px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 60px;
        padding-bottom: 60px;
    }
}

// Section Padding 02.1
.section-padding-02-1 {
    padding-top: 100px;

    @media #{$laptop-device, $desktop-device,} {
        padding-top: 80px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 60px;
    }
}

// Section Padding 02.2
.section-padding-02-2 {
    padding-bottom: 100px;

    @media #{$laptop-device, $desktop-device,} {
        padding-bottom: 80px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-bottom: 60px;
    }
}

// Swiper Dot Style 01
.swiper-dot-style-1 {
    position: relative;

    & .swiper-pagination {
        position: relative;
        bottom: 0;
        margin-top: 40px;
        display: flex;
        justify-content: center;

        & .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            border: 1px solid $body-color;
            background-color: transparent;
            transition: $transition-base;
            margin: 0 5px;
            opacity: 1;

            &.swiper-pagination-bullet-active {
                border-color: $heading-color;
                background-color: $heading-color;
            }
        }
    }
}

// Swiper Dot Style 02
.swiper-dot-style-2 {
    position: relative;

    & .swiper-pagination {
        position: relative;
        display: flex;
        justify-content: center;
        margin-top: 65px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            margin-top: 50px;
        }

        &:hover {
            color: #b28686;
        }

        & .swiper-pagination-bullet {
            width: 30px;
            height: 8px;
            border-radius: 30px;
            background-color: #d3d3d3;
            transition: $transition-base;
            margin: 0 5px;
            opacity: 1;

            &.swiper-pagination-bullet-active {
                width: 60px;
                background-color: #395749;
            }
        }
    }
}

// Navigation Arrows Style 01
.navigation-arrows-style-1 {
    position: relative;
    z-index: 8;

    & .swiper-button-next,
    & .swiper-button-prev {
        font-size: 16px;
        font-weight: 700;
        width: 40px;
        height: 40px;
        line-height: 40px;
        background-color: $white;
        color: $heading-color;
        transition: $transition-base;
        opacity: 0.4;

        @media #{$desktop-device, $tablet-device, $large-mobile} {
            width: 30px;
            height: 30px;
            line-height: 30px;
        }
        @media #{$large-mobile} {
            width: 25px;
            height: 25px;
            line-height: 25px;
        }

        &::after {
            display: none;
        }

        & svg {
            width: 1em;
            height: 1em;
            vertical-align: middle;
        }

        &:hover {
            opacity: 1;
        }
    }

    & .swiper-button-next {
        right: 40px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            right: 20px;
        }
    }
    & .swiper-button-prev {
        left: 30px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            left: 20px;
        }
    }
}

// Navigation Arrows Style 02
.navigation-arrows-style-2 {
    position: relative;

    & .swiper-button-next,
    & .swiper-button-prev {
        width: 44px;
        height: 44px;
        line-height: 44px;
        position: relative;
        margin-top: 0;
        border: 1px solid #bababa;
        background-color: $white;
        border-radius: 50%;
        font-size: 16px;
        font-weight: 700;
        color: $heading-color;
        transition: $transition-base;
        left: 0;
        right: 0;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            width: 40px;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
        }

        &::after {
            display: none;
        }

        & svg {
            width: 1em;
            height: 1em;
        }

        &:hover {
            background-color: $heading-color;
            border: 1px solid $heading-color;
            color: $white;
        }
    }
}

// Pagination
.bg-home {
    background-color: #ebebe8;
}

// Pagination
.paginations {
    margin-top: 60px;
}

.paginations-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;

    & li {
        margin: 0 5px 5px;

        & a {
            background-color: transparent;
            color: $body-color;
            width: 46px;
            height: 46px;
            line-height: 48px;
            border: 1px solid #c6c6c6;
            border-radius: 50%;
            transition: $transition-base;
            text-align: center;
            font-size: 18px;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                width: 40px;
                height: 40px;
                line-height: 40px;
                font-size: 16px;
            }
            @media #{$large-mobile} {
                width: 34px;
                height: 34px;
                line-height: 34px;
                font-size: 15px;
            }

            &:hover,
            &.active {
                background-color: $brand-color;
                border-color: $brand-color;
                color: $white;
            }
        }
    }
}

.paginations-list-2 {
    display: flex;
    flex-wrap: wrap;
    margin-left: -5px;
    margin-right: -5px;

    & li {
        padding: 0 5px 5px;

        & a {
            background-color: transparent;
            color: $body-color;
            width: 44px;
            height: 44px;
            line-height: 46px;
            border: 1px solid #c6c6c6;
            border-radius: 50%;
            transition: $transition-base;
            text-align: center;
            font-size: 18px;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                width: 38px;
                height: 38px;
                line-height: 38px;
                font-size: 16px;
            }
            @media #{$large-mobile} {
                width: 34px;
                height: 34px;
                line-height: 34px;
                font-size: 15px;
            }

            &:hover,
            &.active {
                background-color: $heading-color;
                border-color: $heading-color;
                color: $white;
            }
        }
    }
}

// Scroll Animations
.js-scroll {
    opacity: 0;
}

// Scrolled
.scrolled {
    &.ShortFadeInDown {
        animation: ShortFadeInDown 1s ease-in-out both;
    }
    &.ShortFadeInUp {
        animation: ShortFadeInUp 1s ease-in-out both;
    }

    &.ShortFadeInLeft {
        animation: ShortFadeInLeft 1s ease-in-out both;
    }

    &.ShortFadeInRight {
        animation: ShortFadeInRight 1s ease-in-out both;
    }
}

@keyframes ShortFadeInDown {
    0% {
        opacity: 0;
        transform: translate3d(0, -50px, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes ShortFadeInUp {
    0% {
        opacity: 0;
        transform: translate3d(0, 50px, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes ShortFadeInLeft {
    0% {
        opacity: 0;
        transform: translate3d(-50px, 0, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes ShortFadeInRight {
    0% {
        opacity: 0;
        transform: translate3d(50px, 0, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
