using Microsoft.AspNetCore.Mvc;

namespace TabakGokturk.Controllers
{
    public class ProductController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult CheeseBoards()
        {
            return View();
        }

        public IActionResult DelicatessenBoards()
        {
            return View();
        }

        public IActionResult Detail(int id)
        {
            // Burada gerçek bir uygulamada, id'ye göre ürün detaylarını veritabanından çekeceksiniz
            // Şimdilik sadece View'ı döndürüyoruz
            ViewBag.ProductId = id;
            return View();
        }
    }
}
