/*----------------------------------------*/
/*  03.06 - Order Tracking CSS
/*----------------------------------------*/

// Order Tracking Section
.order-tracking-section {
}

// Order Tracking Wrapper
.order-tracking-wrapper {
}

// Order Tracking Banner
.order-tracking-banner {
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 840px;
    padding: 0 15px;

    @media #{$laptop-device, $desktop-device} {
        height: 640px;
    }
    @media #{$tablet-device} {
        height: 540px;
    }
    @media #{$large-mobile} {
        height: 340px;
    }

    &__sub-title {
        font-size: 32px;
        font-weight: 600;
        line-height: 1;
        color: $white;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 20px;
        }
    }
    &__title {
        color: $white;
        font-size: 46px;
        font-weight: 600;
        line-height: 1;
        margin-top: 20px;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 28px;
        }
        @media #{$large-mobile} {
            font-size: 24px;
        }
    }
    &__btn {
        margin-top: 50px;
        background-color: $white;
        border-color: $white;
        font-size: 20px;
        font-weight: 400;
        line-height: 55px;
        color: $heading-color;
        padding: 0 50px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 16px;
            line-height: 46px;
            padding: 0 30px;
        }

        @media #{$large-mobile} {
            margin-top: 30px;
        }

        &:hover {
            background-color: $heading-color;
            border-color: $heading-color;
            color: $white;
        }
    }
}

// Order Tracking Form
.order-tracking-form {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 840px;

    @media #{$laptop-device, $desktop-device} {
        height: 640px;
    }
    @media #{$tablet-device} {
        height: 540px;
    }
    @media #{$large-mobile} {
        height: auto;
        padding: 40px 20px;
    }

    &__wrapper {
        max-width: 620px;
        width: 100%;
        margin: 0 auto;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            width: 80%;
        }

        & p {
            color: $heading-color;
            font-size: 18px;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 16px;
            }
        }
    }

    & .single-form {
        &__label {
            font-size: 18px;
            color: $heading-color;
            font-weight: 600;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 16px;
            }
        }
        &__input {
        }
        &__btn {
            min-width: 160px;
            font-size: 14px;
            line-height: 1.6;

            @media #{$large-mobile} {
                width: 100%;
                display: block;
            }
        }
    }
}
