/*----------------------------------------*/
/*  03.05 - Cart CSS
/*----------------------------------------*/

// Cart Section
.cart-section {
}

// Cart Empty
.cart-empty {
    img {
        width: 230px;
        height: auto;
        object-fit: cover;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            width: 150px;
        }
    }
    p {
        font-size: 34px;
        margin-top: 50px;
        color: $heading-color;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 20px;
            margin-top: 30px;
        }
    }
    &__btn {
        padding: 18px 25px;
        line-height: 20px;
        font-size: 15px;
        font-weight: 500;
        text-decoration: none;
        text-transform: uppercase;
        border: 1px solid $border-color;
        color: $heading-color;
        background-color: transparent;
        margin-top: 2em;

        &:hover {
            background-color: $brand-color;
            border-color: $brand-color;
            color: $white;
        }
    }
}

// Cart Wrapper
.cart-wrapper {
}

// Cart Form
.cart-form {
    display: table-cell;
    vertical-align: top;
    width: 100%;
    padding-right: 30px;

    @media #{$tablet-device, $large-mobile} {
        display: block;
        padding-right: 0;
    }
}

// Cart Collaterals
.cart-collaterals {
    display: table-cell;
    vertical-align: top;
    min-width: 370px;

    @media #{$tablet-device, $large-mobile} {
        min-width: 100%;
        display: block;
        margin-top: 30px;
    }
}

// Free Shipping Goal
.free-shipping-goal {
    &__label {
        position: relative;
        font-size: 18px;
        line-height: 1.2;
        color: $body-color;

        & strong {
            font-weight: 600;
        }
    }
    &__loading-bar {
        width: 100%;
        height: 6px;
        background-color: #ebebeb;
        position: relative;
        margin-top: 0.7em;
        margin-bottom: 0.7em;

        & .load-percent {
            display: block;
            height: 100%;
            position: absolute;
            max-width: 100%;
            background-color: #e6992f;
            color: #e6992f;

            &::after {
                content: "";
                font-size: 20px;
                font-family: "LaStudioIcons";
                border: 1px solid #e6992f;
                border-radius: 50%;
                position: absolute;
                top: 50%;
                right: 0;
                display: block;
                height: auto;
                line-height: 1;
                padding: 0.15em;
                transform: translateY(-50%);
                background: $white;
            }
        }
    }
}

// Cart Meta
.cart-meta {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    font-size: 16px;
    margin-bottom: 10px;

    & li {
        position: relative;
        flex: 1;

        & + li {
            &::before {
                content: "";
                border-left: 1px solid $body-color;
                height: 20px;
                left: 0;
                position: absolute;
                opacity: 0.5;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        & a {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px;
            gap: 8px;
            position: relative;
            color: $body-color;

            svg {
                width: 1rem;
                height: 1rem;
            }

            &:hover {
                color: $brand-color;
            }
        }
    }
}

// Cart Table
.cart-table {
    margin-top: 36px;

    & .table {
        margin-bottom: 0;

        thead {
            @media #{$large-mobile} {
                display: none;
            }

            & tr {
                & th {
                    color: $heading-color;
                    border: 0;
                    border-bottom: 1px solid #dbdbdb;
                    padding: 10px 0;
                    vertical-align: middle;
                    font-size: 18px;
                    font-weight: 400;

                    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                        font-size: 16px;
                    }
                }
            }
        }
        tbody {
            & tr {
                &.cart-item {
                    @media #{$large-mobile} {
                        position: relative;
                        display: block;
                        padding-top: 10px;
                        padding-bottom: 20px;
                        padding-left: 100px;
                        padding-right: 25px;
                        border: 1px solid #eee;
                        margin-bottom: 17px;
                        overflow: hidden;
                    }
                }

                & td {
                    color: $body-color;
                    border: 0;
                    padding: 20px 0;
                    vertical-align: middle;
                    font-size: 18px;
                    font-weight: 400;
                    line-height: 1.6;

                    @media #{$laptop-device, $desktop-device, $tablet-device} {
                        font-size: 16px;
                    }
                    @media #{$large-mobile} {
                        padding: 0;
                        width: auto;
                        border: none;
                        display: block;
                        min-width: 0;
                        font-size: 14px;

                        &[data-title] {
                            &::before {
                                content: attr(data-title) ": ";
                                float: left;
                                font-size: 13px;
                                color: $heading-color;
                                padding-right: 15px;
                            }
                        }
                    }
                }
            }
        }
    }

    & .cart-product-remove {
        width: 30px;

        @media #{$large-mobile} {
            position: absolute;
            top: 15px;
            right: 10px;
            z-index: 2;
            width: auto;
        }

        & .remove {
            font-size: 0;

            &::before {
                font-family: "LaStudioIcons";
                display: inline-block;
                vertical-align: middle;
                font-weight: normal;
                font-style: normal;
                letter-spacing: normal;
                text-rendering: auto;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                content: "";
                font-size: 14px;
                line-height: 30px;
                color: $body-color;
            }
        }
    }
    & .cart-product-thumbnail {
        width: 90px;

        @media #{$large-mobile} {
            position: absolute;
            width: 70px !important;
            display: inline-block;
            top: 15px;
            left: 15px;
            bottom: 15px;
            overflow: hidden;
        }

        & a {
            display: block;
            padding-right: 20px;

            @media #{$large-mobile} {
                padding-right: 0;
            }
        }
        & img {
            width: 100%;
            height: auto;
        }
    }
    & .cart-product-name {
    }
    & .cart-product-price {
        & .price-amount {
            & del {
                text-decoration: line-through;
                color: #b2b2b2;
            }
            & ins {
                text-decoration: none;
            }
        }
    }
    & .cart-product-quantity {
        width: 100px;
    }
    & .cart-product-subtotal {
        & .price-amount {
            color: $heading-color;
        }
    }

    &__quantity {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        width: 80px;
        text-align: center;

        @media #{$large-mobile} {
            width: auto;
            justify-content: flex-start;
        }

        & button {
            width: 18px;
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            font-size: 18px;
            line-height: 1;
            padding: 0;
            border: 0;
            background: none;

            @media #{$large-mobile} {
                display: none;
            }
        }
        & input {
            width: 40px;
            display: inline-block;
            height: 30px;
            appearance: none;
            color: inherit;
            text-align: center;
            padding: 0px;
            border: 0;
            background: none;

            @media #{$large-mobile} {
                font-size: 14px;
                height: 24px;
            }
        }
    }
}

// Cart Coupon
.cart-coupon {
    width: 50%;
    position: relative;

    @media #{$large-mobile} {
        width: 100%;
    }

    &__input {
        display: inline-block;
        border: 0;
        width: 100%;
        font-size: 16px;
        font-weight: 400;
        color: $body-color;
        height: 34px;
        border-bottom: 1px solid #d8d8d8;
        padding-right: 100px;

        @media #{$large-mobile} {
            padding: 10px 20px;
            border: 1px solid #d8d8d8;
            line-height: 1.8;
            height: auto;
            font-size: 16px;
        }
    }
    &__btn {
        position: absolute;
        top: 0;
        right: 0;
        background: none;
        border: 0;
        padding: 0;
        height: auto;
        font-size: 14px;
        font-weight: 600;
        line-height: 34px;
        color: $heading-color;
        white-space: nowrap;

        @media #{$large-mobile} {
            position: relative;
            width: 100%;
            background-color: $heading-color;
            margin-top: 10px;
            color: $white;
            padding: 18px 30px;
            line-height: 1.2;

            &:hover {
                background-color: $brand-color;
                color: $white;
            }
        }
    }
}

// Cart Update Button
.cart-update-btn {
    background: none;
    border: 0;
    padding: 0;
    height: auto;
    font-size: 14px;
    font-weight: 600;
    color: $heading-color;
    white-space: nowrap;

    align-self: flex-end;
    text-align: right;
    margin-top: -27px;
    line-height: 1.4;
    position: relative;
    z-index: 1;
    float: right;
    clear: none;

    @media #{$large-mobile} {
        float: none;
        position: relative;
        width: 100%;
        background-color: $heading-color;
        margin-top: 10px;
        color: $white;
        padding: 18px 30px;
        line-height: 1.2;
        text-align: center;

        &:hover {
            background-color: $brand-color;
            color: $white;
        }
    }
}

// shipping Methods
.shipping-methods {
    & li {
        margin-top: 0px;

        &:first-child {
            margin-top: 0;
        }

        & label {
            color: $body-color;
            font-weight: 400;

            & .price {
                color: $heading-color;
            }
        }
    }
}

// Cart Totals
.cart-totals {
    background-color: #f9f9f9;
    padding: 30px 30px 0;
    position: relative;
    position: -webkit-sticky;
    position: sticky;
    top: calc(30px + 100px + 0);

    &__title {
        font-size: 24px;
        font-weight: 600;
        line-height: 1.2;
        color: $heading-color;
        margin-bottom: 20px;
    }
    &__table {
    }
    &__checkout {
        margin-left: -30px;
        margin-right: -30px;
        text-align: center;

        & a {
            display: block;
            cursor: pointer;
            padding: 20px 30px;
            transition: $transition-base;
            border: 0;
            line-height: 1;
            font-size: 20px;
            font-weight: 400;
            background-color: $heading-color;
            color: $white;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 16px;
            }

            &:hover {
                background-color: $brand-color;
                color: $white;
            }
        }
    }

    & .table {
        margin-bottom: 0;

        & tbody {
            & tr {
                display: flex;
                justify-content: space-between;

                & th,
                & td {
                    font-size: 18px;
                    font-weight: 600;
                    color: $heading-color;
                    vertical-align: top;
                    padding: 5px 0;
                    border: 0;

                    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                        font-size: 16px;
                    }

                    & strong {
                        font-weight: 400;
                    }
                }
            }
        }
    }

    & .order-total {
        border-top: 1px solid #dbdbdb;
        padding-top: 10px;
        margin-top: 10px;
    }
}
