/*----------------------------------------*/
/*  02.10 - Blog CSS
/*----------------------------------------*/

// Blog section
.blog-section {
    background-color: #fff5ed;
}

// Blog Wrapper
.blog-wrapper {
    padding-top: 30px;

    @media #{$laptop-device, $desktop-device} {
        padding-top: 20px;
    }
    @media #{$tablet-device, $large-mobile} {
        padding-top: 10px;
    }
}

// Blog Item
.blog-item {
    margin-top: 30px;
    position: relative;

    &__image {
        overflow: hidden;

        & a {
            display: block;
            position: relative;

            &::after {
                position: absolute;
                content: "";
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    180deg,
                    rgba($black, 0) 10%,
                    rgba($black, 0.7) 100%
                );
                z-index: 1;
            }
        }
        & img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: $transition-base;
        }
    }
    &__content {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        z-index: 2;
        padding: 60px;

        @media #{$laptop-device, $desktop-device} {
            padding: 30px;
        }
        @media #{$tablet-device, $large-mobile} {
            padding: 40px;
        }
        @media #{$small-mobile} {
            padding: 20px;
        }
    }

    &__inner {
        overflow: hidden;
    }
    &__title {
        font-size: 46px;
        font-weight: 400;
        font-family: $headings-font-family-02;
        line-height: 1.3;
        margin-bottom: 15px;
        color: $white;
        transition: $transition-base;

        @media #{$laptop-device, $desktop-device} {
            font-size: 36px;
        }
        @media #{$tablet-device, $large-mobile} {
            font-size: 28px;
        }

        @media #{$small-mobile} {
            font-size: 22px;
        }

        & a {
            color: inherit;
        }
    }
    &__meta {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;

        & li {
            display: flex;

            &:not(:first-child)::before {
                content: "•";
                margin-left: 0.3rem;
                margin-right: 0.3rem;
                color: $white;
                line-height: 1;
            }

            & span {
                color: $white;
                font-size: 16px;
                text-transform: capitalize;
                line-height: 1;
                display: block;

                @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                    font-size: 14px;
                }
            }

            & a {
                transition: $transition-base;
            }
        }
    }
    &__btn-wrap {
        margin-bottom: -60px;
        transition: $transition-base;
        opacity: 0;
    }
    &__btn {
        font-size: 16px;
        line-height: 1;
        padding: 15px 30px;
        border: 1px solid $white;
        background-color: transparent;
        color: $white;
        transition: $transition-base;

        @media #{$laptop-device, $desktop-device, $small-mobile} {
            padding: 12px 25px;
            font-size: 14px;
        }

        &:hover {
            background-color: #b28686;
            border-color: #b28686;
            color: $white;
        }
    }
    &__play {
        background-color: #ffffff;
        color: $heading-color;
        font-size: 12px;
        width: 54px;
        height: 54px;
        border-radius: 50%;
        position: absolute;
        top: 70%;
        left: 88%;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 3;

        @media #{$laptop-device, $desktop-device} {
            width: 46px;
            height: 46px;
            font-size: 12px;
        }

        & svg {
            width: 1em;
            height: 1em;
            display: inline-block;
            vertical-align: middle;
        }
    }

    &:hover {
        & .blog-item {
            &__image {
                & img {
                    width: 100%;
                    transform: scale(1.05);
                }
            }
            &__btn-wrap {
                margin-bottom: 0;
                opacity: 1;
            }
        }
    }
}

// Blog Item 02
.blog-item-2 {
    &__image {
        overflow: hidden;

        & a {
            display: block;
            position: relative;
            padding-bottom: 55%;
        }
        & img {
            width: 100%;
            transition: $transition-base;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
    &__content {
        margin-top: 30px;

        @media #{$tablet-device} {
            margin-top: 20px;
        }

        &--meta {
            display: flex;
            flex-wrap: wrap;

            & li {
                display: flex;

                &:not(:first-child)::before {
                    content: "•";
                    margin-left: 0.3rem;
                    margin-right: 0.3rem;
                    color: $body-color;
                    line-height: 1;
                }

                & span {
                    color: $body-color;
                    font-size: 12px;
                    line-height: 1;
                }

                & a {
                    transition: $transition-base;

                    &:hover {
                        color: $brand-color;
                    }
                }
            }
        }
        &--title {
            font-size: 36px;
            font-weight: 400;
            line-height: 1;
            margin: 20px 0 15px 0;
            color: $heading-color;
            transition: $transition-base;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                font-size: 26px;
                margin: 15px 0 10px 0;
            }
            @media #{$large-mobile} {
                font-size: 24px;
                margin: 15px 0 10px 0;
            }

            & a {
                color: inherit;
            }

            &:hover {
                color: $brand-color;
            }
        }
        &--description {
            color: $body-color;
            font-size: 18px;
            font-weight: 400;
            line-height: 1.6;
            margin-bottom: -8px;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 16px;
                margin-bottom: -4px;
            }
        }
        &--btn {
            font-size: 16px;
            font-weight: 400;
            line-height: 48px;
            background-color: $white;
            color: $heading-color;
            padding: 0 70px;
            border: 1px solid $heading-color;

            margin-top: 40px;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                margin-top: 20px;
                padding: 0 30px;
            }

            &:hover {
                background-color: $heading-color;
                color: $white;
            }
        }
    }

    &:hover {
        & .blog-item-3 {
            &__image {
                & img {
                    width: 100%;
                    transform: scale(1.05);
                }
            }
        }
    }
}

// Blog Active
.blog-active {
    position: relative;

    & .swiper-button-next {
        right: 5%;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            right: -20px;
        }
        @media #{$large-mobile} {
            right: -15px;
        }
    }
    & .swiper-button-prev {
        left: 5%;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            left: -20px;
        }
        @media #{$large-mobile} {
            left: -15px;
        }
    }
}

// Related container
.related-container {
    max-width: 1720px;
}

// Related Blog Row
.related-blog-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
    margin-right: -30px;
    padding-top: 30px;

    @media #{$laptop-device, $desktop-device, $tablet-device} {
        margin-left: -15px;
        margin-right: -15px;
        padding-top: 10px;
    }
    @media #{$large-mobile} {
        padding-top: 0;
    }
}

// Related Blog col
.related-blog-col {
    width: calc(100% / 3);
    padding: 0 30px;

    @media #{$laptop-device, $desktop-device, $tablet-device} {
        padding: 0 15px;
    }

    @media #{$large-mobile} {
        width: 100%;
    }
}

// Related Blog Item
.related-blog-item {
    margin-top: 30px;

    &__image {
        & a {
            display: block;
            padding-bottom: 60%;
            position: relative;
            overflow: hidden;

            & img {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
                transition: $transition-base;
            }
        }
    }
    &__content {
        margin-top: 30px;
    }
    &__category {
        display: flex;
        flex-wrap: wrap;

        & li {
            font-size: 12px;
            font-weight: 400;
            text-transform: uppercase;
            line-height: 1;
            position: relative;
            color: $brand-color;

            &:not(:first-child) {
                &::before {
                    content: "•";
                }
            }

            & a {
            }
        }
    }
    &__title {
        font-size: 32px;
        font-family: $headings-font-family-02;
        font-weight: 400;
        line-height: 1;
        margin: 10px 0 20px;
        color: $heading-color;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 24px;
        }
    }
    &__meta {
        display: flex;
        flex-wrap: wrap;

        & li {
            font-size: 14px;
            line-height: 1.13;
            color: $body-color;
            position: relative;

            &:not(:first-child) {
                &::before {
                    content: "•";
                    margin-left: 5px;
                    margin-right: 2px;
                }
            }

            & a {
            }
        }
    }

    &:hover {
        & .related-blog-item {
            &__image {
                & img {
                    transform: scale(1.05);
                }
            }
            &__content {
                transform: scale(1);
                opacity: 1;
                visibility: visible;
            }
        }
    }
}
