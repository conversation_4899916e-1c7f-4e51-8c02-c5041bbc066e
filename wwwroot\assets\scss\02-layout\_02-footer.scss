/*----------------------------------------*/
/*  02.02 - Footer CSS
/*----------------------------------------*/

// Footer Section
.footer-section {
    @media #{$large-mobile} {
        padding-bottom: 50px;
    }

    &.footer-border {
        border-top: 1px solid #ccc;
    }
}

// Footer Main
.footer-main {
    padding-top: 35px;
    padding-bottom: 60px;
    display: flex;
    flex-wrap: wrap;
}
// Footer Padding
.footer-padding {
    padding-top: 30px;
    padding-bottom: 70px;

    @media #{$tablet-device, $large-mobile} {
        padding-top: 10px;
        padding-bottom: 50px;
    }
}

// Footer Col
.footer-col-1 {
    width: 25%;

    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
        width: 100%;
    }
}
.footer-col-2 {
    width: 50%;

    @media #{$laptop-device, $desktop-device} {
        width: 64%;
    }
    @media #{$tablet-device, $large-mobile} {
        width: 100%;
    }
}
.footer-col-3 {
    width: 25%;

    @media #{$laptop-device, $desktop-device} {
        width: 36%;
    }
    @media #{$tablet-device, $large-mobile} {
        width: 100%;
    }
}

// Footer About
.footer-about {
    max-width: 340px;
    width: 100%;
    padding-top: 40px;

    & > * {
        padding: calc(20px / 2) 0;

        &:first-child {
            padding-top: 0;
        }
        &:last-child {
            padding-bottom: 0;
        }
    }

    &__logo {
        & img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }
    }
    & p {
        color: #717171;
        font-size: 18px;
        font-weight: 400;
        line-height: 1.55;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 16px;
        }
    }
}

// Footer Title
.footer-title {
    color: $heading-color;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}
// Footer Title
.footer-title-2 {
    color: $heading-color;
    font-size: 24px;
    font-weight: 700;
    font-family: $headings-font-family-02;
    letter-spacing: 1px;

    @media #{$desktop-device} {
        font-size: 20px;
    }
}

// Footer Link
.footer-link {
    display: flex;
    flex-wrap: wrap;

    @media #{$tablet-device} {
        justify-content: space-between;
    }

    & > * {
        width: 33.33%;

        @media #{$large-mobile} {
            width: 50%;
        }

        &:first-child {
            @media #{$small-mobile} {
                width: 100%;
            }
        }
    }

    &__wrapper {
        padding-top: 40px;
    }

    &__list {
        margin-top: 25px;

        & li {
            &:not(:first-child) {
                margin-top: calc(10px / 2);
            }
            &:not(:last-child) {
                padding-bottom: calc(10px / 2);
            }

            & span,
            & a {
                font-size: 16px;
                line-height: 1.56;
                color: #969696;
                transition: $transition-base;
                white-space: nowrap;

                @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                    font-size: 16px;
                }
            }
            & span {
                white-space: normal;
            }

            &:hover {
                & a {
                    color: $heading-color;
                }
            }
        }
    }
}

// Footer Newsletter
.footer-newsletter {
    padding-top: 40px;

    &__form {
        padding-top: 25px;

        & > * {
            margin-top: 20px;

            &:first-child {
                margin-top: 0;
            }
        }

        & p {
            color: #717171;
        }
    }
    &__input {
        position: relative;

        & input {
            padding: 10px 100px 10px 15px;
            font-size: 14px;
            line-height: 20px;
            border: 1px solid $border-color;
            width: 100%;
            background: none;
        }
        & button {
            font-size: 14px;
            font-weight: 700;
            line-height: 20px;
            color: $body-color;
            padding: 8px 15px 8px 15px;
            border: 0;
            border-left: 1px solid #cbcbcb;
            background: none;
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            transition: $transition-base;

            &:hover {
                color: $heading-color;
            }
        }
    }
    &__social {
        display: flex;
        gap: 15px;

        & li {
            & a {
                font-size: 18px;
                color: $body-color;
            }
        }
    }
}

// Footer CopyRight
.footer-copyright {
    border-top: 1px solid #cecece;
    height: 62px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    @media #{$large-mobile} {
        height: auto;
        padding: 10px 0;
    }

    & P {
        color: #717171;
        font-size: 14px;
        font-weight: 400;
        text-transform: capitalize;

        & a {
            transition: $transition-base;

            &:hover {
                color: $brand-color;
            }
        }
    }

    & ul {
        @media #{$large-mobile} {
            margin-top: 10px;
        }
        @media #{$small-mobile} {
            flex-wrap: wrap;
        }
        & li {
            margin-right: calc(70px / 2);
            margin-left: calc(70px / 2);

            @media #{$desktop-device} {
                margin-right: calc(60px / 2);
                margin-left: calc(60px / 2);
            }
            @media #{$tablet-device, $large-mobile} {
                margin-right: calc(30px / 2);
                margin-left: calc(30px / 2);
            }

            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }

            & a {
                font-size: 18px;
                font-weight: 400;
                line-height: 1.6;
                color: #717171;
                transition: $transition-base;
                white-space: nowrap;

                @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                    font-size: 16px;
                }
            }
            &:hover {
                & a {
                    color: $brand-color;
                }
            }
        }
    }
}

// Footer Dark
.footer-dark {
    background-color: #1d1d1d;

    & .footer-title {
        color: $white;
    }

    & .footer-about {
        & p {
            color: #969696;
        }
    }

    & .footer-link {
        &__list {
            & li {
                &:hover {
                    & a {
                        color: $white;
                    }
                }
            }
        }
    }

    & .footer-newsletter {
        &__form {
            & p {
                color: #969696;
            }
        }
        &__input {
            & input {
                border-color: #535353;
            }
            & button {
                border-color: #535353;
                color: #969696;

                &:hover {
                    color: $white;
                }
            }
        }
        &__social {
            & li {
                & a {
                    color: #969696;
                }
            }
        }
    }
}
