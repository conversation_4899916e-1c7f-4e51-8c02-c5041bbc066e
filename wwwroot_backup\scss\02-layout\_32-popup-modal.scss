/*----------------------------------------*/
/*  02.32 - Popup Modal CSS
/*----------------------------------------*/

// Popup Modal
.popup-modal {
    position: fixed;
    height: 100%;
    width: 100%;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: all;

    z-index: 999;
    user-select: none;
    pointer-events: all;

    opacity: 0;
    visibility: hidden;
    transition: $transition-base;

    &.open {
        opacity: 1;
        visibility: visible;

        & .popup-modal-content {
            animation: ModalFadeInUp 0.5s ease-in-out both;
        }
    }
    &.close {
        transition-delay: 0.5s;

        & .popup-modal-content {
            animation: ModalFadeInDown 0.5s ease-in-out both;
        }
    }
}

.popup-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(23, 23, 23, 0.73);
    opacity: 0;
    visibility: hidden;
    transition: $transition-base;

    &.open {
        opacity: 1;
        visibility: visible;
    }
    &.close {
        transition-delay: 0.5s;
    }
}

@keyframes ModalFadeInUp {
    0% {
        opacity: 0;
        transform: translate3d(0, 50px, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
@keyframes ModalFadeInDown {
    0% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
    100% {
        opacity: 0;
        transform: translate3d(0, -50px, 0);
    }
}

// Popup Modal Content
.popup-modal-content {
    background-color: $white;
    width: 1170px;
    height: auto;
    position: relative;
    z-index: 99;
    pointer-events: all;

    @media #{$laptop-device, $desktop-device} {
        width: 800px;
    }
    @media #{$tablet-device} {
        width: 700px;
    }
    @media #{$large-mobile} {
        width: 320px;
    }
}

// Popup Modal Row
.popup-modal-row {
    display: flex;
    flex-wrap: wrap;
}

// Popup Modal Col 1
.popup-modal-col-1 {
    width: 60%;
    flex: 0 0 auto;

    @media #{$large-mobile} {
        width: 100%;
    }
}

// Popup Modal Col 2
.popup-modal-col-2 {
    width: 40%;
    flex: 0 0 auto;

    @media #{$large-mobile} {
        width: 100%;
    }
}

// Popup Discount Content
.popup-discount-content {
    padding: 70px 70px 50px;

    @media #{$laptop-device, $desktop-device} {
        padding: 50px 50px 30px;
    }
    @media #{$tablet-device} {
        padding: 30px;
    }
    @media #{$large-mobile} {
        padding: 20px;
    }

    &__title {
        color: $heading-color;
        font-size: 46px;
        font-weight: 600;
        line-height: 1;
        margin-top: 60px;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 32px;
            margin-top: 30px;
        }
        @media #{$large-mobile} {
            font-size: 26px;
            margin-top: 0;
        }
    }
    &__sub-title {
        font-size: 22px;
        color: $body-color;
        line-height: 1;
        margin-top: 5px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 16px;
        }
    }
    &__form {
        margin: 50px 0 20px;
        position: relative;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            margin: 30px 0 20px;
        }
        @media #{$large-mobile} {
            margin: 20px 0;
        }

        & input {
            padding: 0 110px 0 20px;
            color: $body-color;
            font-size: 18px;
            height: 58px;
            width: 100%;
            border: 1px solid #cbcbcb;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                height: 50px;
                font-size: 16px;
            }
        }
        & button {
            position: absolute;
            top: 0;
            right: 0;
            height: 58px;
            font-size: 18px;
            font-weight: 600;
            background: none;
            border: 0;
            color: $heading-color;
            padding: 0 20px;
            transition: $transition-base;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                height: 50px;
                font-size: 16px;
            }

            &:hover {
                color: $brand-color;
            }
        }
    }
    &__text {
        color: #b4b4b4;
        font-size: 16px;
        line-height: 1.4;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            font-size: 13px;
        }
    }
    &__wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        margin-top: 70px;

        @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
            margin-top: 30px;
        }
    }
    &__dont-show {
        & a {
            font-size: 18px;
            color: $body-color;
            line-height: 1.2;
            transition: $transition-base;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                font-size: 14px;
            }
            @media #{$large-mobile} {
                font-size: 15px;
            }

            &:hover {
                color: $heading-color;
            }
        }
    }
    &__social {
        display: flex;
        margin-left: -8px;
        margin-right: -8px;

        @media #{$tablet-device} {
            margin-left: -5px;
            margin-right: -5px;
        }

        & li {
            padding: 0 8px;

            @media #{$tablet-device} {
                padding: 0 5px;
            }

            & a {
                font-size: 15px;
                width: 39px;
                height: 39px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: $white;
                color: $heading-color;
                border-radius: 50%;
                box-shadow: 0 0 10px 2px rgba($black, 0.05);
                transition: $transition-base;

                @media #{$tablet-device} {
                    width: 32px;
                    height: 32px;
                }
                @media #{$large-mobile} {
                    width: 34px;
                    height: 34px;
                }

                &:hover {
                    background-color: $heading-color;
                    color: $white;
                }
            }
        }
    }
}

// Popup Discount Background
.popup-discount-background {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    height: 100%;

    @media #{$large-mobile} {
        height: 220px;
        background-position: top center;
    }
}

// Popup Close
.popup-close {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 9;

    &__btn {
        font-size: 24px;
        padding: 0;
        border: 0;
        background: none;
        color: $white;

        & svg {
            fill: currentColor;
            height: 1em;
            width: 1em;
        }
    }
}
