@model TabakGokturk.Entity.SKU

@{
    ViewData["Title"] = "<PERSON><PERSON>ü<PERSON>";
}

<h1><PERSON><PERSON><PERSON><PERSON></h1>

<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Edit" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="Guid" />
            <div class="form-group">
                <label asp-for="ProductNumber" class="control-label"></label>
                <input asp-for="ProductNumber" class="form-control" />
                <span asp-validation-for="ProductNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Title" class="control-label"></label>
                <input asp-for="Title" class="form-control" />
                <span asp-validation-for="Title" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Description" class="control-label"></label>
                <textarea asp-for="Description" class="form-control" rows="5"></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Price" class="control-label"></label>
                <input asp-for="Price" class="form-control" />
                <span asp-validation-for="Price" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="OldPrice" class="control-label"></label>
                <input asp-for="OldPrice" class="form-control" />
                <span asp-validation-for="OldPrice" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Image" class="control-label"></label>
                @if (!string.IsNullOrEmpty(Model.Image))
                {
                    <div class="mb-2">
                        <img src="@Model.Image" alt="@Model.Title" style="max-width: 200px;" />
                    </div>
                }
                <input asp-for="Image" class="form-control" />
                <span asp-validation-for="Image" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Category" class="control-label"></label>
                <input asp-for="Category" class="form-control" />
                <span asp-validation-for="Category" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AvailableItems" class="control-label"></label>
                <input asp-for="AvailableItems" class="form-control" />
                <span asp-validation-for="AvailableItems" class="text-danger"></span>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="PatchEnable" /> @Html.DisplayNameFor(model => model.PatchEnable)
                </label>
            </div>
            <div class="form-group">
                <label asp-for="PatchText" class="control-label"></label>
                <input asp-for="PatchText" class="form-control" />
                <span asp-validation-for="PatchText" class="text-danger"></span>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="IsSingleSKU" /> @Html.DisplayNameFor(model => model.IsSingleSKU)
                </label>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="Enabled" /> @Html.DisplayNameFor(model => model.Enabled)
                </label>
            </div>
            <div class="form-group">
                <label asp-for="ParentId" class="control-label">Üst Ürün</label>
                <select asp-for="ParentId" asp-items="ViewBag.ParentSKUs" class="form-control">
                    <option value="">-- Seçiniz --</option>
                </select>
                <span asp-validation-for="ParentId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Kaydet" class="btn btn-primary" />
                <a asp-action="Index" class="btn btn-secondary">Listeye Dön</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
