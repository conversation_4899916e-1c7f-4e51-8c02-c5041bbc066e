@model SKUFormViewModel

@{
    ViewData["Title"] = "<PERSON><PERSON>";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5 pt-4">
    <div class="card shadow-sm">
        <div class="card-header">
            <h3 class="mb-0"><PERSON><PERSON></h3>
        </div>
        <div class="card-body">
            <form asp-action="Create" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                <div class="mb-3">
                    <label asp-for="SKU.ProductNumber" class="form-label"></label>
                    <input asp-for="SKU.ProductNumber" class="form-control" />
                    <span asp-validation-for="SKU.ProductNumber" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.Name" class="form-label"></label>
                    <input asp-for="SKU.Name" class="form-control" />
                    <span asp-validation-for="SKU.Name" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.Title" class="form-label"></label>
                    <input asp-for="SKU.Title" class="form-control" />
                    <span asp-validation-for="SKU.Title" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.Description" class="form-label"></label>
                    <textarea asp-for="SKU.Description" class="form-control" rows="5"></textarea>
                    <span asp-validation-for="SKU.Description" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.Price" class="form-label"></label>
                    <input asp-for="SKU.Price" class="form-control" />
                    <span asp-validation-for="SKU.Price" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.OldPrice" class="form-label"></label>
                    <input asp-for="SKU.OldPrice" class="form-control" />
                    <span asp-validation-for="SKU.OldPrice" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.Image" class="form-label"></label>
                    <input asp-for="SKU.Image" class="form-control" />
                    <span asp-validation-for="SKU.Image" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.Category" class="form-label"></label>
                    <select asp-for="SKU.Category" asp-items="Model.Categories ?? Enumerable.Empty<SelectListItem>()" class="form-control"></select>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.AvailableItems" class="form-label"></label>
                    <input asp-for="SKU.AvailableItems" class="form-control" />
                    <span asp-validation-for="SKU.AvailableItems" class="text-danger"></span>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" asp-for="SKU.PatchEnable" />
                    <label class="form-check-label" asp-for="SKU.PatchEnable">@Html.DisplayNameFor(model => model.SKU.PatchEnable)</label>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.PatchText" class="form-label"></label>
                    <input asp-for="SKU.PatchText" class="form-control" />
                    <span asp-validation-for="SKU.PatchText" class="text-danger"></span>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" asp-for="SKU.IsSingleSKU" />
                    <label class="form-check-label" asp-for="SKU.IsSingleSKU">@Html.DisplayNameFor(model => model.SKU.IsSingleSKU)</label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" asp-for="SKU.Enabled" />
                    <label class="form-check-label" asp-for="SKU.Enabled">@Html.DisplayNameFor(model => model.SKU.Enabled)</label>
                </div>
                <div class="mb-3">
                    <label asp-for="SKU.ParentId" class="form-label">Üst Ürün</label>
                    <select asp-for="SKU.ParentId" asp-items="Model.ParentSKUs ?? Enumerable.Empty<SelectListItem>()" class="form-control"></select>
                </div>
                <div class="mb-3">
                    <input type="submit" value="Kaydet" class="btn btn-primary" />
                    <a asp-action="Index" class="btn btn-secondary ms-2">Listeye Dön</a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
