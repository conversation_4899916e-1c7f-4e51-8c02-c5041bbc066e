@{
    ViewData["Title"] = "Ürün Detayı - Lüks Tabak";
    var productId = ViewBag.ProductId;

    // Gerçek bir uygulamada, bu bilgiler veritabanından gelecektir
    var productName = "Premium Peynir Tabağı";
    var productPrice = "249.99₺";
    var productDescription = "Özenle seçilmiş yerli ve yabancı peynirlerden oluşan özel tasarım tabaklarımız, sofranıza lüks bir dokunuş katacak. İçerisinde Ezine beyaz peynir, Kars kaşarı, Fransız brie, İtalyan parmesan, İsviçre emmental ve Hollanda gouda peyniri bulunmaktadır.";
    var productOldPrice = ""; // İndirimli ürünler için eski fiyat
    var productStock = "Stokta"; // Ürün stok durumu
    var productSku = "PEYNIR-001"; // Ürün stok kodu
    var productRating = 5; // Ürün puanı
    var productReviewCount = 12; // Ürün yorum sayısı
    var productGallery = new string[] {
        "~/assets/images/products/cheese-board-1.png",
        "~/assets/images/products/cheese-board-1.png",
        "~/assets/images/products/cheese-board-1.png"
    };

    // Ürün ID'sine göre bilgileri değiştir
    if (productId == 2) {
        productName = "Klasik Şarküteri Tabağı";
        productPrice = "199.99₺";
        productDescription = "Klasik şarküteri tabağımız, en sevilen şarküteri ürünlerini bir araya getiriyor. İçerisinde dana jambon, hindi füme, salam çeşitleri, pastırma ve sucuk bulunmaktadır. Özel soslarla servis edilir.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Stokta";
        productSku = "SARKUTERI-001";
        productRating = 4;
        productReviewCount = 8;
    }
    else if (productId == 3) {
        productName = "Özel Tasarım Peynir Tabağı";
        productPrice = "299.99₺";
        productDescription = "Özel tasarım peynir tabağımız, gurme damak zevkine hitap eden özel peynirlerden oluşmaktadır. İçerisinde Roquefort, Camembert, Gorgonzola, Cheddar, Manchego ve Tulum peyniri bulunmaktadır. Kuru meyveler ve kuruyemişlerle servis edilir.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Stokta";
        productSku = "PEYNIR-002";
        productRating = 5;
        productReviewCount = 15;
    }
    else if (productId == 4) {
        productName = "Lüks Şarküteri Tabağı";
        productPrice = "349.99₺";
        productOldPrice = "399.99₺";
        productDescription = "Lüks şarküteri tabağımız, en kaliteli ve özel şarküteri ürünlerini bir araya getiriyor. İçerisinde İtalyan prosciutto, İspanyol chorizo, Fransız saucisson, Alman schwarzwälder schinken ve Türk pastırması bulunmaktadır. Özel soslar ve garnitürlerle servis edilir.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Sınırlı Sayıda";
        productSku = "SARKUTERI-002";
        productRating = 5;
        productReviewCount = 22;
    }
    else if (productId == 5) {
        productName = "İtalyan Peynir Tabağı";
        productPrice = "279.99₺";
        productDescription = "İtalyan peynir tabağımız, İtalya'nın en meşhur peynirlerini bir araya getiriyor. İçerisinde Parmigiano Reggiano, Pecorino Romano, Gorgonzola, Taleggio, Mozzarella ve Ricotta bulunmaktadır. İtalyan zeytinleri ve kuru meyvelerle servis edilir.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Stokta";
        productSku = "PEYNIR-003";
        productRating = 4;
        productReviewCount = 7;
    }
    else if (productId == 6) {
        productName = "Fransız Şarküteri Tabağı";
        productPrice = "329.99₺";
        productDescription = "Fransız şarküteri tabağımız, Fransa'nın en meşhur şarküteri ürünlerini bir araya getiriyor. İçerisinde Jambon de Paris, Saucisson sec, Rillettes, Pâté de campagne ve Andouille bulunmaktadır. Fransız hardalı ve kornişonlarla servis edilir.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Stokta";
        productSku = "SARKUTERI-003";
        productRating = 4;
        productReviewCount = 9;
    }
    else if (productId == 7) {
        productName = "Karışık Lüks Tabak";
        productPrice = "449.99₺";
        productDescription = "Karışık lüks tabağımız, en kaliteli peynir ve şarküteri ürünlerini bir araya getiriyor. Özel seçilmiş yerli ve yabancı peynirler, premium şarküteri ürünleri, kuru meyveler ve kuruyemişlerle servis edilir. Özel davetleriniz için mükemmel bir seçim.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Sınırlı Sayıda";
        productSku = "KARISIK-001";
        productRating = 5;
        productReviewCount = 18;
    }
    else if (productId == 8) {
        productName = "Türk Peynir Tabağı";
        productPrice = "229.99₺";
        productDescription = "Türk peynir tabağımız, Anadolu'nun en lezzetli peynirlerini bir araya getiriyor. İçerisinde Ezine beyaz peynir, Kars kaşarı, İzmir tulum, Erzincan tulum, dil peyniri ve örgü peyniri bulunmaktadır. Zeytin, domates ve salatalıkla servis edilir.";
        // Ana görsel gallery'nin ilk öğesi olarak kullanılır
        productGallery = new string[] {
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png",
            "~/assets/images/products/cheese-board-1.png"
        };
        productStock = "Stokta";
        productSku = "PEYNIR-004";
        productRating = 4;
        productReviewCount = 11;
    }
}

<!-- Breadcrumbs Start -->
<div class="single-breadcrumbs">
    <div class="container-fluid custom-container">
        <ul class="single-breadcrumbs-list">
            <li><a asp-controller="Home" asp-action="Index">Ana Sayfa</a></li>
            <li><a asp-controller="Product" asp-action="Index">Ürünlerimiz</a></li>
            @if (productId == 1 || productId == 3 || productId == 5 || productId == 8)
            {
                <li><a asp-controller="Product" asp-action="CheeseBoards">Peynir Tabakları</a></li>
            }
            else if (productId == 2 || productId == 4 || productId == 6)
            {
                <li><a asp-controller="Product" asp-action="DelicatessenBoards">Şarküteri Tabakları</a></li>
            }
            else
            {
                <li><a href="#">Özel Koleksiyon</a></li>
            }
            <li><span>@productName</span></li>
        </ul>
    </div>
</div>
<!-- Breadcrumbs End -->

<!-- Product Single Start -->
<div class="product-single-section section-padding-2">
    <div class="container-fluid custom-container">
        <!-- Product Single Wrapper Start -->
        <div class="product-single-wrapper">
            <div class="product-single-col-1">
                <!-- Product Single image Start -->
                <div class="product-single-image">
                    <div class="product-single-slide navigation-arrows-style-1">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                @foreach (var image in productGallery)
                                {
                                    <div class="product-single-slide-item swiper-slide">
                                        <img src="@image" alt="@productName" width="694" height="728" />
                                    </div>
                                }
                            </div>
                            <div class="swiper-button-next">
                                <i class="lastudioicon-arrow-right"></i>
                            </div>
                            <div class="swiper-button-prev">
                                <i class="lastudioicon-arrow-left"></i>
                            </div>
                            <div class="product-single-zoom">
                                <div class="zoom">
                                    @foreach (var image in productGallery)
                                    {
                                        <a class="product-glightbox" href="@image" aria-label="zoom"></a>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="product-single-thumb">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                @foreach (var image in productGallery)
                                {
                                    <div class="product-single-thumb-item swiper-slide">
                                        <img src="@image" alt="@productName" width="144" height="155" />
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Product Single image End -->
            </div>

            <div class="product-single-col-2">
                <!-- Product Single content Start -->
                <div class="product-single-content">
                    <h2 class="product-single-content__title">@productName</h2>
                    <div class="product-single-content__price-stock">
                        <div class="product-single-content__price">
                            @if (productId == 4)
                            {
                                <del>399.99₺</del>
                            }
                            <ins>@productPrice</ins>
                        </div>
                        <div class="product-single-content__stock">
                            <span class="stock-icon" aria-label="check-circle">
                                <i class="dlicon ui-1_check-circle-08"></i>
                            </span>
                            <span class="stock-text">@productStock</span>
                        </div>
                    </div>
                    <div class="product-single-content__short-description">
                        <p>@productDescription</p>
                    </div>
                    <div class="product-single-content__add-to-cart-wrapper">
                        <div class="product-single-content__quantity-add-to-cart">
                            <div class="product-single-content__quantity product-quantity">
                                <button type="button" class="decrease" aria-label="delete">
                                    <i class="lastudioicon-i-delete-2"></i>
                                </button>
                                <input class="quantity-input" type="text" value="1" />
                                <button type="button" class="increase" aria-label="add">
                                    <i class="lastudioicon-i-add-2"></i>
                                </button>
                            </div>
                            <button class="product-single-content__add-to-cart btn btn-primary btn-hover-secondary">Sepete Ekle</button>
                        </div>

                        <a href="#" class="product-add-compare">Karşılaştır</a>
                        <a href="#" class="product-add-wishlist">Favorilere Ekle</a>
                    </div>
                    <div class="product-single-content__meta">
                        <div class="product-single-content__meta--item">
                            <div class="label">SKU:</div>
                            <div class="content">@productSku</div>
                        </div>
                        <div class="product-single-content__meta--item">
                            <div class="label">Kategori:</div>
                            <div class="content">
                                @if (productId == 1 || productId == 3 || productId == 5 || productId == 8)
                                {
                                    <a asp-controller="Product" asp-action="CheeseBoards">Peynir Tabakları</a>
                                }
                                else if (productId == 2 || productId == 4 || productId == 6)
                                {
                                    <a asp-controller="Product" asp-action="DelicatessenBoards">Şarküteri Tabakları</a>
                                }
                                else
                                {
                                    <a href="#">Özel Koleksiyon</a>
                                }
                            </div>
                        </div>
                        <div class="product-single-content__meta--item">
                            <div class="label">Etiketler:</div>
                            <div class="content">
                                @if (productId == 1 || productId == 3 || productId == 5 || productId == 8)
                                {
                                    <a href="#">Peynir</a>
                                }
                                else if (productId == 2 || productId == 4 || productId == 6)
                                {
                                    <a href="#">Şarküteri</a>
                                }
                                else
                                {
                                    <a href="#">Karışık</a>
                                }
                                <a href="#">Gurme</a>
                                <a href="#">Lüks</a>
                            </div>
                        </div>
                    </div>
                    <div class="product-single-content__social">
                        <div class="label">Paylaş</div>
                        <ul class="socail-icon">
                            <li>
                                <a href="#" aria-label="facebook">
                                    <i class="lastudioicon-b-facebook"></i>
                                </a>
                            </li>
                            <li>
                                <a href="#" aria-label="twitter">
                                    <i class="lastudioicon-b-twitter"></i>
                                </a>
                            </li>
                            <li>
                                <a href="#" aria-label="linkedin">
                                    <i class="lastudioicon-b-linkedin"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <!-- Product Single content End -->
            </div>
        </div>
        <!-- Product Single Wrapper End -->
    </div>
</div>
<!-- Product Single End -->

<!-- Product Single Tabs Start -->
<div class="product-single-tabs-section section-padding-2">
    <div class="container-fluid custom-container">
        <!-- Product Single Tabs Start -->
        <div class="product-single-tabs">
            <ul class="nav justify-content-center" role="tablist">
                <li role="presentation">
                    <button class="active" data-bs-toggle="pill" data-bs-target="#description" type="button" role="tab" aria-controls="description">
                        Açıklama
                    </button>
                </li>
                <li role="presentation">
                    <button data-bs-toggle="pill" data-bs-target="#additionalInformation" type="button" role="tab" aria-controls="additionalInformation">
                        Ek Bilgiler
                    </button>
                </li>
                <li role="presentation">
                    <button data-bs-toggle="pill" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews">
                        Yorumlar (@productReviewCount)
                    </button>
                </li>
            </ul>

            <div class="tab-content">
                <div class="tab-pane fade show active" id="description">
                    <div class="row justify-content-between align-items-center">
                        <div class="col-lg-6">
                            <!-- Product Single Tab Description Start -->
                            <div class="product-single-tab-description">
                                <!-- Product Single Tab Description Item Start -->
                                <div class="product-single-tab-description-item">
                                    <h4 class="product-single-tab-description-item__title">
                                        + ÜRÜN DETAYLARI
                                    </h4>
                                    <p>@productDescription</p>
                                </div>
                                <!-- Product Single Tab Description Item End -->

                                <!-- Product Single Tab Description Item Start -->
                                <div class="product-single-tab-description-item">
                                    <h4 class="product-single-tab-description-item__title">
                                        + SAKLAMA VE TÜKETİM BİLGİLERİ
                                    </h4>
                                    <p>Tüm ürünlerimiz günlük olarak hazırlanmakta ve en taze şekilde servis edilmektedir. Özel siparişleriniz için lütfen bizimle iletişime geçiniz.</p>
                                    <p>+4 derecede buzdolabında saklanmalıdır. Teslim tarihinden itibaren 2 gün içinde tüketilmelidir.</p>
                                </div>
                                <!-- Product Single Tab Description Item End -->
                            </div>
                            <!-- Product Single Tab Description End -->
                        </div>
                        <div class="col-lg-5">
                            <!-- Product Single Tab Image Start -->
                            <div class="product-single-tab-image">
                                <h5 class="product-single-tab-image__title">
                                    Ürün Tanıtımı
                                </h5>
                                <div class="product-single-tab-image__image">
                                    <a class="glightbox" href="@productGallery[0]"></a>
                                    <img src="@productGallery[0]" alt="@productName" width="320" height="218" loading="lazy" />
                                </div>
                            </div>
                            <!-- Product Single Tab Image End -->
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="additionalInformation">
                    <!-- Product Single Table Start -->
                    <div class="product-single-table">
                        <div class="table-responsive">
                            <table class="table align-middle">
                                <tbody>
                                    <tr>
                                        <th>Ağırlık</th>
                                        <td>
                                            <p>1 kg</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Boyutlar</th>
                                        <td>
                                            <p>30 × 30 × 5 cm</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Servis</th>
                                        <td>
                                            <p>2-4 kişilik</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Saklama Koşulları</th>
                                        <td>
                                            <p>+4 derecede buzdolabında saklanmalıdır</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Tüketim Süresi</th>
                                        <td>
                                            <p>Teslim tarihinden itibaren 2 gün içinde tüketilmelidir</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- Product Single Table End -->
                </div>
                <div class="tab-pane fade" id="reviews">
                    <!-- Product Single Review Start -->
                    <div class="product-single-review">
                        <!-- Product Comment Start -->
                        <div class="product-comment">
                            <h3 class="comment-title">
                                @productReviewCount yorum - @productName
                            </h3>

                            <!-- Comment Items Start -->
                            <ul class="comment-items">
                                <!-- Comment Item Start -->
                                <li class="comment-item">
                                    <div class="comment-item__author">
                                        <img src="~/assets/images/user/user-1.jpg" alt="Kullanıcı" width="90" height="90" loading="lazy" />
                                    </div>
                                    <div class="comment-item__content">
                                        <div class="comment-item__rating">
                                            <span class="star-rating">
                                                <span style="width: 80%;"></span>
                                            </span>
                                        </div>
                                        <p class="comment-item__description">
                                            Mükemmel bir ürün, çok lezzetli ve görsel olarak da çok etkileyici.
                                        </p>
                                        <p class="comment-item__meta">
                                            <strong>Ahmet Yılmaz</strong> - 15 Haziran 2023
                                        </p>
                                    </div>
                                </li>
                                <!-- Comment Item End -->

                                <!-- Comment Item Start -->
                                <li class="comment-item">
                                    <div class="comment-item__author">
                                        <img src="~/assets/images/user/user-2.jpg" alt="Kullanıcı" width="90" height="90" loading="lazy" />
                                    </div>
                                    <div class="comment-item__content">
                                        <div class="comment-item__rating">
                                            <span class="star-rating">
                                                <span style="width: 100%;"></span>
                                            </span>
                                        </div>
                                        <p class="comment-item__description">
                                            Misafirlerimiz çok beğendi, kesinlikle tavsiye ederim.
                                        </p>
                                        <p class="comment-item__meta">
                                            <strong>Ayşe Demir</strong> - 22 Mayıs 2023
                                        </p>
                                    </div>
                                </li>
                                <!-- Comment Item End -->
                            </ul>
                            <!-- Comment Items End -->
                        </div>
                        <!-- Product Comment End -->

                        <!-- Product Comment Form Start -->
                        <div class="product-comment-form">
                            <h3 class="comment-title">
                                Yorum Ekle
                            </h3>

                            <form action="#">
                                <!-- Comment Form Start -->
                                <div class="comment-form">
                                    <div class="comment-form__notes">
                                        E-posta adresiniz yayınlanmayacaktır.
                                    </div>
                                    <div class="comment-form__rating">
                                        <div class="label">
                                            Puanınız *
                                        </div>
                                        <span class="star-rating">
                                            <span style="width: 80%"></span>
                                        </span>
                                    </div>
                                    <!-- Single Form Start -->
                                    <div class="single-form">
                                        <label class="single-form__label">
                                            Yorumunuz *
                                        </label>
                                        <textarea class="single-form__input"></textarea>
                                    </div>
                                    <!-- Single Form Start -->

                                    <div class="row">
                                        <div class="col-md-6">
                                            <!-- Single Form Start -->
                                            <div class="single-form">
                                                <label class="single-form__label">
                                                    Adınız *
                                                </label>
                                                <input type="text" class="single-form__input" />
                                            </div>
                                            <!-- Single Form Start -->
                                        </div>
                                        <div class="col-md-6">
                                            <!-- Single Form Start -->
                                            <div class="single-form">
                                                <label class="single-form__label">
                                                    E-posta *
                                                </label>
                                                <input type="email" class="single-form__input" />
                                            </div>
                                            <!-- Single Form Start -->
                                        </div>
                                    </div>

                                    <!-- Single Form Start -->
                                    <div class="single-form">
                                        <input type="checkbox" name="save" id="save" />
                                        <label class="single-form__label checkbox-label" for="save">
                                            <span></span>
                                            Adımı, e-postamı ve web sitemi bir dahaki sefere yorum yaptığımda bu tarayıcıda kaydet.
                                        </label>
                                    </div>
                                    <!-- Single Form Start -->

                                    <!-- Single Form Start -->
                                    <div class="single-form">
                                        <button class="single-form__btn btn btn-primary btn-hover-secondary" type="submit">
                                            Gönder
                                        </button>
                                    </div>
                                    <!-- Single Form Start -->
                                </div>
                                <!-- Comment Form End -->
                            </form>
                        </div>
                        <!-- Product Comment Form End -->
                    </div>
                    <!-- Product Single Review End -->
                </div>
            </div>
        </div>
        <!-- Product Single Tabs End -->
    </div>
</div>

<!-- Related Product Start -->
<div class="related-product-section section-padding-2">
    <div class="container-fluid custom-container">
        <!-- Related Title Start -->
        <div class="related-title text-center">
            <h2 class="related-title__title">İlgili Ürünler</h2>
        </div>
        <!-- Related Title End -->

        <!-- Related Product Start -->
        <div class="related-product-active swiper-dot-style-1">
            <div class="swiper">
                <div class="swiper-wrapper">
                    <!-- Product Item Start -->
                    <div class="swiper-slide">
                        <div class="single-product js-scroll ShortFadeInUp">
                            <div class="single-product__thumbnail">
                                <div class="single-product__thumbnail--meta-3">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Favorilere Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="heart"><i class="lastudioicon-heart-2"></i></a>
                                </div>
                                <div class="single-product__thumbnail--badge onsale">
                                    Yeni
                                </div>
                                <div class="single-product__thumbnail--holder">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="1">
                                        <img src="~/assets/images/products/cheese-board-1.png" alt="Premium Peynir Tabağı" width="344" height="370" loading="lazy" />
                                    </a>
                                </div>
                                <div class="single-product__thumbnail--meta-2">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Sepete Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="cart"><i class="lastudioicon-shopping-cart-3"></i></a>
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Karşılaştır" data-bs-custom-class="p-meta-tooltip" aria-label="compare"><i class="lastudioicon-ic_compare_arrows_24px"></i></a>
                                    <button data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Hızlı Bakış" data-bs-custom-class="p-meta-tooltip" data-bs-toggle="modal" data-bs-target="#quickView" aria-label="zoom-in">
                                        <i class="lastudioicon-search-zoom-in"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="single-product__info text-center">
                                <div class="single-product__info--tags">
                                    <a asp-controller="Product" asp-action="CheeseBoards">Peynir Tabakları</a>
                                </div>
                                <h3 class="single-product__info--title">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="1">
                                        Premium Peynir Tabağı
                                    </a>
                                </h3>
                                <div class="single-product__info--price">
                                    <ins>249.99₺</ins>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Product Item End -->

                    <!-- Product Item Start -->
                    <div class="swiper-slide">
                        <div class="single-product js-scroll ShortFadeInUp">
                            <div class="single-product__thumbnail">
                                <div class="single-product__thumbnail--meta-3">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Favorilere Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="heart"><i class="lastudioicon-heart-2"></i></a>
                                </div>
                                <div class="single-product__thumbnail--holder">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="2">
                                        <img src="~/assets/images/products/cheese-board-1.png" alt="Klasik Şarküteri Tabağı" width="344" height="370" loading="lazy" />
                                    </a>
                                </div>
                                <div class="single-product__thumbnail--meta-2">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Sepete Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="cart"><i class="lastudioicon-shopping-cart-3"></i></a>
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Karşılaştır" data-bs-custom-class="p-meta-tooltip" aria-label="compare"><i class="lastudioicon-ic_compare_arrows_24px"></i></a>
                                    <button data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Hızlı Bakış" data-bs-custom-class="p-meta-tooltip" data-bs-toggle="modal" data-bs-target="#quickView" aria-label="zoom-in">
                                        <i class="lastudioicon-search-zoom-in"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="single-product__info text-center">
                                <div class="single-product__info--tags">
                                    <a asp-controller="Product" asp-action="DelicatessenBoards">Şarküteri Tabakları</a>
                                </div>
                                <h3 class="single-product__info--title">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="2">
                                        Klasik Şarküteri Tabağı
                                    </a>
                                </h3>
                                <div class="single-product__info--price">
                                    <ins>199.99₺</ins>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Product Item End -->

                    <!-- Product Item Start -->
                    <div class="swiper-slide">
                        <div class="single-product js-scroll ShortFadeInUp">
                            <div class="single-product__thumbnail">
                                <div class="single-product__thumbnail--meta-3">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Favorilere Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="heart"><i class="lastudioicon-heart-2"></i></a>
                                </div>
                                <div class="single-product__thumbnail--holder">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="3">
                                        <img src="~/assets/images/products/cheese-board-1.png" alt="Özel Tasarım Peynir Tabağı" width="344" height="370" loading="lazy" />
                                    </a>
                                </div>
                                <div class="single-product__thumbnail--meta-2">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Sepete Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="cart"><i class="lastudioicon-shopping-cart-3"></i></a>
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Karşılaştır" data-bs-custom-class="p-meta-tooltip" aria-label="compare"><i class="lastudioicon-ic_compare_arrows_24px"></i></a>
                                    <button data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Hızlı Bakış" data-bs-custom-class="p-meta-tooltip" data-bs-toggle="modal" data-bs-target="#quickView" aria-label="zoom-in">
                                        <i class="lastudioicon-search-zoom-in"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="single-product__info text-center">
                                <div class="single-product__info--tags">
                                    <a asp-controller="Product" asp-action="CheeseBoards">Peynir Tabakları</a>
                                </div>
                                <h3 class="single-product__info--title">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="3">
                                        Özel Tasarım Peynir Tabağı
                                    </a>
                                </h3>
                                <div class="single-product__info--price">
                                    <ins>299.99₺</ins>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Product Item End -->

                    <!-- Product Item Start -->
                    <div class="swiper-slide">
                        <div class="single-product js-scroll ShortFadeInUp">
                            <div class="single-product__thumbnail">
                                <div class="single-product__thumbnail--meta-3">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Favorilere Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="heart"><i class="lastudioicon-heart-2"></i></a>
                                </div>
                                <div class="single-product__thumbnail--badge onsale">
                                    İndirimli
                                </div>
                                <div class="single-product__thumbnail--holder">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="4">
                                        <img src="~/assets/images/products/cheese-board-1.png" alt="Lüks Şarküteri Tabağı" width="344" height="370" loading="lazy" />
                                    </a>
                                </div>
                                <div class="single-product__thumbnail--meta-2">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Sepete Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="cart"><i class="lastudioicon-shopping-cart-3"></i></a>
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Karşılaştır" data-bs-custom-class="p-meta-tooltip" aria-label="compare"><i class="lastudioicon-ic_compare_arrows_24px"></i></a>
                                    <button data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Hızlı Bakış" data-bs-custom-class="p-meta-tooltip" data-bs-toggle="modal" data-bs-target="#quickView" aria-label="zoom-in">
                                        <i class="lastudioicon-search-zoom-in"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="single-product__info text-center">
                                <div class="single-product__info--tags">
                                    <a asp-controller="Product" asp-action="DelicatessenBoards">Şarküteri Tabakları</a>
                                </div>
                                <h3 class="single-product__info--title">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="4">
                                        Lüks Şarküteri Tabağı
                                    </a>
                                </h3>
                                <div class="single-product__info--price">
                                    <del>399.99₺</del>
                                    <ins>349.99₺</ins>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Product Item End -->

                    <!-- Product Item Start -->
                    <div class="swiper-slide">
                        <div class="single-product js-scroll ShortFadeInUp">
                            <div class="single-product__thumbnail">
                                <div class="single-product__thumbnail--meta-3">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Favorilere Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="heart"><i class="lastudioicon-heart-2"></i></a>
                                </div>
                                <div class="single-product__thumbnail--badge onsale">
                                    Sınırlı Sayıda
                                </div>
                                <div class="single-product__thumbnail--holder">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="7">
                                        <img src="~/assets/images/products/mixed-board-1.jpg" alt="Karışık Lüks Tabak" width="344" height="370" loading="lazy" />
                                    </a>
                                </div>
                                <div class="single-product__thumbnail--meta-2">
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Sepete Ekle" data-bs-custom-class="p-meta-tooltip" aria-label="cart"><i class="lastudioicon-shopping-cart-3"></i></a>
                                    <a href="#" data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Karşılaştır" data-bs-custom-class="p-meta-tooltip" aria-label="compare"><i class="lastudioicon-ic_compare_arrows_24px"></i></a>
                                    <button data-bs-tooltip="tooltip" data-bs-placement="top" data-bs-title="Hızlı Bakış" data-bs-custom-class="p-meta-tooltip" data-bs-toggle="modal" data-bs-target="#quickView" aria-label="zoom-in">
                                        <i class="lastudioicon-search-zoom-in"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="single-product__info text-center">
                                <div class="single-product__info--tags">
                                    <a href="#">Özel Koleksiyon</a>
                                </div>
                                <h3 class="single-product__info--title">
                                    <a asp-controller="Product" asp-action="Detail" asp-route-id="7">
                                        Karışık Lüks Tabak
                                    </a>
                                </h3>
                                <div class="single-product__info--price">
                                    <ins>449.99₺</ins>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Product Item End -->
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </div>
        <!-- Related Product End -->
    </div>
</div>
<!-- Related Product End -->
