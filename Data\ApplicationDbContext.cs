using Microsoft.EntityFrameworkCore;
using TabakGokturk.Entity;

namespace TabakGokturk.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<SKU> SKUs { get; set; }
        public DbSet<SKUVariant> SKUVariants { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<SKUCategory> SKUCategories { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerAddress> CustomerAddresses { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<OrderStatus> OrderStatuses { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<SKU>()
                .HasOne(s => s.Parent)
                .WithMany(s => s.Child<PERSON>KUs)
                .HasForeignKey(s => s.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SKUVariant>()
                .HasOne(sv => sv.SKU)
                .WithMany(s => s.Variants)
                .HasForeignKey(sv => sv.SKUId);

            modelBuilder.Entity<SKUCategory>()
                .HasOne(sc => sc.SKU)
                .WithMany(s => s.SKUCategories)
                .HasForeignKey(sc => sc.SKUId);

            modelBuilder.Entity<SKUCategory>()
                .HasOne(sc => sc.Category)
                .WithMany(c => c.SKUCategories)
                .HasForeignKey(sc => sc.CategoryId);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.Customer)
                .WithMany(c => c.Orders)
                .HasForeignKey(o => o.CustomerId);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.OrderStatus)
                .WithMany(os => os.Orders)
                .HasForeignKey(o => o.OrderStatusId);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Order)
                .WithMany(o => o.OrderItems)
                .HasForeignKey(oi => oi.OrderId);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.SKU)
                .WithMany(s => s.OrderItems)
                .HasForeignKey(oi => oi.SKUId);

            modelBuilder.Entity<CustomerAddress>()
                .HasOne(ca => ca.Customer)
                .WithMany(c => c.Addresses)
                .HasForeignKey(ca => ca.CustomerId);
        }
    }
}