@charset "UTF-8";
/*-----------------------------------------------------------------------------------

    Template Name: Lu<PERSON> - Candle & Wine Website Template
    Author: Hastech IT
    Author URL: 
    Support: https://hasthemes.com/contact-us/
    Description: 
    Version: 1.0.0

-----------------------------------------------------------------------------------

    CSS INDEX
    ===================

    00 - Helpers (Variables & Mixins)
    01 - Basic (Typography, Common Classes)
        01.01 - Typography
        01.02 - Common Classes
        02.03 - Single Form
    02 - Layout (Header, Footer, Section, Main Container/Wrapper and <PERSON> Header Styles)
        02.01 - Header
        02.02 - Footer
        02.03 - Slider
        02.04 - Banner
        02.05 - Product
        02.06 - Features
        02.07 - Special offer
        02.08 - CountDown
        02.09 - Popular brands
        02.10 - Blog
        02.11 - Instagram
        02.12 - Our Info
        02.13 - About
        02.14 - Category
        02.15 - Product Popular
        02.16 - Gallery
        02.17 - Booking
        02.18 - Client
        02.19 - Call To Action
        02.20 - Testimonial
        02.21 - Contact
        02.22 - Coming Soon
        02.23 - Newsletter
        02.24 - Breadcrumb
        02.25 - Our Team
        02.26 - FAQ’s   
        02.27 - Term Of Use
        02.28 - 404     
        02.29 - Comment  
        02.30 - Cart Sidebar  
        02.31 - Quick View Modal
        02.31 - Popup Modal 
    03 - Pages ()
        03.01 - Shop Page
        03.02 - Product Single
        03.03 - My Account
        30.04 - Log In & Register
        03.05 - Cart
        03.06 - Order Tracking
        03.07 - Wishlist
        03.08 - Compare
        03.09 - Checkout
        03.10 - Thank You
        03.11 - Blog
        03.12 - Blog Single

-----------------------------------------------------------------------------------*/
/*===== 00 - Helpers (Variables) =====*/
/*----------------------------------------*/
/*  01. Helpers CSS
/*----------------------------------------*/
/*===== 01 - Basic (Typography, Common Classes) =====*/
/*----------------------------------------*/
/*  01.01 - Template default CSS
/*----------------------------------------*/
/* Common Style */
*,
*::after,
*::before {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
}

body {
  font-family: "Josefin Sans", sans-serif;
  font-size: 16px;
  font-weight: 400;
  font-style: normal;
  line-height: 1.5;
  overflow-x: hidden;
  color: #494949;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Josefin Sans", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
  color: var(--heading-color);
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  line-height: inherit;
  margin: 0;
}

p {
  margin: 0;
}

a,
button {
  line-height: inherit;
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

*:focus {
  outline: none;
}

button:focus,
a:focus {
  text-decoration: none;
  color: inherit;
  outline: none;
}

a:hover {
  text-decoration: none;
  color: var(--hover-color);
}

button,
input[type=submit] {
  cursor: pointer;
}

img {
  max-width: 100%;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  opacity: 1;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  opacity: 1;
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  opacity: 1;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

hr {
  border-top-width: 2px;
}

/*----------------------------------------*/
/*  01.02 - Common Classes CSS
/*----------------------------------------*/
@media (min-width: 1400px) {
  .custom-container,
.container,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
    max-width: 1526px;
  }
}
@media only screen and (min-width: 1600px), only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .custom-container,
.container-fluid {
    padding: 0 40px;
  }
}
@media only screen and (max-width: 767px) {
  .custom-container,
.container-fluid {
    padding: 0 20px;
  }
}

.home-container {
  max-width: 1720px;
}

.tooltip {
  font-family: "Josefin Sans", sans-serif;
  font-size: 14px;
  line-height: 14px;
}

.tooltip-inner {
  padding: 10px 10px 7px;
  border-radius: 3px;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
}

/* tab Content */
.tab-content .tab-pane {
  display: block;
  overflow: hidden;
  height: 0;
  visibility: hidden;
  max-width: 100%;
  opacity: 0;
}
.tab-content .tab-pane.active {
  height: auto;
  visibility: visible;
  opacity: 1;
  overflow: visible;
}

.view-more-btn {
  color: #b9b9b9;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 0.15em;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin-top: 65px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .view-more-btn {
    margin-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .view-more-btn {
    font-size: 16px;
  }
}
.view-more-btn:hover {
  color: #b28686;
}

.view-more-btn-2 {
  color: #363636;
}
.view-more-btn-2:hover {
  color: #b28686;
}

.related-title__title {
  font-size: 36px;
  font-weight: 600;
  line-height: 1;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .related-title__title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .related-title__title {
    font-size: 24px;
  }
}

.btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2;
  color: #494949;
  background-color: #ffffff;
  border: 2px solid;
  border-radius: 0;
  padding: 14px 33px 13px;
  white-space: nowrap;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .btn {
    padding: 12px 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .btn {
    padding: 10px 25px;
  }
}

.custom-g-1,
.custom-gx-1 {
  --bs-gutter-x: 20px;
}

.custom-g-1,
.custom-gy-1 {
  --bs-gutter-y: 20px;
}

.custom-g-2,
.custom-gx-2 {
  --bs-gutter-x: 40px;
}

.custom-g-2,
.custom-gy-2 {
  --bs-gutter-y: 40px;
}

.section-title {
  position: relative;
  z-index: 1;
  padding: 35px 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title {
    padding: 20px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title {
    padding: 15px 0;
  }
}
@media only screen and (max-width: 767px) {
  .section-title {
    padding: 10px 0;
  }
}
.section-title__title {
  font-size: 66px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  color: #363636;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title__title {
    font-size: 46px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title__title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title__title {
    font-size: 30px;
  }
}
.section-title__shape {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: -1;
}
.section-title__shape img {
  width: auto;
  height: auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title__shape img {
    height: 86px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title__shape img {
    height: 66px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title__shape img {
    height: 50px;
  }
}

.section-title-2 {
  position: relative;
  z-index: 1;
}
.section-title-2__title {
  font-size: 50px;
  font-weight: 500;
  font-family: "Playfair Display", serif;
  color: #363636;
  line-height: 1.1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title-2__title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title-2__title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title-2__title {
    font-size: 24px;
  }
}
.section-title-2__shape {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: -1;
}
.section-title-2__shape img {
  width: auto;
  height: auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title-2__shape img {
    height: 180px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title-2__shape img {
    height: 140px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title-2__shape img {
    height: 100px;
  }
}

.section-title-3 {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.section-title-3__title {
  font-size: 56px;
  font-weight: 400;
  line-height: 1;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title-3__title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title-3__title {
    font-size: 24px;
  }
}
.section-title-3 p {
  font-size: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-title-3 p {
    font-size: 18px;
  }
}

.section-padding {
  padding-top: 80px;
  padding-bottom: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-padding {
    padding-top: 70px;
    padding-bottom: 70px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-padding {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.section-padding-1 {
  padding-top: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-padding-1 {
    padding-top: 70px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-padding-1 {
    padding-top: 60px;
  }
}

.section-padding-2 {
  padding-bottom: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-padding-2 {
    padding-bottom: 70px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-padding-2 {
    padding-bottom: 60px;
  }
}

.section-padding-02 {
  padding-top: 100px;
  padding-bottom: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-padding-02 {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-padding-02 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.section-padding-02-1 {
  padding-top: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-padding-02-1 {
    padding-top: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-padding-02-1 {
    padding-top: 60px;
  }
}

.section-padding-02-2 {
  padding-bottom: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-padding-02-2 {
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .section-padding-02-2 {
    padding-bottom: 60px;
  }
}

.swiper-dot-style-1 {
  position: relative;
}
.swiper-dot-style-1 .swiper-pagination {
  position: relative;
  bottom: 0;
  margin-top: 40px;
  display: flex;
  justify-content: center;
}
.swiper-dot-style-1 .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  border: 1px solid #494949;
  background-color: transparent;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin: 0 5px;
  opacity: 1;
}
.swiper-dot-style-1 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border-color: #363636;
  background-color: #363636;
}

.swiper-dot-style-2 {
  position: relative;
}
.swiper-dot-style-2 .swiper-pagination {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: 65px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .swiper-dot-style-2 .swiper-pagination {
    margin-top: 50px;
  }
}
.swiper-dot-style-2 .swiper-pagination:hover {
  color: #b28686;
}
.swiper-dot-style-2 .swiper-pagination .swiper-pagination-bullet {
  width: 30px;
  height: 8px;
  border-radius: 30px;
  background-color: #d3d3d3;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin: 0 5px;
  opacity: 1;
}
.swiper-dot-style-2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 60px;
  background-color: #395749;
}

.navigation-arrows-style-1 {
  position: relative;
  z-index: 8;
}
.navigation-arrows-style-1 .swiper-button-next, .navigation-arrows-style-1 .swiper-button-prev {
  font-size: 16px;
  font-weight: 700;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: #ffffff;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 0.4;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .navigation-arrows-style-1 .swiper-button-next, .navigation-arrows-style-1 .swiper-button-prev {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .navigation-arrows-style-1 .swiper-button-next, .navigation-arrows-style-1 .swiper-button-prev {
    width: 25px;
    height: 25px;
    line-height: 25px;
  }
}
.navigation-arrows-style-1 .swiper-button-next::after, .navigation-arrows-style-1 .swiper-button-prev::after {
  display: none;
}
.navigation-arrows-style-1 .swiper-button-next svg, .navigation-arrows-style-1 .swiper-button-prev svg {
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
.navigation-arrows-style-1 .swiper-button-next:hover, .navigation-arrows-style-1 .swiper-button-prev:hover {
  opacity: 1;
}
.navigation-arrows-style-1 .swiper-button-next {
  right: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .navigation-arrows-style-1 .swiper-button-next {
    right: 20px;
  }
}
.navigation-arrows-style-1 .swiper-button-prev {
  left: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .navigation-arrows-style-1 .swiper-button-prev {
    left: 20px;
  }
}

.navigation-arrows-style-2 {
  position: relative;
}
.navigation-arrows-style-2 .swiper-button-next, .navigation-arrows-style-2 .swiper-button-prev {
  width: 44px;
  height: 44px;
  line-height: 44px;
  position: relative;
  margin-top: 0;
  border: 1px solid #bababa;
  background-color: #ffffff;
  border-radius: 50%;
  font-size: 16px;
  font-weight: 700;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  left: 0;
  right: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .navigation-arrows-style-2 .swiper-button-next, .navigation-arrows-style-2 .swiper-button-prev {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
}
.navigation-arrows-style-2 .swiper-button-next::after, .navigation-arrows-style-2 .swiper-button-prev::after {
  display: none;
}
.navigation-arrows-style-2 .swiper-button-next svg, .navigation-arrows-style-2 .swiper-button-prev svg {
  width: 1em;
  height: 1em;
}
.navigation-arrows-style-2 .swiper-button-next:hover, .navigation-arrows-style-2 .swiper-button-prev:hover {
  background-color: #363636;
  border: 1px solid #363636;
  color: #ffffff;
}

.bg-home {
  background-color: #ebebe8;
}

.paginations {
  margin-top: 60px;
}

.paginations-list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.paginations-list li {
  margin: 0 5px 5px;
}
.paginations-list li a {
  background-color: transparent;
  color: #494949;
  width: 46px;
  height: 46px;
  line-height: 48px;
  border: 1px solid #c6c6c6;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  text-align: center;
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .paginations-list li a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .paginations-list li a {
    width: 34px;
    height: 34px;
    line-height: 34px;
    font-size: 15px;
  }
}
.paginations-list li a:hover, .paginations-list li a.active {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

.paginations-list-2 {
  display: flex;
  flex-wrap: wrap;
  margin-left: -5px;
  margin-right: -5px;
}
.paginations-list-2 li {
  padding: 0 5px 5px;
}
.paginations-list-2 li a {
  background-color: transparent;
  color: #494949;
  width: 44px;
  height: 44px;
  line-height: 46px;
  border: 1px solid #c6c6c6;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  text-align: center;
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .paginations-list-2 li a {
    width: 38px;
    height: 38px;
    line-height: 38px;
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .paginations-list-2 li a {
    width: 34px;
    height: 34px;
    line-height: 34px;
    font-size: 15px;
  }
}
.paginations-list-2 li a:hover, .paginations-list-2 li a.active {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}

.js-scroll {
  opacity: 0;
}

.scrolled.ShortFadeInDown {
  -webkit-animation: ShortFadeInDown 1s ease-in-out both;
          animation: ShortFadeInDown 1s ease-in-out both;
}
.scrolled.ShortFadeInUp {
  -webkit-animation: ShortFadeInUp 1s ease-in-out both;
          animation: ShortFadeInUp 1s ease-in-out both;
}
.scrolled.ShortFadeInLeft {
  -webkit-animation: ShortFadeInLeft 1s ease-in-out both;
          animation: ShortFadeInLeft 1s ease-in-out both;
}
.scrolled.ShortFadeInRight {
  -webkit-animation: ShortFadeInRight 1s ease-in-out both;
          animation: ShortFadeInRight 1s ease-in-out both;
}

@-webkit-keyframes ShortFadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -50px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes ShortFadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -50px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes ShortFadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 50px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@keyframes ShortFadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 50px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes ShortFadeInLeft {
  0% {
    opacity: 0;
    transform: translate3d(-50px, 0, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@keyframes ShortFadeInLeft {
  0% {
    opacity: 0;
    transform: translate3d(-50px, 0, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes ShortFadeInRight {
  0% {
    opacity: 0;
    transform: translate3d(50px, 0, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@keyframes ShortFadeInRight {
  0% {
    opacity: 0;
    transform: translate3d(50px, 0, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
/*----------------------------------------*/
/*  02.03 - Single Form CSS
/*----------------------------------------*/
.single-form {
  margin-top: 25px;
}
.single-form__label {
  font-size: 16px;
  line-height: 1.3;
  margin-bottom: 10px;
}
.single-form__input {
  width: 100%;
  font-size: 18px;
  line-height: 20px;
  padding: 20px;
  background-color: transparent;
  border: 1px solid #dedede;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: block;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .single-form__input {
    font-size: 16px;
    padding: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .single-form__input {
    font-size: 16px;
    padding: 12px 15px;
  }
}
.single-form__input:focus {
  border-color: #bbb;
}
.single-form__btn {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
.single-form__btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}
.single-form__select {
  background-color: transparent;
  border-radius: 0;
  border: solid 1px #dedede;
  float: none;
  font-size: 18px;
  width: 100%;
  height: auto;
  line-height: 20px;
  outline: none;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .single-form__select {
    font-size: 16px;
    padding: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .single-form__select {
    font-size: 16px;
    padding: 12px 15px;
  }
}
.single-form__select::after {
  border-bottom: 2px solid #888;
  border-right: 2px solid #888;
  height: 8px;
  right: 20px;
  width: 8px;
}
.single-form__select .nice-select-dropdown {
  border-radius: 0;
  width: 100%;
}
.single-form__select .nice-select-search-box {
  padding: 10px;
}
.single-form__select .nice-select-search {
  background-color: #ffffff;
  border: 1px solid #dedede;
  border-radius: 0;
  color: #363636;
  padding: 7px 12px;
  width: 100%;
  min-height: 40px;
  line-height: 30px;
  font-size: 16px;
}
.single-form__select .list {
  border-radius: 0;
}
.single-form__select .option {
  color: #494949;
}
.single-form__select .option:hover, .single-form__select .option.focus, .single-form__select .option.selected.focus {
  background-color: #d68d67;
  color: #ffffff;
}
.single-form select.select2 {
  display: none;
}
.single-form textarea {
  height: 150px;
}
.single-form .checkbox-label {
  margin-bottom: 0;
}
.single-form .checkbox-label span {
  position: relative;
  display: inline-block;
  background-color: #fff;
  border: 1px solid #bbb;
  line-height: 0;
  width: 16px;
  height: 16px;
  margin: -4px 10px 0 0;
  outline: 0;
  text-align: center;
  vertical-align: middle;
  clear: none;
  cursor: pointer;
  -webkit-appearance: none;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-form .checkbox-label span::before {
  position: absolute;
  content: "\ea20";
  font-family: LaStudioIcons;
  font-size: 10px;
  width: 16px;
  height: 16px;
  line-height: 14px;
  text-align: center;
  color: #ffffff;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-form input[type=checkbox] {
  display: none;
}
.single-form input[type=checkbox]:checked + .checkbox-label span {
  background-color: #363636;
  border-color: #363636;
}
.single-form input[type=checkbox]:checked + .checkbox-label span::before {
  opacity: 1;
  visibility: visible;
}
.single-form .radio-label {
  margin-bottom: 0;
}
.single-form .radio-label span {
  position: relative;
  display: inline-block;
  background-color: #fff;
  border: 1px solid #bbb;
  line-height: 0;
  width: 14px;
  height: 14px;
  margin: -4px 5px 0 0;
  border-radius: 50%;
  outline: 0;
  text-align: center;
  vertical-align: middle;
  clear: none;
  cursor: pointer;
  -webkit-appearance: none;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-form .radio-label span::before {
  position: absolute;
  content: "";
  width: 14px;
  height: 14px;
  background-color: #ffffff;
  border-radius: 50%;
  top: -1px;
  left: -1px;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-form input[type=radio] {
  display: none;
}
.single-form input[type=radio]:checked + .radio-label span {
  background-color: #363636;
  border-color: #363636;
}
.single-form input[type=radio]:checked + .radio-label span::before {
  transform: scale(0.5);
}

/*===== 02 - Layout =====*/
/*----------------------------------------*/
/*  02.01 - Header CSS
/*----------------------------------------*/
.header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
}

.header-padding {
  padding: 25px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .header-padding {
    padding: 20px 0;
  }
}

/*----------------------------------------*/
/*             Header Top CSS
/*----------------------------------------*/
.header__top {
  background-color: #f8ece5;
  padding: 12px 0;
}
.header__top--wrapper {
  display: flex;
}
@media only screen and (max-width: 767px) {
  .header__top--wrapper {
    display: block;
    text-align: center;
  }
}
.header__top--wrapper p {
  color: #363636;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.25;
  padding: 3px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .header__top--wrapper p {
    font-size: 14px;
  }
}
@media only screen and (max-width: 767px) {
  .header__top--wrapper p {
    font-size: 13px;
  }
}
.header__top--items {
  margin-right: calc(-30px / 2);
  margin-left: calc(-30px / 2);
  display: flex;
  flex-wrap: wrap;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .header__top--items {
    margin-right: calc(-20px / 2);
    margin-left: calc(-20px / 2);
  }
}
@media only screen and (max-width: 767px) {
  .header__top--items {
    justify-content: space-between;
  }
}
.header__top--items li {
  padding-right: calc(30px / 2);
  padding-left: calc(30px / 2);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .header__top--items li {
    padding-right: calc(20px / 2);
    padding-left: calc(20px / 2);
  }
}
.header__top--items li a i {
  font-size: 14px;
  color: #363636;
}
.header__top--items li a span {
  font-size: 14px;
  color: #494949;
  padding-left: 5px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1600px) {
  .header__top--items li a span {
    font-size: 18px;
  }
}
.header__top--items li a:hover span {
  color: #d68d67;
}

/*----------------------------------------*/
/*             Header Middle CSS
/*----------------------------------------*/
.header__middle {
  padding: 42px 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .header__middle {
    padding: 30px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .header__middle {
    padding: 25px 0;
  }
}

.header-mid-logo a {
  display: inline-block;
}
.header-mid-logo img {
  width: 100%;
  height: 39px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-mid-logo img {
    height: 32px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .header-mid-logo img {
    height: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .header-mid-logo img {
    height: 25px;
  }
}

.header-mid-meta__item {
  display: flex;
  align-items: center;
  margin-left: calc(-15px / 2);
  margin-right: calc(-15px / 2);
}
.header-mid-meta__item li {
  padding: 0 calc(15px / 2);
}
.header-mid-meta__item li a, .header-mid-meta__item li button {
  color: #363636;
  position: relative;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  font-size: 20px;
  padding: 0;
  border: 0;
  background: none;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .header-mid-meta__item li a, .header-mid-meta__item li button {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .header-mid-meta__item li a, .header-mid-meta__item li button {
    font-size: 16px;
  }
}
.header-mid-meta__item li a .badge, .header-mid-meta__item li button .badge {
  background-color: #d68d67;
  color: #ffffff;
  border-radius: 50%;
  padding: 0;
  width: 18px;
  height: 18px;
  line-height: 20px;
  text-align: center;
  font-weight: 500;
  font-size: 9px;
  position: absolute;
  left: 43%;
  bottom: 80%;
}

.header-mid-search {
  max-width: 230px;
}

.header-mid-toggle {
  margin-left: 20px;
}
.header-mid-toggle__toggle {
  padding: 0;
  border: 0;
  background: none;
  color: #363636;
  font-size: 24px;
  line-height: 1;
}
@media only screen and (max-width: 767px) {
  .header-mid-toggle__toggle {
    font-size: 20px;
  }
}
.header-mid-toggle__toggle:focus, .header-mid-toggle__toggle:hover {
  color: #d68d67;
}

/*----------------------------------------*/
/*             Header Main CSS
/*----------------------------------------*/
.header__main.header-shadow {
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .header__main {
    padding: 10px 0;
  }
}
.header__main--toggle {
  padding: 0;
  border: 0;
  background: none;
  color: #ffffff;
  font-size: 24px;
  line-height: 1;
}
@media only screen and (max-width: 767px) {
  .header__main--toggle {
    font-size: 20px;
  }
}
.header__main--toggle:focus, .header__main--toggle:hover {
  color: #ffffff;
}
.header__main--logo a img {
  width: 100%;
  height: 28px;
}
.header__main--meta {
  padding: 13px 0;
}
.header__main--meta .toggle-icon {
  font-size: 22px;
  width: 22px;
  line-height: 22px;
  height: 22px;
  text-align: center;
  color: #ffffff;
  position: relative;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  padding: 0;
  border: 0;
  background: none;
  margin-left: 20px;
}
.header__main--meta .toggle-icon svg {
  width: 1em;
  height: auto;
}
.header__main--meta .toggle-icon span {
  color: #ffffff;
  font-size: 24px;
  line-height: 24px;
  width: 24px;
  height: 24px;
}
.header__main--meta .toggle-icon:hover {
  color: #d68d67;
}
.header__main-dark .header__main--toggle {
  color: #363636;
}
.header__main-dark .header__main--toggle:focus, .header__main-dark .header__main--toggle:hover {
  color: #d68d67;
}
.header__main-dark .toggle-icon {
  color: #363636;
}
.header__main-dark .toggle-icon span {
  color: #363636;
}

@-webkit-keyframes stickyDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes stickyDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
.menu-items-list > li {
  position: relative;
}
.menu-items-list > li > a {
  font-size: 18px;
  line-height: 1.7;
  color: #ffffff;
  padding: 35px 28px;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .menu-items-list > li > a {
    font-size: 16px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list > li > a {
    font-size: 16px;
    padding: 18px 22px;
  }
}
.menu-items-list > li > a span {
  position: relative;
  display: block;
}
.menu-items-list > li > a span::before {
  position: absolute;
  content: "";
  top: 41%;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #d68d67;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.menu-items-list > li > a.active {
  color: #d68d67;
}
.menu-items-list > li > a i {
  font-size: 10px;
  margin-left: 5px;
}
.menu-items-list > li:hover > a {
  color: #d68d67;
}
.menu-items-list > li:hover > a span::before {
  width: 100%;
}
.menu-items-list > li:hover > .mega-menu, .menu-items-list > li:hover > .sub-menu {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
  top: 100%;
}
.menu-items-list > li:hover > .mega-menu > li:hover > .sub-menu, .menu-items-list > li:hover > .sub-menu > li:hover > .sub-menu {
  left: 100%;
  top: 0;
  opacity: 1;
  visibility: visible;
}
.menu-items-list .sub-menu {
  position: absolute;
  left: 0;
  top: -999em;
  z-index: 999;
  width: 230px;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: transform 200ms, opacity 200ms;
  transition-timing-function: cubic-bezier(0.17, 0.67, 0.83, 0.67);
  padding: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.078);
}
.menu-items-list .sub-menu li {
  margin: 0;
  position: relative;
}
.menu-items-list .sub-menu li a {
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  font-size: 18px;
  line-height: 1.7;
  padding: 5px 10px;
  color: #494949;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list .sub-menu li a {
    font-size: 16px;
  }
}
.menu-items-list .sub-menu li a span {
  position: relative;
  display: inline-block;
}
.menu-items-list .sub-menu li a span::before {
  position: absolute;
  content: "";
  width: 0;
  height: 1px;
  bottom: 4px;
  left: 0;
  background-color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.menu-items-list .sub-menu li a i {
  font-size: 9px;
  transform: rotate(-90deg);
}
.menu-items-list .sub-menu li a.active > a {
  color: #363636;
}
.menu-items-list .sub-menu li a.active > a span::before {
  width: 100%;
  background-color: #363636;
}
.menu-items-list .sub-menu li:hover > a {
  color: #d68d67;
}
.menu-items-list .sub-menu li:hover > a span::before {
  width: 100%;
  background-color: #d68d67;
}
.menu-items-list .mega-menu {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  z-index: 999;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: transform 200ms, opacity 200ms;
  transition-timing-function: cubic-bezier(0.17, 0.67, 0.83, 0.67);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.078);
  padding: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list .mega-menu {
    padding: 30px;
  }
}
.menu-items-list .mega-menu li a {
  font-size: 18px;
  line-height: 1.7;
  color: #494949;
  padding: 5px 10px;
  display: block;
  white-space: nowrap;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list .mega-menu li a {
    font-size: 16px;
  }
}
.menu-items-list .mega-menu li a span {
  position: relative;
  display: inline-block;
}
.menu-items-list .mega-menu li a span::before {
  position: absolute;
  content: "";
  width: 0;
  height: 1px;
  bottom: 4px;
  left: 0;
  background-color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.menu-items-list .mega-menu li a.active {
  color: #363636;
}
.menu-items-list .mega-menu li:hover a {
  color: #d68d67;
}
.menu-items-list .mega-menu li:hover a span::before {
  width: 100%;
  background-color: #d68d67;
}
.menu-items-list .mega-menu--wrapper {
  padding: 50px 135px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .menu-items-list .mega-menu--wrapper {
    padding: 50px 90px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list .mega-menu--wrapper {
    padding: 50px 70px;
  }
}
.menu-items-list .mega-menu__banner {
  width: 40%;
  flex: 0 0 auto;
}
.menu-items-list .mega-menu__banner a {
  position: relative;
  display: block;
  text-align: center;
}
.menu-items-list .mega-menu__banner--image {
  overflow: hidden;
}
.menu-items-list .mega-menu__banner--image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.menu-items-list .mega-menu__banner--image:hover img {
  transform: scale(1.15);
}
.menu-items-list .mega-menu__banner--caption {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
.menu-items-list .mega-menu__banner--caption .caption-title {
  font-size: 46px;
  font-weight: 600;
  line-height: 1em;
  color: #ffffff;
  margin: 0 0 20px 0;
  position: relative;
}
.menu-items-list .mega-menu__banner--caption .caption-title::after {
  content: "";
  display: block;
  border-bottom: 2px solid;
  width: 60px;
  margin: 0.4em auto 0;
}
.menu-items-list .mega-menu__banner--caption .caption-desc {
  font-size: 22px;
  color: #ffffff;
}
.menu-items-list .mega-menu__content {
  padding-left: 110px;
  width: 60%;
  display: flex;
  flex-direction: column;
  gap: 10px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list .mega-menu__content {
    padding-left: 50px;
  }
}
.menu-items-list .mega-menu__content--title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
  color: #363636;
  margin-bottom: 20px;
  display: block;
}
.menu-items-list .mega-menu__content--list {
  width: 50%;
  flex: 0 0 auto;
}
.menu-items-list .mega-menu__content--list li a {
  padding: 0 0 3px;
}
.menu-items-list .mega-menu__info {
  margin-left: -10px;
  margin-right: -10px;
  display: flex;
}
.menu-items-list .mega-menu__info li {
  margin: 0 10px;
}
.menu-items-list .mega-menu__info li a {
  font-size: 14px;
  padding: 0;
}
.menu-items-list .mega-menu__social {
  display: flex;
  gap: 0 20px;
  margin-top: 10px;
}
.menu-items-list .mega-menu__social--lable {
  white-space: nowrap;
}
.menu-items-list .mega-menu__social--social {
  display: flex;
  gap: 0 10px;
}
.menu-items-list .mega-menu__social--social li a {
  width: 28px;
  height: 28px;
  border: 1px solid #e8e8e8;
  border-radius: 50%;
  line-height: 30px;
  text-align: center;
  padding: 0;
  font-size: 14px;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.menu-items-list .mega-menu__social--social li:hover a {
  background-color: #363636;
  color: #ffffff;
}
.menu-items-list .mega-menus-list {
  -moz-column-count: 5;
       column-count: 5;
  display: block;
}
.menu-items-list .mega-menu-title {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.7;
  color: #363636;
  padding: 5px 10px;
  display: block;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .menu-items-list .mega-menu-title {
    font-size: 16px;
  }
}
.menu-items-list .mega-menu-col {
  width: 25%;
  flex: 0 0 auto;
}
.menu-items-list .mega-menu-width {
  max-width: 1170px;
  margin: 0 auto;
}
.menu-items-list--dark > li > a {
  color: #363636;
}
.menu-items-list--dark > li > a.active {
  color: #d68d67;
}
.menu-items-list--dark > li:hover > a {
  color: #d68d67;
}
.menu-items-list.menu-uppercase > li > a {
  padding: 17px 15px 15px;
  font-size: 18px;
  letter-spacing: 0.12em;
  text-transform: uppercase;
  margin: 0 5px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .menu-items-list.menu-uppercase > li > a {
    font-size: 16px;
    padding: 14px 20px;
  }
}

.meta-items-list {
  gap: 0 20px;
}
.meta-items-list li button, .meta-items-list li a {
  color: #ffffff;
  position: relative;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  font-size: 16px;
  padding: 0;
  border: 0;
  background: none;
  line-height: 1;
}
.meta-items-list li button .badge, .meta-items-list li a .badge {
  background-color: #d68d67;
  color: #ffffff;
  border-radius: 50%;
  padding: 0;
  width: 18px;
  height: 18px;
  line-height: 20px;
  text-align: center;
  font-weight: 500;
  font-size: 10px;
  position: absolute;
  left: 40%;
  bottom: 90%;
}
.meta-items-list li:hover > button, .meta-items-list li:hover > a {
  color: #d68d67;
}
.meta-items-list--dark li button, .meta-items-list--dark li a {
  color: #363636;
}
.meta-items-list--dark li:hover > button, .meta-items-list--dark li:hover > a {
  color: #d68d67;
}

.meta-search {
  display: flex;
}
.meta-search input {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  border: 0;
  border-bottom: 1px solid #dbdbdb;
  width: 165px;
  flex: 1 1 auto;
  background-color: transparent;
  color: #ffffff;
}
.meta-search input::-webkit-input-placeholder {
  opacity: 0.5;
  color: #ffffff;
}
.meta-search input:-moz-placeholder {
  opacity: 0.5;
  color: #ffffff;
}
.meta-search input::-moz-placeholder {
  opacity: 0.5;
  color: #ffffff;
}
.meta-search input:-ms-input-placeholder {
  opacity: 0.5;
  color: #ffffff;
}
.meta-search button {
  background-color: transparent;
  font-size: 20px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border: 0;
  padding: 0 0 0 10px;
  color: #ffffff;
}
.meta-search--dark input {
  background-color: #ffffff;
  color: #363636;
}
.meta-search--dark input::-webkit-input-placeholder {
  opacity: 0.5;
  color: #363636;
}
.meta-search--dark input:-moz-placeholder {
  opacity: 0.5;
  color: #363636;
}
.meta-search--dark input::-moz-placeholder {
  opacity: 0.5;
  color: #363636;
}
.meta-search--dark input:-ms-input-placeholder {
  opacity: 0.5;
  color: #363636;
}
.meta-search--dark button {
  color: #363636;
}

.is-sticky .header__main.header-shadow {
  box-shadow: none;
}
.is-sticky .header__main--toggle {
  color: #363636;
}
.is-sticky .header__main--toggle:focus, .is-sticky .header__main--toggle:hover {
  color: #d68d67;
}
.is-sticky .header__main--meta .toggle-icon {
  color: #363636;
}
.is-sticky .header__main--meta .toggle-icon span {
  color: #363636;
}
.is-sticky .header__main-2-category .category-btn {
  color: #363636;
}
.is-sticky .menu-items-list > li > a {
  color: #363636;
  padding: 25px 28px;
}
.is-sticky .menu-items-list > li > a.active {
  color: #d68d67;
}
.is-sticky .menu-items-list > li:hover > a {
  color: #d68d67;
}
.is-sticky .menu-items-list.menu-uppercase > li > a {
  padding: 16px 15px 13px;
}
.is-sticky .meta-items-list li button, .is-sticky .meta-items-list li a {
  color: #363636;
}
.is-sticky .meta-items-list li:hover > button, .is-sticky .meta-items-list li:hover > a {
  color: #d68d67;
}
.is-sticky .meta-search input {
  background-color: #ffffff;
  color: #363636;
}
.is-sticky .meta-search input::-webkit-input-placeholder {
  opacity: 0.5;
  color: #363636;
}
.is-sticky .meta-search input:-moz-placeholder {
  opacity: 0.5;
  color: #363636;
}
.is-sticky .meta-search input::-moz-placeholder {
  opacity: 0.5;
  color: #363636;
}
.is-sticky .meta-search input:-ms-input-placeholder {
  opacity: 0.5;
  color: #363636;
}
.is-sticky .meta-search button {
  color: #363636;
}

/*----------------------------------------*/
/*             Search CSS
/*----------------------------------------*/
.search-modal {
  background-color: rgba(0, 0, 0, 0.6);
}
.search-modal .modal-dialog {
  max-width: 760px;
  padding: 30px;
}
.search-modal .modal-content {
  border-radius: 0;
  background: none;
  border: 0;
}
.search-modal__close {
  position: absolute;
  top: 20px;
  right: 0;
  margin: 30px;
  padding: 15px;
  font-size: 24px;
  border: 0;
  background-color: #363636;
  line-height: 1;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (max-width: 767px) {
  .search-modal__close {
    padding: 10px;
    font-size: 18px;
  }
}
.search-modal__close:focus, .search-modal__close:hover {
  color: #ffffff;
  background-color: #d68d67;
}
.search-modal__form {
  position: relative;
  transform: translateY(-20px);
  transition: transform 200ms ease-out;
}
.search-modal__form input {
  width: 100%;
  height: 52px;
  background: none;
  color: #ffffff;
  border: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.44);
  font-size: 18px;
}
.search-modal__form input::-webkit-input-placeholder {
  opacity: 1;
  color: #ffffff;
}
.search-modal__form input:-moz-placeholder {
  opacity: 1;
  color: #ffffff;
}
.search-modal__form input::-moz-placeholder {
  opacity: 1;
  color: #ffffff;
}
.search-modal__form input:-ms-input-placeholder {
  opacity: 1;
  color: #ffffff;
}
.search-modal__form button {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: 0;
  height: 52px;
  font-size: 20px;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.search-modal__form button:hover {
  color: #d68d67;
}

/*----------------------------------------*/
/*           OffCanvas Menu CSS
/*----------------------------------------*/
.offcanvas-sidebar {
  max-width: 450px;
  padding: 20px 0;
}
.offcanvas-sidebar.offcanvas-end {
  width: 100%;
}
.offcanvas-sidebar .offcanvas-body {
  padding: 50px;
  display: flex;
  gap: 20px;
  flex-direction: column;
}
.offcanvas-sidebar .offcanvas-body::-webkit-scrollbar-track {
  background-color: #ffffff;
}
.offcanvas-sidebar .offcanvas-body::-webkit-scrollbar {
  width: 3px;
}
.offcanvas-sidebar .offcanvas-body::-webkit-scrollbar-thumb {
  background-color: #363636;
}
.offcanvas-sidebar__close {
  position: absolute;
  border: 0;
  background: none;
  padding: 0;
  font-size: 26px;
  top: 30px;
  right: 30px;
  color: #494949;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.offcanvas-sidebar__close:hover {
  color: #363636;
}
.offcanvas-sidebar__menu {
  margin-bottom: 50px;
}
.offcanvas-sidebar__banner {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 110px 40px 55px;
  position: relative;
  z-index: 1;
}
.offcanvas-sidebar__banner::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.offcanvas-sidebar__banner:hover::before {
  opacity: 0.2;
  visibility: visible;
}
.offcanvas-sidebar__banner .banner-title {
  color: rgba(198, 32, 32, 0);
  font-size: 80px;
  font-weight: 400;
  line-height: 0.9em;
  -webkit-text-stroke-width: 1px;
  stroke-width: 1px;
  -webkit-text-stroke-color: #c62020;
  stroke: #c62020;
}
.offcanvas-sidebar__banner .banner-sub-title {
  color: #363636;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.5em;
  margin-bottom: 10px;
}
.offcanvas-sidebar__banner .banner-btn {
  font-size: 14px;
  line-height: 35px;
  fill: #363636;
  color: #363636;
  background-color: #ffffff;
  box-shadow: 5px 5px 0 0 #363636;
  padding: 0 40px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.offcanvas-sidebar__banner .banner-btn:hover {
  color: #ffffff;
  background-color: #d68d67;
}
.offcanvas-sidebar__copyright p {
  font-size: 18px;
  color: #494949;
}

.offcanvas-menu-list li {
  padding: 5px 0;
}
.offcanvas-menu-list li :first-child {
  padding-top: 0;
}
.offcanvas-menu-list li:last-child {
  padding-bottom: 0;
}
.offcanvas-menu-list li a {
  font-size: 18px;
  color: #363636;
  line-height: 1.8;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.offcanvas-menu-list li:hover a {
  color: #d68d67;
}

.offcanvas-info-list {
  margin-top: 30px;
}
.offcanvas-info-list li {
  padding: 5px 0;
}
.offcanvas-info-list li :first-child {
  padding-top: 0;
}
.offcanvas-info-list li:last-child {
  padding-bottom: 0;
}
.offcanvas-info-list li span, .offcanvas-info-list li a {
  font-size: 18px;
  line-height: 1.7;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  color: #494949;
}
.offcanvas-info-list li:hover a {
  color: #d68d67;
}

.offcanvas-social {
  display: flex;
  gap: 15px;
}
.offcanvas-social li a {
  font-size: 18px;
  color: #494949;
}

/*----------------------------------------*/
/*           Mobile Menu CSS
/*----------------------------------------*/
.mobile-menu .offcanvas-header {
  padding: 10px;
}
.mobile-menu .offcanvas-body {
  padding: 10px 50px 80px;
}
.mobile-menu__close {
  font-size: 26px;
  padding: 10px;
  line-height: 1;
  cursor: pointer;
  border: 0;
  background: none;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  color: #494949;
  margin-left: auto;
}
.mobile-menu__close:focus, .mobile-menu__close:hover {
  color: #363636;
}

.mobile-menu-items li > a {
  padding: 10px 0;
  font-size: 18px;
  line-height: 1.8;
  border-bottom: 1px solid #ededed;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mobile-menu-items li > a:hover {
  color: #d68d67;
}
.mobile-menu-items li > a .menu-expand {
  font-size: 14px;
  line-height: 1;
  padding: 8px 9px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.mobile-menu-items li.active > a .menu-expand {
  transform: rotate(180deg);
}
.mobile-menu-items .sub-menu, .mobile-menu-items .mega-menu {
  padding: 15px;
  background-color: #f9f9f9;
  display: none;
}
.mobile-menu-items .sub-menu li > a, .mobile-menu-items .mega-menu li > a {
  padding: 5px 10px;
  font-size: 1em;
  color: #494949;
  border-bottom: 0px;
}
.mobile-menu-items .sub-menu__banner a, .mobile-menu-items .mega-menu__banner a {
  position: relative;
  display: block;
  text-align: center;
}
.mobile-menu-items .sub-menu__banner--image, .mobile-menu-items .mega-menu__banner--image {
  overflow: hidden;
}
.mobile-menu-items .sub-menu__banner--image img, .mobile-menu-items .mega-menu__banner--image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.mobile-menu-items .sub-menu__banner--image:hover img, .mobile-menu-items .mega-menu__banner--image:hover img {
  transform: scale(1.15);
}
.mobile-menu-items .sub-menu__banner--caption, .mobile-menu-items .mega-menu__banner--caption {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
.mobile-menu-items .sub-menu__banner--caption .caption-title, .mobile-menu-items .mega-menu__banner--caption .caption-title {
  font-size: 34px;
  font-weight: 600;
  line-height: 1em;
  color: #ffffff;
  margin: 0 0 10px 0;
  position: relative;
}
.mobile-menu-items .sub-menu__banner--caption .caption-title::after, .mobile-menu-items .mega-menu__banner--caption .caption-title::after {
  content: "";
  display: block;
  border-bottom: 2px solid;
  width: 60px;
  margin: 0.4em auto 0;
}
.mobile-menu-items .sub-menu__banner--caption .caption-desc, .mobile-menu-items .mega-menu__banner--caption .caption-desc {
  font-size: 16px;
  color: #ffffff;
}
.mobile-menu-items .sub-menu__content, .mobile-menu-items .mega-menu__content {
  display: flex;
  flex-direction: column;
  gap: 10px 0;
  margin-top: 20px;
}
.mobile-menu-items .sub-menu__content--title, .mobile-menu-items .mega-menu__content--title {
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  color: #363636;
  margin-bottom: 10px;
  display: block;
}
.mobile-menu-items .sub-menu__content--list, .mobile-menu-items .mega-menu__content--list {
  width: 100%;
  flex: 0 0 auto;
}
.mobile-menu-items .sub-menu__content--list li a, .mobile-menu-items .mega-menu__content--list li a {
  padding: 0 0 3px;
}
.mobile-menu-items .sub-menu__info, .mobile-menu-items .mega-menu__info {
  margin-left: -10px;
  margin-right: -10px;
  display: flex;
}
.mobile-menu-items .sub-menu__info li, .mobile-menu-items .mega-menu__info li {
  margin: 0 10px;
}
.mobile-menu-items .sub-menu__info li a, .mobile-menu-items .mega-menu__info li a {
  font-size: 14px;
  padding: 0;
}
.mobile-menu-items .sub-menu__social, .mobile-menu-items .mega-menu__social {
  display: flex;
  gap: 0 20px;
  margin-top: 10px;
}
.mobile-menu-items .sub-menu__social--lable, .mobile-menu-items .mega-menu__social--lable {
  white-space: nowrap;
}
.mobile-menu-items .sub-menu__social--social, .mobile-menu-items .mega-menu__social--social {
  display: flex;
  gap: 0 10px;
}
.mobile-menu-items .sub-menu__social--social li a, .mobile-menu-items .mega-menu__social--social li a {
  width: 28px;
  height: 28px;
  border: 1px solid #e8e8e8;
  border-radius: 50%;
  line-height: 30px;
  text-align: center;
  padding: 0;
  font-size: 14px;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.mobile-menu-items .sub-menu__social--social li:hover a, .mobile-menu-items .mega-menu__social--social li:hover a {
  background-color: #363636;
  color: #ffffff;
}
.mobile-menu-items .mega-menu-title {
  font-size: 1em;
  font-weight: 600;
  line-height: 1.7;
  color: #363636;
  padding: 5px 10px;
  display: block;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .mobile-menu-items .mega-menu-title {
    font-size: 16px;
  }
}

/*----------------------------------------*/
/*           Mobile Meta CSS
/*----------------------------------------*/
.mobile-meta {
  background-color: #363636;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.mobile-meta-items {
  display: flex;
  justify-content: space-around;
}
.mobile-meta-items li {
  height: 50px;
  line-height: 50px;
}
.mobile-meta-items li button, .mobile-meta-items li a {
  padding: 5px;
  background: none;
  border: 0;
  color: #ffffff;
  position: relative;
  line-height: 1;
}
.mobile-meta-items li button .badge, .mobile-meta-items li a .badge {
  background-color: #d68d67;
  color: #ffffff;
  border-radius: 50%;
  padding: 0;
  width: 16px;
  height: 16px;
  line-height: 20px;
  text-align: center;
  font-weight: 500;
  font-size: 10px;
  position: absolute;
  left: 45%;
  bottom: 67%;
}

/*----------------------------------------*/
/*           Slide Down Menu CSS
/*----------------------------------------*/
.slidedown-menu {
  position: fixed;
  display: flex;
  align-items: stretch;
  top: 0;
  width: 100%;
  height: auto;
  pointer-events: auto;
  z-index: 995;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-100%);
  transition: all 0.3s cubic-bezier(0.6, 0.17, 0.45, 0.88);
}
.slidedown-menu.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
.slidedown-menu__inner {
  position: relative;
  width: 100%;
  overflow-y: auto;
  background-color: #363636;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  left: 0;
  top: 0;
}
.slidedown-menu__close {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  font-size: 26px;
  top: 30px;
  right: 30px;
  line-height: 1;
  cursor: pointer;
  z-index: 100;
  color: #ffffff;
}
.slidedown-menu__close button {
  padding: 0;
  background: none;
  border: 0;
}
.slidedown-menu__content {
  max-height: 100vh;
  overflow: auto;
  padding: 20px 0;
}
.slidedown-menu__content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.slidedown-menu__content::-webkit-scrollbar-button {
  width: 10px;
  height: 10px;
}
.slidedown-menu__content::-webkit-scrollbar-thumb {
  background: #cecece;
  border: 0 none #ffffff;
  border-radius: 8px;
}
.slidedown-menu__content::-webkit-scrollbar-thumb:hover {
  background: #c1c1c1;
}
.slidedown-menu__content::-webkit-scrollbar-thumb:active {
  background: #c1c1c1;
}
.slidedown-menu__content::-webkit-scrollbar-track {
  background: rgba(122, 122, 122, 0.15);
  border: 0 none #ffffff;
  border-radius: 8px;
}
.slidedown-menu__content::-webkit-scrollbar-track:hover {
  background: rgba(122, 122, 122, 0.2);
}
.slidedown-menu__content::-webkit-scrollbar-track:active {
  background: rgba(122, 122, 122, 0.2);
}
.slidedown-menu__content::-webkit-scrollbar-corner {
  background: transparent;
}
.slidedown-menu__menu {
  min-height: 100vh;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slidedown-primary-menu {
  text-align: center;
}
.slidedown-primary-menu > li > a {
  font-size: 44px;
  padding: 20px 0;
  color: rgba(255, 255, 255, 0.38);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .slidedown-primary-menu > li > a {
    font-size: 32px;
    padding: 15px 0;
  }
}
@media only screen and (max-width: 767px) {
  .slidedown-primary-menu > li > a {
    font-size: 26px;
    padding: 14px 0;
  }
}
.slidedown-primary-menu > li > a i {
  font-size: 10px;
  margin-left: 6px;
}
.slidedown-primary-menu > li > a.active {
  color: #ffffff;
}
.slidedown-primary-menu > li:hover > a {
  color: #ffffff;
}
.slidedown-primary-menu .sub-menu, .slidedown-primary-menu .mega-menu {
  display: none;
}
.slidedown-primary-menu .sub-menu li > a, .slidedown-primary-menu .mega-menu li > a {
  font-size: 18px;
  padding: 5px 10px;
  color: rgba(255, 255, 255, 0.38);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 2;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .slidedown-primary-menu .sub-menu li > a, .slidedown-primary-menu .mega-menu li > a {
    font-size: 16px;
    line-height: 1.8;
  }
}
.slidedown-primary-menu .sub-menu li > a i, .slidedown-primary-menu .mega-menu li > a i {
  font-size: 10px;
  margin-left: 6px;
}
.slidedown-primary-menu .sub-menu li:hover > a, .slidedown-primary-menu .mega-menu li:hover > a {
  color: #ffffff;
}

.is-sticky {
  position: fixed;
  box-shadow: 0 5px 16px rgba(0, 0, 0, 0.1);
  width: 100%;
  background-color: #ffffff;
  z-index: 99;
  left: 0;
  top: 0;
  -webkit-animation: stickyDown 0.8s ease-out;
          animation: stickyDown 0.8s ease-out;
}
.is-sticky .header__top {
  display: none !important;
}
.is-sticky .header__middle {
  display: none !important;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .is-sticky .header__middle {
    display: block !important;
  }
}
.is-sticky .header-padding {
  padding: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .is-sticky .header-padding {
    padding: 10px 0;
  }
}

/*----------------------------------------*/
/*  02.02 - Footer CSS
/*----------------------------------------*/
@media only screen and (max-width: 767px) {
  .footer-section {
    padding-bottom: 50px;
  }
}
.footer-section.footer-border {
  border-top: 1px solid #ccc;
}

.footer-main {
  padding-top: 35px;
  padding-bottom: 60px;
  display: flex;
  flex-wrap: wrap;
}

.footer-padding {
  padding-top: 30px;
  padding-bottom: 70px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-padding {
    padding-top: 10px;
    padding-bottom: 50px;
  }
}

.footer-col-1 {
  width: 25%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-col-1 {
    width: 100%;
  }
}

.footer-col-2 {
  width: 50%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-col-2 {
    width: 64%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-col-2 {
    width: 100%;
  }
}

.footer-col-3 {
  width: 25%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-col-3 {
    width: 36%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-col-3 {
    width: 100%;
  }
}

.footer-about {
  max-width: 340px;
  width: 100%;
  padding-top: 40px;
}
.footer-about > * {
  padding: calc(20px / 2) 0;
}
.footer-about > *:first-child {
  padding-top: 0;
}
.footer-about > *:last-child {
  padding-bottom: 0;
}
.footer-about__logo img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.footer-about p {
  color: #717171;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.55;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-about p {
    font-size: 16px;
  }
}

.footer-title {
  color: #363636;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer-title-2 {
  color: #363636;
  font-size: 24px;
  font-weight: 700;
  font-family: "Playfair Display", serif;
  letter-spacing: 1px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-title-2 {
    font-size: 20px;
  }
}

.footer-link {
  display: flex;
  flex-wrap: wrap;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-link {
    justify-content: space-between;
  }
}
.footer-link > * {
  width: 33.33%;
}
@media only screen and (max-width: 767px) {
  .footer-link > * {
    width: 50%;
  }
}
@media only screen and (max-width: 575px) {
  .footer-link > *:first-child {
    width: 100%;
  }
}
.footer-link__wrapper {
  padding-top: 40px;
}
.footer-link__list {
  margin-top: 25px;
}
.footer-link__list li:not(:first-child) {
  margin-top: calc(10px / 2);
}
.footer-link__list li:not(:last-child) {
  padding-bottom: calc(10px / 2);
}
.footer-link__list li span, .footer-link__list li a {
  font-size: 16px;
  line-height: 1.56;
  color: #969696;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  white-space: nowrap;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-link__list li span, .footer-link__list li a {
    font-size: 16px;
  }
}
.footer-link__list li span {
  white-space: normal;
}
.footer-link__list li:hover a {
  color: #363636;
}

.footer-newsletter {
  padding-top: 40px;
}
.footer-newsletter__form {
  padding-top: 25px;
}
.footer-newsletter__form > * {
  margin-top: 20px;
}
.footer-newsletter__form > *:first-child {
  margin-top: 0;
}
.footer-newsletter__form p {
  color: #717171;
}
.footer-newsletter__input {
  position: relative;
}
.footer-newsletter__input input {
  padding: 10px 100px 10px 15px;
  font-size: 14px;
  line-height: 20px;
  border: 1px solid #dedede;
  width: 100%;
  background: none;
}
.footer-newsletter__input button {
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  color: #494949;
  padding: 8px 15px 8px 15px;
  border: 0;
  border-left: 1px solid #cbcbcb;
  background: none;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.footer-newsletter__input button:hover {
  color: #363636;
}
.footer-newsletter__social {
  display: flex;
  gap: 15px;
}
.footer-newsletter__social li a {
  font-size: 18px;
  color: #494949;
}

.footer-copyright {
  border-top: 1px solid #cecece;
  height: 62px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media only screen and (max-width: 767px) {
  .footer-copyright {
    height: auto;
    padding: 10px 0;
  }
}
.footer-copyright P {
  color: #717171;
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}
.footer-copyright P a {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.footer-copyright P a:hover {
  color: #d68d67;
}
@media only screen and (max-width: 767px) {
  .footer-copyright ul {
    margin-top: 10px;
  }
}
@media only screen and (max-width: 575px) {
  .footer-copyright ul {
    flex-wrap: wrap;
  }
}
.footer-copyright ul li {
  margin-right: calc(70px / 2);
  margin-left: calc(70px / 2);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-copyright ul li {
    margin-right: calc(60px / 2);
    margin-left: calc(60px / 2);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-copyright ul li {
    margin-right: calc(30px / 2);
    margin-left: calc(30px / 2);
  }
}
.footer-copyright ul li:first-child {
  margin-left: 0;
}
.footer-copyright ul li:last-child {
  margin-right: 0;
}
.footer-copyright ul li a {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
  color: #717171;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  white-space: nowrap;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .footer-copyright ul li a {
    font-size: 16px;
  }
}
.footer-copyright ul li:hover a {
  color: #d68d67;
}

.footer-dark {
  background-color: #1d1d1d;
}
.footer-dark .footer-title {
  color: #ffffff;
}
.footer-dark .footer-about p {
  color: #969696;
}
.footer-dark .footer-link__list li:hover a {
  color: #ffffff;
}
.footer-dark .footer-newsletter__form p {
  color: #969696;
}
.footer-dark .footer-newsletter__input input {
  border-color: #535353;
}
.footer-dark .footer-newsletter__input button {
  border-color: #535353;
  color: #969696;
}
.footer-dark .footer-newsletter__input button:hover {
  color: #ffffff;
}
.footer-dark .footer-newsletter__social li a {
  color: #969696;
}

/*----------------------------------------*/
/*  02.03. - Slider CSS
/*----------------------------------------*/
.slider-section {
  position: relative;
}

.slider-item {
  height: 810px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-item {
    height: 510px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-item {
    height: 430px;
  }
}
@media only screen and (max-width: 767px) {
  .slider-item {
    height: auto;
    padding: 60px 0;
  }
}

/*--------------------------------
  Home Page 01 Style 
--------------------------------*/
.home-1-slider-style-1 {
  background-color: #e5ebe8;
}

.home-1-slider-content-style-1 {
  position: relative;
  max-width: 580px;
  margin: 0 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .home-1-slider-content-style-1 {
    width: 480px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .home-1-slider-content-style-1 {
    width: 380px;
    margin: 0;
  }
}
@media only screen and (max-width: 767px) {
  .home-1-slider-content-style-1 {
    width: 270px;
    margin: 0;
  }
}
@media only screen and (max-width: 767px) {
  .home-1-slider-content-style-1 {
    text-align: center;
  }
}
.home-1-slider-content-style-1__sub-title {
  line-height: 1;
  font-size: 36px;
  font-weight: 300;
  font-style: italic;
  font-family: "Playfair Display", serif;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .home-1-slider-content-style-1__sub-title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .home-1-slider-content-style-1__sub-title {
    font-size: 24px;
  }
}
.home-1-slider-content-style-1__title {
  color: #363636;
  font-size: 86px;
  font-weight: 300;
  line-height: 1;
  font-family: "Playfair Display", serif;
  margin-top: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .home-1-slider-content-style-1__title {
    font-size: 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .home-1-slider-content-style-1__title {
    font-size: 56px;
  }
}
@media only screen and (max-width: 767px) {
  .home-1-slider-content-style-1__title {
    font-size: 30px;
  }
}
.home-1-slider-content-style-1__btns {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
}
.home-1-slider-content-style-1__btns > * {
  padding: 0 15px;
  margin-top: 15px;
}
@media only screen and (max-width: 767px) {
  .home-1-slider-content-style-1__btns {
    justify-content: center;
    margin-top: 10px;
  }
}
.home-1-slider-content-style-1__btn {
  background-color: #ffffff;
  color: #494949;
  font-weight: 400;
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border: 1px solid #ffffff;
  padding: 17px 50px;
  line-height: 1.223;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .home-1-slider-content-style-1__btn {
    font-size: 16px;
    padding: 14px 35px;
  }
}
@media only screen and (max-width: 767px) {
  .home-1-slider-content-style-1__btn {
    font-size: 14px;
    padding: 10px 27px;
  }
}
.home-1-slider-content-style-1__btn:hover {
  border-color: #d68d67;
  background-color: #d68d67;
  color: #ffffff;
}
.home-1-slider-content-style-1 .button-01 .home-1-slider-content-style-1__btn {
  background-color: transparent;
  border-color: #363636;
  color: #363636;
}
.home-1-slider-content-style-1 .button-01 .home-1-slider-content-style-1__btn:hover {
  background-color: #ffffff;
  border-color: #ffffff;
}

.home-1-slider-animation .home-1-slider-content-style-1 {
  opacity: 0;
}
.home-1-slider-animation.swiper-slide-active .home-1-slider-content-style-1 {
  opacity: 1;
}
.home-1-slider-animation.swiper-slide-active .home-1-slider-content-style-1 > *:nth-child(1) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 0.5s;
          animation-delay: 0.5s;
  -webkit-animation-duration: 1.3s;
          animation-duration: 1.3s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.home-1-slider-animation.swiper-slide-active .home-1-slider-content-style-1 > *:nth-child(2) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
  -webkit-animation-duration: 1.3s;
          animation-duration: 1.3s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.home-1-slider-animation.swiper-slide-active .home-1-slider-content-style-1 > *:nth-child(3) > *:nth-child(1) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 1.5s;
          animation-delay: 1.5s;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.home-1-slider-animation.swiper-slide-active .home-1-slider-content-style-1 > *:nth-child(3) > *:nth-child(2) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 2s;
          animation-delay: 2s;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}

/*--------------------------------
  Home Page 02 Style 
--------------------------------*/
.home-2-slider-style {
  position: relative;
  height: 1080px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .home-2-slider-style {
    height: 820px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .home-2-slider-style {
    height: 720px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .home-2-slider-style {
    height: 650px;
  }
}
@media only screen and (max-width: 767px) {
  .home-2-slider-style {
    height: auto;
    padding: 100px 0;
  }
}

.home-2-slider-content-style-1 {
  position: relative;
  max-width: 600px;
  width: 100%;
  padding-top: 150px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .home-2-slider-content-style-1 {
    max-width: 520px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .home-2-slider-content-style-1 {
    max-width: 480px;
    padding-top: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .home-2-slider-content-style-1 {
    max-width: 380px;
    padding-top: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .home-2-slider-content-style-1 {
    text-align: center;
  }
}
.home-2-slider-content-style-1__sub-title {
  color: #ffffff;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.4em;
  margin-bottom: 20px;
}
@media only screen and (max-width: 767px) {
  .home-2-slider-content-style-1__sub-title {
    font-size: 15px;
    letter-spacing: 0.2em;
  }
}
.home-2-slider-content-style-1__title {
  color: #d8b871;
  font-size: 86px;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .home-2-slider-content-style-1__title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 767px) {
  .home-2-slider-content-style-1__title {
    font-size: 48px;
  }
}
.home-2-slider-content-style-1__description {
  color: #c7c1b4;
  font-size: 32px;
  font-weight: 400;
  font-weight: normal;
  line-height: 1.25;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .home-2-slider-content-style-1__description {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .home-2-slider-content-style-1__description {
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .home-2-slider-content-style-1__description {
    font-size: 20px;
  }
}
.home-2-slider-content-style-1__btn {
  font-size: 24px;
  line-height: 1em;
  color: #ffffff;
  position: relative;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin: 130px 40px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .home-2-slider-content-style-1__btn {
    font-size: 20px;
    margin-top: 100px;
  }
}
@media only screen and (max-width: 767px) {
  .home-2-slider-content-style-1__btn {
    font-size: 18px;
    margin-top: 80px;
  }
}
.home-2-slider-content-style-1__btn::before {
  position: absolute;
  content: "";
  width: 5.8em;
  height: 5.8em;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='141.04' height='145.999' viewBox='0 0 141.04 145.999'%3E%3Cpath d='M72.999 145.999a72.655 72.655 0 0 1-40.816-12.467 73.2 73.2 0 0 1-26.447-32.118 72.909 72.909 0 0 1 6.73-69.229A73.209 73.209 0 0 1 44.583 5.737a73.112 73.112 0 0 1 70.071 7.308 73.237 73.237 0 0 1 26.385 33.456h-1.076c-.2-.5-.409-1.017-.625-1.528a71.98 71.98 0 1 0-10.539 73.528h1.287a73.316 73.316 0 0 1-24.894 20.033 72.571 72.571 0 0 1-15.406 5.524 73.348 73.348 0 0 1-16.787 1.941Z' fill='%23fff' opacity='.3'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  right: 35%;
  top: 50%;
  transform: translateY(-50%);
}
.home-2-slider-content-style-1__btn:hover {
  color: #d68d67;
}

.home-2-slider-animation .home-2-slider-content-style-1 {
  opacity: 0;
}
.home-2-slider-animation.swiper-slide-active .home-2-slider-content-style-1 {
  opacity: 1;
}
.home-2-slider-animation.swiper-slide-active .home-2-slider-content-style-1 > *:nth-child(1) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 0.5s;
          animation-delay: 0.5s;
  -webkit-animation-duration: 1.3s;
          animation-duration: 1.3s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.home-2-slider-animation.swiper-slide-active .home-2-slider-content-style-1 > *:nth-child(2) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
  -webkit-animation-duration: 1.3s;
          animation-duration: 1.3s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.home-2-slider-animation.swiper-slide-active .home-2-slider-content-style-1 > *:nth-child(3) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 1.5s;
          animation-delay: 1.5s;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.home-2-slider-animation.swiper-slide-active .home-2-slider-content-style-1 > *:nth-child(4) {
  -webkit-animation-name: ShortFadeInUp;
          animation-name: ShortFadeInUp;
  -webkit-animation-delay: 2s;
          animation-delay: 2s;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}

/*----------------------------------------*/
/*  02.04 - Banner CSS
/*----------------------------------------*/
.banner-wrapper {
  padding-top: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-wrapper {
    padding-top: 10px;
  }
}

.banner-item {
  position: relative;
  margin-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-item {
    margin-top: 20px;
  }
}
.banner-item__badge {
  position: absolute;
  top: 25px;
  left: 25px;
  width: 65px;
  height: 65px;
  border-radius: 50%;
  font-size: 22px;
  font-weight: 400;
  line-height: 1em;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  text-align: center;
  z-index: 3;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-item__badge {
    width: 50px;
    height: 50px;
    font-size: 18px;
    top: 15px;
    left: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-item__badge {
    width: 50px;
    height: 50px;
    font-size: 16px;
    top: 15px;
    left: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .banner-item__badge {
    width: 56px;
    height: 56px;
    font-size: 16px;
    top: 20px;
    left: 20px;
  }
}
.banner-item__badge.trend {
  background-color: #60bcb5;
}
.banner-item__badge.hot {
  background-color: #fd4e4e;
}
.banner-item__badge.sale {
  background-color: #8e74d3;
}
.banner-item__image {
  overflow: hidden;
  position: relative;
}
.banner-item__image::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(34, 34, 34, 0.7);
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.banner-item__image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.banner-item__content {
  max-width: 400px;
  width: 100%;
  text-align: center;
  margin: 0 auto;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  right: 0;
  z-index: 3;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (max-width: 575px) {
  .banner-item__content {
    padding: 0 15px;
  }
}
.banner-item__title {
  font-size: 42px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-item__title {
    font-size: 26px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item__title {
    font-size: 24px;
  }
}
.banner-item__divider {
  width: 80px;
  height: 2px;
  background-color: #ffffff;
  margin: 0 auto;
}
.banner-item__description {
  color: #ffffff;
  font-size: 22px;
  line-height: 1.5;
  margin: 20px 0px 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-item__description {
    font-size: 16px;
    margin: 15px 0px 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item__description {
    font-size: 18px;
    margin: 15px 0px 20px;
  }
}
@media only screen and (max-width: 575px) {
  .banner-item__description {
    font-size: 16px;
    margin: 15px 0px 20px;
  }
}
.banner-item__btn {
  padding: 15px 45px;
  font-size: 16px;
  font-weight: normal;
  line-height: 20px;
  color: #ffffff;
  border: 1px solid #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item__btn {
    padding: 12px 25px;
    font-size: 14px;
  }
}
.banner-item:hover .banner-item__image img {
  transform: scale(1.1);
}
.banner-item:hover .banner-item__image::before {
  opacity: 1;
  visibility: visible;
}
.banner-item:hover .banner-item__content {
  opacity: 1;
  visibility: visible;
}

.banner-item-2 {
  padding: 40px 0;
  height: 100%;
}
.banner-item-2.banner-bg-01 {
  background-color: #f5e7dc;
}
.banner-item-2.banner-bg-02 {
  background-color: #efddc8;
  background-size: contain;
  background-position: center left;
  background-repeat: no-repeat;
}
.banner-item-2__inner {
  max-width: 700px;
  margin: 0 auto;
}
.banner-item-2__image {
  width: 40%;
}
@media only screen and (max-width: 767px) {
  .banner-item-2__image {
    width: 100%;
  }
}
.banner-item-2__image img {
  width: 200px;
  height: 200px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
  margin: 0 auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-item-2__image img {
    width: 130px;
    height: 130px;
  }
}
@media only screen and (max-width: 767px) {
  .banner-item-2__image img {
    width: 130px;
    height: 130px;
    margin-bottom: 20px;
  }
}
.banner-item-2__content {
  width: 60%;
}
@media only screen and (max-width: 767px) {
  .banner-item-2__content {
    width: 100%;
  }
}
.banner-item-2__content > * {
  margin-top: 10px;
}
.banner-item-2__content > *:first-child {
  margin-top: 0;
}
.banner-item-2__sub-title {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 26px;
  font-weight: 400;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item-2__sub-title {
    font-size: 20px;
  }
}
.banner-item-2__title {
  color: #ed8e52;
  font-family: "Playfair Display", serif;
  font-size: 100px;
  font-weight: 400;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item-2__title {
    font-size: 60px;
  }
}
.banner-item-2__discount {
  display: block;
  color: #ed8e52;
  font-family: "Playfair Display", serif;
  font-weight: 400;
  line-height: 1.2;
  display: flex;
  justify-content: center;
  align-items: center;
}
.banner-item-2__discount .discount-sale {
  font-size: 24px;
  font-style: italic;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item-2__discount .discount-sale {
    font-size: 20px;
  }
}
.banner-item-2__discount .discount-count {
  font-size: 120px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .banner-item-2__discount .discount-count {
    font-size: 80px;
  }
}
.banner-item-2__btn {
  font-size: 16px;
  line-height: 20px;
  color: #363636;
  background-color: #ffffff;
  padding: 10px 25px;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.banner-item-2__btn:hover {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.05 - Product CSS
/*----------------------------------------*/
.product-section .tab-content {
  overflow: hidden;
}

.single-product {
  width: 100%;
  display: flex;
  flex-flow: column nowrap;
  margin-top: 30px;
  margin-bottom: -4px;
}
.single-product__thumbnail {
  width: 100%;
  height: 100%;
  position: relative;
  flex-grow: 2;
  flex-basis: 100%;
  background-color: #f9f9f9;
}
.single-product__thumbnail.border {
  border-color: #d3d3d3 !important;
}
.single-product__thumbnail--badge {
  position: absolute;
  z-index: 3;
  font-size: 10px;
  padding: 0 15px;
  line-height: 23px;
  text-align: center;
  color: #ffffff;
  top: 20px;
  min-width: 60px;
  text-transform: uppercase;
}
.single-product__thumbnail--badge.onsale {
  background-color: #363636;
}
.single-product__thumbnail--badge.out-of-stock {
  background-color: #adadad;
}
.single-product__thumbnail--holder {
  width: 100%;
  height: 100%;
  display: block;
}
.single-product__thumbnail--holder a {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  padding-bottom: 102%;
}
.single-product__thumbnail--holder a img {
  aspect-ratio: 1/1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  background-position: center center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border-radius: 10px;
}
.single-product__thumbnail--holder a .product-hover {
  opacity: 0;
  visibility: hidden;
}
.single-product__thumbnail--meta {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background: none;
  width: auto;
  padding: 13px;
  border-radius: 50px;
  font-size: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .single-product__thumbnail--meta {
    left: 20px;
    bottom: 20px;
  }
}
.single-product__thumbnail--meta::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 46px;
  height: 46px;
  max-width: 100%;
  max-height: 100%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  z-index: -1;
  background-color: #ffffff;
  border-radius: inherit;
}
.single-product__thumbnail--meta button, .single-product__thumbnail--meta a {
  color: #918684;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 0.8;
  padding: 0;
  margin: 0;
  border: 0;
  background: none;
}
.single-product__thumbnail--meta button:not(.toggle-btn), .single-product__thumbnail--meta a:not(.toggle-btn) {
  display: none;
  -webkit-animation-name: p-meta-left;
          animation-name: p-meta-left;
  -webkit-animation-duration: 500ms;
          animation-duration: 500ms;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  -webkit-animation-iteration-count: 1;
          animation-iteration-count: 1;
  -webkit-animation-direction: normal;
          animation-direction: normal;
}
.single-product__thumbnail--meta button:nth-of-type(2), .single-product__thumbnail--meta a:nth-of-type(2) {
  -webkit-animation-delay: 50ms;
          animation-delay: 50ms;
}
.single-product__thumbnail--meta button:nth-of-type(3), .single-product__thumbnail--meta a:nth-of-type(3) {
  -webkit-animation-delay: 100ms;
          animation-delay: 100ms;
}
.single-product__thumbnail--meta button:nth-of-type(4), .single-product__thumbnail--meta a:nth-of-type(4) {
  -webkit-animation-delay: 150ms;
          animation-delay: 150ms;
}
.single-product__thumbnail--meta button:nth-of-type(5), .single-product__thumbnail--meta a:nth-of-type(5) {
  -webkit-animation-delay: 200ms;
          animation-delay: 200ms;
}
.single-product__thumbnail--meta:hover {
  width: 180px;
}
.single-product__thumbnail--meta:hover::before {
  width: 100%;
  height: 100%;
}
.single-product__thumbnail--meta:hover button:not(.toggle-btn), .single-product__thumbnail--meta:hover a:not(.toggle-btn) {
  display: block;
}
.single-product__thumbnail--meta:hover button.toggle-btn, .single-product__thumbnail--meta:hover a.toggle-btn {
  display: none;
}
.single-product__thumbnail--meta-2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  z-index: 1;
  padding: 13px;
  display: flex;
  justify-content: space-around;
  opacity: 0;
  visibility: hidden;
}
.single-product__thumbnail--meta-2::before {
  position: absolute;
  content: "";
  width: 46px;
  height: 46px;
  left: 0;
  top: 0;
  transition: all 550ms ease 50ms;
  background-color: #ffffff;
  border-radius: 50px;
  z-index: -1;
  max-width: 100%;
  max-height: 100%;
}
.single-product__thumbnail--meta-2 button, .single-product__thumbnail--meta-2 a {
  color: #918684;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 1;
  font-size: 20px;
  font-weight: 600;
  padding: 0;
  margin: 0;
  border: 0;
  background: none;
  -webkit-animation-duration: 500ms;
          animation-duration: 500ms;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  -webkit-animation-iteration-count: 1;
          animation-iteration-count: 1;
  -webkit-animation-direction: normal;
          animation-direction: normal;
}
.single-product__thumbnail--meta-2 button:nth-of-type(2), .single-product__thumbnail--meta-2 a:nth-of-type(2) {
  -webkit-animation-delay: 50ms;
          animation-delay: 50ms;
}
.single-product__thumbnail--meta-2 button:nth-of-type(3), .single-product__thumbnail--meta-2 a:nth-of-type(3) {
  -webkit-animation-delay: 100ms;
          animation-delay: 100ms;
}
.single-product__thumbnail--meta-2 button:nth-of-type(4), .single-product__thumbnail--meta-2 a:nth-of-type(4) {
  -webkit-animation-delay: 150ms;
          animation-delay: 150ms;
}
.single-product__thumbnail--meta-2 button:nth-of-type(5), .single-product__thumbnail--meta-2 a:nth-of-type(5) {
  -webkit-animation-delay: 200ms;
          animation-delay: 200ms;
}
.single-product__thumbnail--meta-2 button:hover, .single-product__thumbnail--meta-2 a:hover {
  color: #363636;
}
.single-product__thumbnail--meta-3 {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2;
}
.single-product__thumbnail--meta-3 button, .single-product__thumbnail--meta-3 a {
  color: #363636;
  background-color: #ffffff;
  width: 38px;
  height: 38px;
  text-align: center;
  border-radius: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  padding: 0;
  margin: 0;
  border: 0;
}
.single-product__thumbnail--meta-3 button i, .single-product__thumbnail--meta-3 a i {
  padding-top: 3px;
}
.single-product__thumbnail--meta-3 button:hover, .single-product__thumbnail--meta-3 a:hover {
  background-color: #d68d67;
  color: #ffffff;
}
.single-product__thumbnail--meta-4 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  display: flex;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-product__thumbnail--meta-4 button, .single-product__thumbnail--meta-4 a {
  color: #363636;
  background-color: #ffffff;
  width: 32px;
  height: 32px;
  text-align: center;
  border-radius: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  padding: 0;
  margin: 0;
  border: 0;
  -webkit-animation-duration: 500ms;
          animation-duration: 500ms;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  -webkit-animation-iteration-count: 1;
          animation-iteration-count: 1;
  -webkit-animation-direction: normal;
          animation-direction: normal;
}
.single-product__thumbnail--meta-4 button:nth-of-type(2), .single-product__thumbnail--meta-4 a:nth-of-type(2) {
  -webkit-animation-delay: 50ms;
          animation-delay: 50ms;
}
.single-product__thumbnail--meta-4 button:nth-of-type(3), .single-product__thumbnail--meta-4 a:nth-of-type(3) {
  -webkit-animation-delay: 100ms;
          animation-delay: 100ms;
}
.single-product__thumbnail--meta-4 button:nth-of-type(4), .single-product__thumbnail--meta-4 a:nth-of-type(4) {
  -webkit-animation-delay: 150ms;
          animation-delay: 150ms;
}
.single-product__thumbnail--meta-4 button:nth-of-type(5), .single-product__thumbnail--meta-4 a:nth-of-type(5) {
  -webkit-animation-delay: 200ms;
          animation-delay: 200ms;
}
.single-product__thumbnail--meta-4 button:hover, .single-product__thumbnail--meta-4 a:hover {
  background-color: #d68d67;
  color: #ffffff;
}
.single-product__info {
  padding-top: 20px;
}
.single-product__info--tags {
  font-size: 16px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  color: #938683;
  line-height: 1.5;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .single-product__info--tags {
    font-size: 15px;
  }
}
.single-product__info--tags a:hover {
  color: #d68d67;
}
.single-product__info--title {
  font-size: 18px;
  font-weight: 600;
  font-family: "Playfair Display", serif;
  padding: 4px 0 8px;
  line-height: 1.2;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .single-product__info--title {
    font-size: 18px;
  }
}
.single-product__info--title a {
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-product__info--title a:hover {
  color: #d68d67;
}
.single-product__info--price {
  color: #b28686;
  font-size: 20px;
  line-height: 1.2;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .single-product__info--price {
    font-size: 18px;
  }
}
.single-product__info--price del {
  text-decoration: line-through;
}
.single-product__info--price ins {
  text-decoration: none;
}
.single-product__info--rating {
  position: relative;
  margin-top: 5px;
}
.single-product__info--rating span {
  overflow: hidden;
  position: relative;
  height: 1em;
  line-height: 1;
  font-size: 13px;
  color: #b3bdbc;
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 0.2em;
}
.single-product__info--rating span::before {
  content: "\ea5e\ea5e\ea5e\ea5e\ea5e";
  display: inline-block;
  font: normal normal normal 1em/1 LaStudioIcons;
  speak: none;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.single-product__info--rating .star-rating > span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  color: #f5a623;
}
.single-product__info--color-swatch {
  margin-top: 5px;
}
.single-product__info--color-swatch .color-item {
  width: 15px;
  height: 15px;
  border: 0;
  border-radius: 50px;
  border: 1px solid #ffffff;
}
.single-product__info--color-swatch .color-item.active {
  box-shadow: 0 0 0 1px #d68d67;
}
.single-product__info--color-swatch .color-item.blue {
  background-color: #8224e3;
}
.single-product__info--color-swatch .color-item.bronze {
  background-color: #dbd5bc;
}
.single-product__info--color-swatch .color-item.green {
  background-color: #81d742;
}
.single-product__info--color-swatch .color-item.red {
  background-color: #c60000;
}
.single-product__info--color-swatch .color-item.yellow {
  background-color: #efd465;
}
.single-product__info--bar {
  padding-top: 10px;
}
.single-product__info--bar-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.single-product__info--text {
  font-size: 14px;
  color: #363636;
  margin-bottom: 4px;
}
.single-product__info--bar-progress {
  background: #dedede;
  width: 100%;
  height: 6px;
  overflow: hidden;
  position: relative;
}
.single-product__info--bar-value {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #8db704;
}
.single-product__info--add-to-cart {
  margin-top: 20px;
  margin-bottom: 4px;
}
.single-product__info--add-to-cart .add-to-cart {
  border: 0;
  background-color: #d68d67;
  font-size: 16px;
  line-height: 20px;
  padding: 14px 30px 13px;
  border-radius: 30px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  color: #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .single-product__info--add-to-cart .add-to-cart {
    font-size: 15px;
    padding: 12px 20px 10px;
  }
}
.single-product__info--add-to-cart .add-to-cart:hover {
  background-color: #347e56;
}
.single-product:hover .single-product__thumbnail--holder a .product-hover {
  opacity: 1;
  visibility: visible;
}
.single-product:hover .single-product__thumbnail--meta-2, .single-product:hover .single-product__thumbnail--meta-4 {
  opacity: 1;
  visibility: visible;
}
.single-product:hover .single-product__thumbnail--meta-2::before, .single-product:hover .single-product__thumbnail--meta-4::before {
  width: 100%;
}
.single-product:hover .single-product__thumbnail--meta-2 button, .single-product:hover .single-product__thumbnail--meta-2 a, .single-product:hover .single-product__thumbnail--meta-4 button, .single-product:hover .single-product__thumbnail--meta-4 a {
  -webkit-animation-name: btn-none;
          animation-name: btn-none;
}
.single-product--dark .single-product__thumbnail--badge.onsale {
  background-color: #363636;
}
.single-product--dark .single-product__info--tags {
  color: #938683;
}
.single-product--dark .single-product__info--title a {
  color: #ffffff;
}
.single-product--dark .single-product__info--title a:hover {
  color: #d68d67;
}
.single-product--dark .single-product__info--price {
  color: #494949;
}
.single-product--dark .single-product__info--price del {
  color: #494949;
}
.single-product-02 .single-product__thumbnail {
  border: 1px solid #b9b9a9;
  padding: 5% 0;
  background-color: #ebebe8;
}
.single-product-02 .single-product__thumbnail--meta-2 {
  width: auto;
  height: 165px;
  flex-direction: column;
  top: auto;
  left: auto;
  transform: translate(0);
  right: 20px;
  bottom: 20px;
}
.single-product-02 .single-product__thumbnail--meta-2::before {
  top: auto;
  bottom: 0;
}
.single-product-02 .single-product__thumbnail a img {
  background-color: #ebebe8;
}
.single-product-02 .single-product__info--tags {
  font-family: "Josefin Sans", sans-serif;
  font-size: 16px;
  font-weight: 400;
}
.single-product-02 .single-product__info--title {
  font-family: "Josefin Sans", sans-serif;
  font-size: 16px;
  font-weight: 400;
  text-transform: uppercase;
}
.single-product-02 .single-product__info--price {
  color: #d8b871;
}
.single-product-02 .single-product__info--rating span {
  font-size: 12px;
}
.single-product-02:hover .single-product__thumbnail--meta-2::before {
  width: 100%;
  height: 100%;
}

@-webkit-keyframes p-meta-left {
  from {
    opacity: 0;
    transform: translate3d(-50%, 0, 0);
  }
  to {
    opacity: 1;
    transform: none;
  }
}

@keyframes p-meta-left {
  from {
    opacity: 0;
    transform: translate3d(-50%, 0, 0);
  }
  to {
    opacity: 1;
    transform: none;
  }
}
@-webkit-keyframes btn-none {
  to {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
  }
}
@keyframes btn-none {
  to {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
  }
}
.product-wrapper {
  padding-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-wrapper {
    padding-top: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-wrapper {
    padding-top: 10px;
  }
}

.product-arrow {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}
.product-arrow > * {
  margin: 0 10px;
}
.product-arrow > *:first-child {
  margin-left: 0;
}
.product-arrow > *:last-child {
  margin-right: 0;
}

/*----------------------------------------*/
/*  02.06 - Features CSS
/*----------------------------------------*/
.features-text {
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}
.features-text .swiper-slide {
  flex-shrink: inherit;
  width: auto;
}

.features-text-item span {
  font-family: "Playfair Display", serif;
  font-size: 86px;
  font-weight: 400;
  line-height: 1;
  color: #d6936b;
  white-space: nowrap;
  display: block;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .features-text-item span {
    font-size: 56px;
  }
}
@media only screen and (max-width: 767px) {
  .features-text-item span {
    font-size: 30px;
  }
}

/*----------------------------------------*/
/*  02.07 - Special offer CSS
/*----------------------------------------*/
.special-offer-wrapper {
  padding-top: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-wrapper {
    padding-top: 0;
  }
}

.special-offer-image {
  margin-top: 50px;
  position: relative;
}
.special-offer-image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.special-offer-image .badge {
  position: absolute;
  width: 25%;
  top: -3.2%;
  left: -2.6%;
  z-index: 1;
  display: block;
}

.special-offer-content {
  padding-left: 16%;
  margin-top: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .special-offer-content {
    padding-left: 8.5%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .special-offer-content {
    padding-left: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .special-offer-content {
    padding-left: 0;
  }
}
.special-offer-content > * {
  padding-top: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-content > * {
    padding-top: 10px;
  }
}
.special-offer-content > *:first-child {
  padding-top: 0;
}
.special-offer-content__title {
  font-size: 46px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  line-height: 1;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .special-offer-content__title {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-content__title {
    font-size: 26px;
  }
}
.special-offer-content__price {
  font-size: 38px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  line-height: 1;
  color: #b28686;
  margin-bottom: 5px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .special-offer-content__price {
    font-size: 24px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-content__price {
    font-size: 20px;
  }
}
.special-offer-content__description {
  font-size: 32px;
  font-weight: 400;
  font-style: italic;
  font-family: "Playfair Display", serif;
  line-height: 1.5;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .special-offer-content__description {
    font-size: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .special-offer-content__description {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .special-offer-content__description {
    font-size: 20px;
  }
}
.special-offer-content__btns {
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;
  margin-top: -20px;
}
.special-offer-content__btns > * {
  padding: 20px 10px 0;
}
.special-offer-content__btn {
  font-size: 18px;
  line-height: 20px;
  background-color: #b85d48;
  border: 1px solid #b85d48;
  color: #ffffff;
  padding: 20px;
  min-width: 180px;
  width: 100%;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .special-offer-content__btn {
    min-width: 140px;
    font-size: 16px;
    padding: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-content__btn {
    min-width: 130px;
    font-size: 14px;
    padding: 12px;
  }
}
.special-offer-content__btn:hover {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
.special-offer-content .button-02 .special-offer-content__btn {
  background-color: transparent;
  color: #363636;
  border-color: #c6c6c6;
}
.special-offer-content .button-02 .special-offer-content__btn:hover {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
.special-offer-content__social {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.special-offer-content__social-label {
  color: #363636;
  font-size: 18px;
  font-style: italic;
  font-family: "Playfair Display", serif;
  padding-top: 20px;
  padding-right: 20px;
  white-space: nowrap;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-content__social-label {
    padding-right: 10px;
  }
}
.special-offer-content__social-icon {
  display: flex;
  flex-wrap: wrap;
}
.special-offer-content__social-icon li {
  padding-top: 20px;
  padding-right: 20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .special-offer-content__social-icon li {
    padding-right: 10px;
  }
}
.special-offer-content__social-icon li:last-child {
  padding-right: 0;
}
.special-offer-content__social-icon li a {
  border: 1px solid #ededed;
  color: #d3d3d3;
  font-size: 16px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .special-offer-content__social-icon li a {
    width: 34px;
    height: 34px;
  }
}
.special-offer-content__social-icon li a:hover {
  border-color: #b28686;
  color: #b28686;
}

/*----------------------------------------*/
/*  02.08 - CountDown CSS
/*----------------------------------------*/
.countdown-content__countdown {
  display: flex;
  padding-bottom: 20px;
}
@media only screen and (max-width: 767px) {
  .countdown-content__countdown {
    justify-content: center;
    padding-bottom: 0;
  }
}
.countdown-content__countdown > * {
  padding: 0 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .countdown-content__countdown > * {
    padding: 0 15px;
  }
}
@media only screen and (max-width: 767px) {
  .countdown-content__countdown > * {
    padding: 0 5px;
  }
}
.countdown-content__timer-item {
  display: flex;
  flex-flow: column nowrap;
  align-items: stretch;
  text-align: center;
  margin: 5px;
}
@media only screen and (max-width: 767px) {
  .countdown-content__timer-item {
    justify-content: center;
  }
}
.countdown-content__timer-item--value {
  color: #363636;
  font-size: 56px;
  font-weight: 600;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .countdown-content__timer-item--value {
    font-size: 36px;
  }
}
.countdown-content__timer-item--label {
  font-size: 24px;
  font-weight: 400;
  line-height: 1;
  margin-top: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .countdown-content__timer-item--label {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .countdown-content__timer-item--label {
    font-size: 16px;
  }
}

.countdown-content-two {
  display: flex;
  margin: 35px 0 35px -10%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .countdown-content-two {
    margin: 20px 0 20px -8%;
  }
}
@media only screen and (max-width: 767px) {
  .countdown-content-two {
    margin: 30px 0;
  }
}
.countdown-content-two__timer-item {
  text-align: center;
  width: 24%;
  height: 35px;
  border-right: 2px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.countdown-content-two__timer-item:last-child {
  border-right: 0;
}
.countdown-content-two__timer-item--value {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 36px;
  font-weight: 400;
  line-height: 1.2;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .countdown-content-two__timer-item--value {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .countdown-content-two__timer-item--value {
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .countdown-content-two__timer-item--value {
    font-size: 28px;
  }
}
.countdown-content-two__timer-item--label {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.2;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .countdown-content-two__timer-item--label {
    font-size: 16px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .countdown-content-two__timer-item--label {
    font-size: 16px;
  }
}

/*----------------------------------------*/
/*  02.09 - Popular brands CSS
/*----------------------------------------*/
.brand-section {
  background-color: #fff5ed;
}

.brand-active {
  padding-top: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .brand-active {
    padding-top: 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .brand-active {
    padding-top: 30px;
  }
}

.brand-item {
  text-align: center;
}
.brand-item img {
  width: auto;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  padding: 10px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  filter: brightness(100%) contrast(100%) saturate(0%) blur(0) hue-rotate(0deg);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .brand-item img {
    width: 80%;
  }
}
@media only screen and (max-width: 767px) {
  .brand-item img {
    width: 80%;
  }
}
@media only screen and (max-width: 575px) {
  .brand-item img {
    width: 100%;
  }
}
.brand-item:hover img {
  transform: scale(1.1);
}

.brand-wrapper {
  padding-top: 30px;
}

.brand-item-2 {
  width: 75%;
  padding: 5%;
  margin: 0 auto;
  overflow: hidden;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .brand-item-2 {
    width: 95%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .brand-item-2 {
    width: 85%;
  }
}
@media only screen and (max-width: 767px) {
  .brand-item-2 {
    width: 95%;
  }
}
.brand-item-2 img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.brand-item-2:hover img {
  transform: scale(1.1);
}

/*----------------------------------------*/
/*  02.10 - Blog CSS
/*----------------------------------------*/
.blog-section {
  background-color: #fff5ed;
}

.blog-wrapper {
  padding-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-wrapper {
    padding-top: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-wrapper {
    padding-top: 10px;
  }
}

.blog-item {
  margin-top: 30px;
  position: relative;
}
.blog-item__image {
  overflow: hidden;
}
.blog-item__image a {
  display: block;
  position: relative;
}
.blog-item__image a::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.7) 100%);
  z-index: 1;
}
.blog-item__image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.blog-item__content {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 2;
  padding: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-item__content {
    padding: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-item__content {
    padding: 40px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-item__content {
    padding: 20px;
  }
}
.blog-item__inner {
  overflow: hidden;
}
.blog-item__title {
  font-size: 46px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  line-height: 1.3;
  margin-bottom: 15px;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-item__title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-item__title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-item__title {
    font-size: 22px;
  }
}
.blog-item__title a {
  color: inherit;
}
.blog-item__meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.blog-item__meta li {
  display: flex;
}
.blog-item__meta li:not(:first-child)::before {
  content: "•";
  margin-left: 0.3rem;
  margin-right: 0.3rem;
  color: #ffffff;
  line-height: 1;
}
.blog-item__meta li span {
  color: #ffffff;
  font-size: 16px;
  text-transform: capitalize;
  line-height: 1;
  display: block;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-item__meta li span {
    font-size: 14px;
  }
}
.blog-item__meta li a {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.blog-item__btn-wrap {
  margin-bottom: -60px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 0;
}
.blog-item__btn {
  font-size: 16px;
  line-height: 1;
  padding: 15px 30px;
  border: 1px solid #ffffff;
  background-color: transparent;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 575px) {
  .blog-item__btn {
    padding: 12px 25px;
    font-size: 14px;
  }
}
.blog-item__btn:hover {
  background-color: #b28686;
  border-color: #b28686;
  color: #ffffff;
}
.blog-item__play {
  background-color: #ffffff;
  color: #363636;
  font-size: 12px;
  width: 54px;
  height: 54px;
  border-radius: 50%;
  position: absolute;
  top: 70%;
  left: 88%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-item__play {
    width: 46px;
    height: 46px;
    font-size: 12px;
  }
}
.blog-item__play svg {
  width: 1em;
  height: 1em;
  display: inline-block;
  vertical-align: middle;
}
.blog-item:hover .blog-item__image img {
  width: 100%;
  transform: scale(1.05);
}
.blog-item:hover .blog-item__btn-wrap {
  margin-bottom: 0;
  opacity: 1;
}

.blog-item-2__image {
  overflow: hidden;
}
.blog-item-2__image a {
  display: block;
  position: relative;
  padding-bottom: 55%;
}
.blog-item-2__image img {
  width: 100%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.blog-item-2__content {
  margin-top: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-item-2__content {
    margin-top: 20px;
  }
}
.blog-item-2__content--meta {
  display: flex;
  flex-wrap: wrap;
}
.blog-item-2__content--meta li {
  display: flex;
}
.blog-item-2__content--meta li:not(:first-child)::before {
  content: "•";
  margin-left: 0.3rem;
  margin-right: 0.3rem;
  color: #494949;
  line-height: 1;
}
.blog-item-2__content--meta li span {
  color: #494949;
  font-size: 12px;
  line-height: 1;
}
.blog-item-2__content--meta li a {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.blog-item-2__content--meta li a:hover {
  color: #d68d67;
}
.blog-item-2__content--title {
  font-size: 36px;
  font-weight: 400;
  line-height: 1;
  margin: 20px 0 15px 0;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-item-2__content--title {
    font-size: 26px;
    margin: 15px 0 10px 0;
  }
}
@media only screen and (max-width: 767px) {
  .blog-item-2__content--title {
    font-size: 24px;
    margin: 15px 0 10px 0;
  }
}
.blog-item-2__content--title a {
  color: inherit;
}
.blog-item-2__content--title:hover {
  color: #d68d67;
}
.blog-item-2__content--description {
  color: #494949;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
  margin-bottom: -8px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-item-2__content--description {
    font-size: 16px;
    margin-bottom: -4px;
  }
}
.blog-item-2__content--btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 48px;
  background-color: #ffffff;
  color: #363636;
  padding: 0 70px;
  border: 1px solid #363636;
  margin-top: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-item-2__content--btn {
    margin-top: 20px;
    padding: 0 30px;
  }
}
.blog-item-2__content--btn:hover {
  background-color: #363636;
  color: #ffffff;
}
.blog-item-2:hover .blog-item-3__image img {
  width: 100%;
  transform: scale(1.05);
}

.blog-active {
  position: relative;
}
.blog-active .swiper-button-next {
  right: 5%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-active .swiper-button-next {
    right: -20px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-active .swiper-button-next {
    right: -15px;
  }
}
.blog-active .swiper-button-prev {
  left: 5%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-active .swiper-button-prev {
    left: -20px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-active .swiper-button-prev {
    left: -15px;
  }
}

.related-container {
  max-width: 1720px;
}

.related-blog-row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -30px;
  margin-right: -30px;
  padding-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .related-blog-row {
    margin-left: -15px;
    margin-right: -15px;
    padding-top: 10px;
  }
}
@media only screen and (max-width: 767px) {
  .related-blog-row {
    padding-top: 0;
  }
}

.related-blog-col {
  width: calc(100% / 3);
  padding: 0 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .related-blog-col {
    padding: 0 15px;
  }
}
@media only screen and (max-width: 767px) {
  .related-blog-col {
    width: 100%;
  }
}

.related-blog-item {
  margin-top: 30px;
}
.related-blog-item__image a {
  display: block;
  padding-bottom: 60%;
  position: relative;
  overflow: hidden;
}
.related-blog-item__image a img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.related-blog-item__content {
  margin-top: 30px;
}
.related-blog-item__category {
  display: flex;
  flex-wrap: wrap;
}
.related-blog-item__category li {
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  line-height: 1;
  position: relative;
  color: #d68d67;
}
.related-blog-item__category li:not(:first-child)::before {
  content: "•";
}
.related-blog-item__title {
  font-size: 32px;
  font-family: "Playfair Display", serif;
  font-weight: 400;
  line-height: 1;
  margin: 10px 0 20px;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .related-blog-item__title {
    font-size: 24px;
  }
}
.related-blog-item__meta {
  display: flex;
  flex-wrap: wrap;
}
.related-blog-item__meta li {
  font-size: 14px;
  line-height: 1.13;
  color: #494949;
  position: relative;
}
.related-blog-item__meta li:not(:first-child)::before {
  content: "•";
  margin-left: 5px;
  margin-right: 2px;
}
.related-blog-item:hover .related-blog-item__image img {
  transform: scale(1.05);
}
.related-blog-item:hover .related-blog-item__content {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}

/*----------------------------------------*/
/*  02.11 - Instagram CSS
/*----------------------------------------*/
.instagram-wrapper {
  margin-top: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .instagram-wrapper {
    margin-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .instagram-wrapper {
    margin-top: 40px;
  }
}

.instagram-item {
  position: relative;
  overflow: hidden;
  margin-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .instagram-item {
    margin-top: 22px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .instagram-item {
    margin-top: 15px;
  }
}
.instagram-item:first-child {
  margin-top: 0;
}
.instagram-item a {
  display: block;
  position: relative;
}
.instagram-item__image {
  position: relative;
}
.instagram-item__image::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.21, 0.75, 0.27, 0.96);
  pointer-events: none;
  background-color: #363636;
  z-index: 1;
}
.instagram-item__image img {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.instagram-item__icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.5s cubic-bezier(0.21, 0.75, 0.27, 0.96);
  z-index: 2;
  opacity: 0;
}
.instagram-item__icon i {
  font-size: 30px;
  color: #ffffff;
}
.instagram-item:hover .instagram-item__image::before {
  opacity: 0.2;
}
.instagram-item:hover .instagram-item__image img {
  transform: scale(1.1);
}
.instagram-item:hover .instagram-item__icon {
  opacity: 1;
}

.instagram-active .swiper {
  overflow: visible;
}

.instagram-col {
  width: 50%;
  padding: 5px;
}
@media only screen and (max-width: 575px) {
  .instagram-col {
    width: 100%;
  }
}

.instagram-wrapper-2 {
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -5px;
  margin-bottom: -5px;
}

/*----------------------------------------*/
/*  02.12 - Our Info CSS
/*----------------------------------------*/
.our-info-section {
  border-top: 1px solid #d1d1d1;
}

.our-info-item__title {
  font-size: 34px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  line-height: 1;
  color: #363636;
  margin-bottom: 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .our-info-item__title {
    font-size: 24px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .our-info-item__title {
    font-size: 20px;
  }
}
.our-info-item__info {
  color: #363636;
  font-size: 24px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  line-height: 1.25;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .our-info-item__info {
    font-size: 18px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .our-info-item__info {
    font-size: 16px;
  }
}

/*----------------------------------------*/
/*  02.13 - About CSS
/*----------------------------------------*/
.about-section {
  position: relative;
  z-index: 1;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}
.about-section::before {
  position: absolute;
  content: "";
  background-image: url(../images/about/about-shape.png);
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: contain;
  display: block;
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
}
.about-section__year {
  color: #ffffff;
  font-family: "Playfair Display", serif;
  font-size: 250px;
  font-weight: bold;
  line-height: 1em;
  position: absolute;
  right: 8%;
  bottom: 21%;
  mix-blend-mode: overlay;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .about-section__year {
    font-size: 150px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-section__year {
    font-size: 130px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-section__year {
    font-size: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .about-section__year {
    font-size: 68px;
  }
}
@media only screen and (max-width: 575px) {
  .about-section__year {
    font-size: 18vw;
    bottom: 12%;
  }
}

.about-height {
  height: 970px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .about-height {
    height: 720px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-height {
    height: 620px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-height {
    height: 520px;
  }
}
@media only screen and (max-width: 767px) {
  .about-height {
    height: 410px;
  }
}

.about-content {
  position: relative;
}
.about-content > * {
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .about-content > * {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 575px) {
  .about-content > * {
    margin-top: 15px;
  }
}
.about-content > *:first-child {
  margin-top: 0;
}
.about-content__sub-title {
  color: #ffffff;
  font-size: 24px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.4em;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .about-content__sub-title {
    font-size: 20px;
    letter-spacing: 0.2em;
  }
}
@media only screen and (max-width: 767px) {
  .about-content__sub-title {
    font-size: 18px;
    letter-spacing: 0.1em;
  }
}
@media only screen and (max-width: 575px) {
  .about-content__sub-title {
    font-size: 16px;
    letter-spacing: 0;
  }
}
.about-content__title {
  color: #ffffff;
  font-family: "Playfair Display", serif;
  font-size: 86px;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 1;
  padding-bottom: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .about-content__title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 767px) {
  .about-content__title {
    font-size: 48px;
  }
}
@media only screen and (max-width: 575px) {
  .about-content__title {
    font-size: 38px;
    padding-bottom: 10px;
  }
}
.about-content__btn {
  font-size: 24px;
  font-weight: 400;
  line-height: 30px;
  color: #363636;
  background-color: #ffffff;
  padding: 25px;
  min-width: 280px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .about-content__btn {
    min-width: 220px;
    font-size: 20px;
    padding: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .about-content__btn {
    min-width: 180px;
    font-size: 16px;
    padding: 10px;
  }
}
.about-content__btn:hover {
  background-color: #395749;
  color: #ffffff;
}

.about-title {
  max-width: 970px;
  margin: 0 auto;
}
.about-title__title {
  font-size: 64px;
  font-weight: 400;
  line-height: 1;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-title__title {
    font-size: 42px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-title__title {
    font-size: 32px;
  }
}
@media only screen and (max-width: 767px) {
  .about-title__title {
    font-size: 28px;
  }
}
.about-title p {
  text-align: center;
  color: #494949;
  font-size: 20px;
  line-height: 1.6em;
  margin-top: 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-title p {
    font-size: 18px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .about-title p {
    font-size: 16px;
  }
}

.about-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .about-items {
    padding-top: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .about-items {
    padding-top: 10px;
  }
}

.about-col {
  width: 27%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-col {
    width: 29%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-col {
    width: 30%;
  }
}
@media only screen and (max-width: 767px) {
  .about-col {
    width: 100%;
  }
}

.about-item {
  margin-top: 30px;
}
.about-item__top {
  display: block;
  position: relative;
}
.about-item__top--image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  border-top-left-radius: 150px;
  border-top-right-radius: 150px;
}
.about-item__top--title {
  font-size: 46px;
  font-weight: 400;
  line-height: 1;
  color: #ffffff;
  position: absolute;
  bottom: 50px;
  left: 0;
  width: 100%;
  text-align: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-item__top--title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .about-item__top--title {
    font-size: 28px;
  }
}
.about-item__bottom {
  padding: 40px 30px;
  border: 1px solid #a8a8a8;
  text-align: center;
  position: relative;
  margin-top: 115px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .about-item__bottom {
    padding: 30px 15px 25px;
    margin-top: 65px;
  }
}
.about-item__bottom::before {
  position: absolute;
  content: "";
  top: -116px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 115px;
  background-color: #a8a8a8;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .about-item__bottom::before {
    top: -66px;
    height: 65px;
  }
}
.about-item__bottom--count {
  width: 50px;
  height: 50px;
  line-height: 46px;
  z-index: 1;
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  fill: #494949;
  color: #494949;
  background-color: #ffffff;
  border: 1px solid #a8a8a8;
  border-radius: 50px;
  padding: 2px 0 0 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .about-item__bottom--count {
    font-size: 16px;
  }
}
.about-item__bottom--description {
  font-size: 20px;
  line-height: 1.6;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .about-item__bottom--description {
    font-size: 16px;
  }
}

.about-video {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 880px;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-video {
    height: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-video {
    height: 450px;
  }
}
@media only screen and (max-width: 767px) {
  .about-video {
    height: 350px;
    text-align: center;
  }
}
.about-video__title {
  color: #ffffff;
  font-size: 56px;
  font-weight: 400;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .about-video__title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 575px) {
  .about-video__title {
    font-size: 28px;
  }
}
.about-video__play {
  font-size: 34px;
  border: 2px solid #ffffff;
  width: 74px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  border-color: #ffffff;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin-top: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .about-video__play {
    font-size: 24px;
    width: 56px;
    height: 56px;
  }
}
@media only screen and (max-width: 767px) {
  .about-video__play {
    font-size: 24px;
    width: 56px;
    height: 56px;
    margin: 20px auto 0;
  }
}
.about-video__play svg {
  width: 1em;
  height: auto;
}
.about-video__play:hover {
  transform: scale(1.2);
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.14 - Category CSS
/*----------------------------------------*/
.category-wrapper {
  padding-top: 60px;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-wrapper {
    padding-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .category-wrapper {
    padding-top: 40px;
  }
}

.category-item {
  position: relative;
}
.category-item__image a {
  position: relative;
  display: block;
  overflow: hidden;
}
.category-item__image a::before {
  z-index: -1;
  content: "";
  border: 1px solid #b9b9a9;
  border-radius: 50%;
  position: absolute;
  bottom: 0;
  width: 270px;
  display: flex;
  padding-bottom: 270px;
  left: 50%;
  transform: translateX(-50%);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-item__image a::before {
    width: 220px;
    padding-bottom: 220px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .category-item__image a::before {
    width: 200px;
    padding-bottom: 200px;
  }
}
@media only screen and (max-width: 767px) {
  .category-item__image a::before {
    width: 180px;
    padding-bottom: 180px;
  }
}
@media only screen and (max-width: 575px) {
  .category-item__image a::before {
    width: 90%;
    padding-bottom: 88%;
  }
}
.category-item__image img {
  width: 300px;
  -o-object-fit: cover;
     object-fit: cover;
  max-height: 340px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-item__image img {
    width: 250px;
    height: 290px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .category-item__image img {
    width: 220px;
    height: 250px;
  }
}
@media only screen and (max-width: 767px) {
  .category-item__image img {
    width: 200px;
    height: 230px;
  }
}
@media only screen and (max-width: 575px) {
  .category-item__image img {
    width: 100%;
    height: 140%;
  }
}
.category-item__title {
  font-family: "Playfair Display", serif;
  font-size: 32px;
  font-weight: normal;
  line-height: 1em;
  color: #363636;
  margin-top: 30px;
  margin-bottom: 8px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .category-item__title {
    font-size: 28px;
    margin-top: 20px;
  }
}
@media only screen and (max-width: 575px) {
  .category-item__title {
    font-size: 20px;
    margin-top: 15px;
  }
}
.category-item__title a {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.category-item__title a:hover {
  color: #d68d67;
}
.category-item:hover .category-item__image img {
  transform: scale(1.1);
}

.category-arrow {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}
@media only screen and (max-width: 767px) {
  .category-arrow {
    margin-top: 30px;
  }
}
.category-arrow .swiper-button-next, .category-arrow .swiper-button-prev {
  width: 35px;
  height: 40px;
  font-size: 32px;
  border: 0;
  border-radius: 0;
  background-color: transparent;
}
.category-arrow .swiper-button-next:hover, .category-arrow .swiper-button-prev:hover {
  background-color: transparent;
  color: #d68d67;
  border: 0;
}

/*----------------------------------------*/
/*  02.15 - Product Popular CSS
/*----------------------------------------*/
.product-popular {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 30px;
}
.product-popular.popular-style-01 {
  padding-bottom: 150px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular.popular-style-01 {
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular.popular-style-01 {
    padding-bottom: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .product-popular.popular-style-01 {
    padding-bottom: 0;
  }
}
.product-popular.popular-style-02 {
  padding-bottom: 150px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular.popular-style-02 {
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular.popular-style-02 {
    padding-bottom: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .product-popular.popular-style-02 {
    padding-bottom: 0;
  }
}
.product-popular.popular-style-03 {
  padding-top: 110px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular.popular-style-03 {
    padding-top: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .product-popular.popular-style-03 {
    padding-top: 0;
  }
}
.product-popular:nth-child(2n+0) .product-popular-content {
  padding-left: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular:nth-child(2n+0) .product-popular-content {
    padding-left: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-popular:nth-child(2n+0) .product-popular-content {
    padding-left: 0;
  }
}
.product-popular:nth-child(2n+1) .product-popular-content {
  padding-right: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular:nth-child(2n+1) .product-popular-content {
    padding-right: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-popular:nth-child(2n+1) .product-popular-content {
    padding-right: 0;
  }
}

.product-popular-image {
  position: relative;
  width: 55%;
  z-index: -1;
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .product-popular-image {
    width: 100%;
  }
}
.product-popular-image__bg {
  max-width: 600px;
  width: 100%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular-image__bg {
    max-width: 450px;
  }
}
.product-popular-image__bg img {
  width: auto;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.product-popular-image__item {
  position: absolute;
  right: 0;
  bottom: -150px;
  z-index: 1;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular-image__item {
    bottom: -100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular-image__item {
    right: 7%;
    bottom: -50px;
  }
}
@media only screen and (max-width: 767px) {
  .product-popular-image__item {
    bottom: auto;
    top: 50px;
  }
}
@media only screen and (max-width: 575px) {
  .product-popular-image__item {
    top: 35px;
  }
}
.product-popular-image__item img {
  width: 370px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .product-popular-image__item img {
    width: 300px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular-image__item img {
    width: 260px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular-image__item img {
    width: 180px;
  }
}
@media only screen and (max-width: 767px) {
  .product-popular-image__item img {
    width: 180px;
  }
}
@media only screen and (max-width: 575px) {
  .product-popular-image__item img {
    width: 30vw;
  }
}
.product-popular-image.popular-image-style-01 {
  padding-right: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular-image.popular-image-style-01 {
    padding-right: 50px;
  }
}
@media only screen and (max-width: 575px) {
  .product-popular-image.popular-image-style-01 {
    padding-right: 60px;
  }
}
.product-popular-image.popular-image-style-02 {
  padding-left: 10%;
}
@media only screen and (max-width: 767px) {
  .product-popular-image.popular-image-style-02 {
    padding-left: 0;
    padding-right: 100px;
  }
}
@media only screen and (max-width: 575px) {
  .product-popular-image.popular-image-style-02 {
    padding-right: 60px;
  }
}
.product-popular-image.popular-image-style-03 {
  padding-right: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular-image.popular-image-style-03 {
    padding-right: 50px;
  }
}
@media only screen and (max-width: 575px) {
  .product-popular-image.popular-image-style-03 {
    padding-right: 60px;
  }
}
.product-popular-image.popular-image-style-03 .product-popular-image__item {
  bottom: auto;
  top: -110px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-popular-image.popular-image-style-03 .product-popular-image__item {
    top: -50px;
  }
}
@media only screen and (max-width: 767px) {
  .product-popular-image.popular-image-style-03 .product-popular-image__item {
    top: 50px;
  }
}
@media only screen and (max-width: 575px) {
  .product-popular-image.popular-image-style-03 .product-popular-image__item {
    top: 35px;
  }
}

.product-popular-content {
  width: 45%;
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .product-popular-content {
    width: 100%;
  }
}
.product-popular-content > * {
  margin-top: 20px;
}
.product-popular-content > *:first-child {
  margin-top: 0;
}
.product-popular-content__title {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 54px;
  font-weight: 400;
  line-height: 1.2;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular-content__title {
    font-size: 38px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-popular-content__title {
    font-size: 30px;
  }
}
.product-popular-content__description p {
  font-size: 26px;
  font-weight: 400;
  line-height: 1.4;
  color: #727272;
  padding: 25px 0px 55px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular-content__description p {
    font-size: 20px;
    padding: 15px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-popular-content__description p {
    font-size: 18px;
    padding: 10px 0;
  }
}
.product-popular-content__btn {
  max-width: 250px;
  width: 100%;
  font-size: 22px;
  font-weight: normal;
  line-height: 28px;
  color: #797979;
  background-color: transparent;
  border: 1px solid #b9b9a9;
  padding: 20px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-popular-content__btn {
    padding: 15px;
    max-width: 190px;
    font-size: 18px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-popular-content__btn {
    padding: 12px;
    max-width: 160px;
    font-size: 16px;
  }
}
.product-popular-content__btn:hover {
  background-color: #395749;
  border-color: #395749;
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.16 - Gallery CSS
/*----------------------------------------*/
.gallery-section {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.gallery-wrapper {
  padding: 60px 23% 0;
  margin: 3% 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .gallery-wrapper {
    padding-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .gallery-wrapper {
    padding-top: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .gallery-wrapper {
    padding: 40px 15% 0;
    margin: 3.8% 0;
  }
}

.gallery-item {
  padding-bottom: 60%;
  position: relative;
  overflow: hidden;
}
.gallery-item img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-active .swiper {
  overflow: inherit;
}
.gallery-active .swiper-slide {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.gallery-active .swiper-slide-active {
  transform: scale(1.18);
  z-index: 1;
}
.gallery-active .swiper-button-next, .gallery-active .swiper-button-prev {
  width: 33px;
  height: 33px;
  opacity: 1;
}
.gallery-active .swiper-button-next:hover, .gallery-active .swiper-button-prev:hover {
  background-color: #d68d67;
  color: #ffffff;
}
.gallery-active .swiper-button-next {
  right: 0;
}
.gallery-active .swiper-button-prev {
  left: 0;
}

/*----------------------------------------*/
/*  02.17 - Booking CSS
/*----------------------------------------*/
.booking-section {
  background-color: #ebebe8;
  background-position: center right;
  background-repeat: no-repeat;
  background-size: 58% auto;
  height: 1000px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .booking-section {
    height: 700px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .booking-section {
    height: 600px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .booking-section {
    height: 460px;
  }
}
@media only screen and (max-width: 767px) {
  .booking-section {
    height: auto;
    padding: 60px 0;
  }
}

.booking-content {
  max-width: 600px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .booking-content {
    max-width: 520px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .booking-content {
    max-width: 480px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .booking-content {
    max-width: 340px;
  }
}
@media only screen and (max-width: 767px) {
  .booking-content {
    max-width: 340px;
    text-align: center;
  }
}
.booking-content__box {
  display: flex;
  align-items: flex-end;
  text-align: center;
  flex-direction: row-reverse;
}
@media only screen and (max-width: 767px) {
  .booking-content__box {
    flex-direction: column;
  }
}
.booking-content__image {
  margin-left: -60px;
  width: 80%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .booking-content__image {
    width: 60%;
    margin-left: -70px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .booking-content__image {
    width: 50%;
    margin-left: -50px;
  }
}
@media only screen and (max-width: 767px) {
  .booking-content__image {
    width: 40%;
    margin: 0 auto 30px;
  }
}
.booking-content__image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.booking-content__text {
  padding-bottom: 30px;
  width: 100%;
}
.booking-content__title {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 66px;
  font-weight: 400;
  font-style: italic;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .booking-content__title {
    font-size: 54px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .booking-content__title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .booking-content__title {
    font-size: 50px;
  }
}
.booking-content__description {
  color: #bc1723;
  font-family: "Playfair Display", serif;
  font-size: 36px;
  font-weight: 400;
  font-style: italic;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .booking-content__description {
    font-size: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .booking-content__description {
    font-size: 24px;
  }
}
.booking-content__btn {
  min-width: 280px;
  font-size: 24px;
  font-weight: 400;
  line-height: 28px;
  color: #797979;
  background-color: transparent;
  border: 1px solid #b9b9a9;
  padding: 25px;
  margin-left: 70px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  text-align: center;
  margin-top: 45px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .booking-content__btn {
    min-width: 200px;
    font-size: 20px;
    padding: 15px;
    margin-top: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .booking-content__btn {
    min-width: 180px;
    font-size: 18px;
    padding: 12px;
    margin-top: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .booking-content__btn {
    min-width: 180px;
    font-size: 18px;
    padding: 12px;
    margin: 25px 0 0;
  }
}
.booking-content__btn:hover {
  background-color: #395749;
  border-color: #395749;
  color: #ffffff;
  text-align: center;
}

/*----------------------------------------*/
/*  02.18 - Client CSS
/*----------------------------------------*/
.client-wrapper {
  padding-top: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .client-wrapper {
    padding-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .client-wrapper {
    padding-top: 40px;
  }
}

.client-item {
  padding: 35px;
  border: 1px solid #cccccc;
  margin-top: 26px;
  height: auto;
}
@media only screen and (max-width: 767px) {
  .client-item {
    padding: 35px 25px;
  }
}
.client-item__image {
  position: relative;
  margin-top: -62px;
  z-index: 1;
}
.client-item__image img {
  width: 60px;
  height: auto;
  border-radius: 50%;
}
.client-item__description {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 20px;
  font-style: italic;
  width: 100%;
  margin: 20px 0px 15px;
}
@media only screen and (max-width: 767px) {
  .client-item__description {
    font-size: 16px;
  }
}
.client-item__rating {
  position: relative;
}
.client-item__rating span {
  overflow: hidden;
  position: relative;
  height: 1em;
  line-height: 1;
  font-size: 13px;
  color: #b3bdbc;
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 0.2em;
}
.client-item__rating span::before {
  content: "\ea5e\ea5e\ea5e\ea5e\ea5e";
  display: inline-block;
  font: normal normal normal 1em/1 LaStudioIcons;
  speak: none;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.client-item__rating .star-rating > span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  color: #363636;
}
.client-item__name {
  color: #494949;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
  margin-top: 20px;
}

/*----------------------------------------*/
/*  02.19 - Call To Action CSS
/*----------------------------------------*/
.call-to-action-section {
  background-color: #d5d5bd;
  min-height: 330px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .call-to-action-section {
    min-height: 240px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .call-to-action-section {
    min-height: 230px;
  }
}
@media only screen and (max-width: 767px) {
  .call-to-action-section {
    min-height: auto;
    padding: 60px 0;
  }
}

.call-to-action-wrapper {
  position: relative;
  height: 100%;
}

.call-to-action-image-01 {
  width: 16%;
  z-index: 2;
  position: relative;
}
@media only screen and (max-width: 767px) {
  .call-to-action-image-01 {
    margin: 0 auto;
  }
}
.call-to-action-image-01 img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.call-to-action-image-02 {
  width: 62%;
  top: 3%;
  left: 7%;
  position: absolute;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .call-to-action-image-02 {
    width: 55%;
    top: 10%;
  }
}
@media only screen and (max-width: 767px) {
  .call-to-action-image-02 {
    position: relative;
    top: 0;
    left: 0;
    margin: 0 auto;
  }
}
.call-to-action-image-02 img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.call-to-action-content {
  max-width: 600px;
  z-index: 2;
  position: relative;
}
.call-to-action-content__title {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 66px;
  font-weight: 700;
  line-height: 1;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .call-to-action-content__title {
    font-size: 50px;
  }
}
@media only screen and (max-width: 575px) {
  .call-to-action-content__title {
    font-size: 36px;
  }
}
.call-to-action-content__description {
  color: #363636;
  font-size: 24px;
  margin: 5px 0px 10px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .call-to-action-content__description {
    font-size: 18px;
  }
}
.call-to-action-content__btn {
  padding: 15px 50px;
  font-size: 24px;
  font-weight: 400;
  line-height: 27px;
  background-color: #395749;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .call-to-action-content__btn {
    padding: 12px 40px;
    font-size: 20px;
  }
}
.call-to-action-content__btn:hover {
  background-color: #363636;
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.20 - Testimonial CSS
/*----------------------------------------*/
.testimonial-container {
  max-width: 1280px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .testimonial-container {
    max-width: 80%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial-container {
    max-width: 95%;
  }
}

.testimonial-wrapper {
  margin-top: 60px;
  max-width: 970px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial-wrapper {
    max-width: 700px;
    margin-top: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-wrapper {
    margin-top: 40px;
    padding-bottom: 40px;
  }
}
.testimonial-wrapper .swiper-button-next, .testimonial-wrapper .swiper-button-prev {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 30px;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.testimonial-wrapper .swiper-button-next::after, .testimonial-wrapper .swiper-button-prev::after {
  display: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial-wrapper .swiper-button-next, .testimonial-wrapper .swiper-button-prev {
    display: none;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-wrapper .swiper-button-next, .testimonial-wrapper .swiper-button-prev {
    bottom: auto;
    top: 88%;
    margin-top: 0;
    font-size: 24px;
  }
}
.testimonial-wrapper .swiper-button-next:hover, .testimonial-wrapper .swiper-button-prev:hover {
  color: #d68d67;
}
.testimonial-wrapper .swiper-button-next {
  right: -15%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .testimonial-wrapper .swiper-button-next {
    right: -10%;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-wrapper .swiper-button-next {
    right: 36%;
  }
}
.testimonial-wrapper .swiper-button-prev {
  left: -15%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .testimonial-wrapper .swiper-button-prev {
    left: -10%;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-wrapper .swiper-button-prev {
    left: 36%;
  }
}

.testimonial-item__rating {
  margin-bottom: 10px;
}
.testimonial-item__rating span {
  overflow: hidden;
  position: relative;
  height: 1em;
  line-height: 1;
  font-size: 14px;
  color: #b3bdbc;
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 0.2em;
}
.testimonial-item__rating span::before {
  content: "\ea5e\ea5e\ea5e\ea5e\ea5e";
  display: inline-block;
  font: normal normal normal 1em/1 LaStudioIcons;
  speak: none;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.testimonial-item__rating .star-rating > span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  color: #f5a623;
}
.testimonial-item__title {
  color: #494949;
  font-size: 20px;
  line-height: 1.2;
  text-align: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .testimonial-item__title {
    font-size: 18px;
  }
}
.testimonial-item__description {
  color: #363636;
  font-family: "Playfair Display", serif;
  font-size: 36px;
  font-weight: 500;
  font-style: italic;
  line-height: 1.5em;
  width: 100%;
  margin: 10px 0 30px;
  text-align: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial-item__description {
    font-size: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-item__description {
    font-size: 23px;
  }
}
.testimonial-item__image {
  width: 55px;
  height: 55px;
  display: inline-block;
  vertical-align: middle;
  border-radius: 50%;
  overflow: hidden;
}
.testimonial-item__name {
  color: #494949;
  font-size: 20px;
  line-height: 1;
  font-weight: 400;
  margin-top: 10px;
  text-align: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .testimonial-item__name {
    font-size: 18px;
  }
}

/*----------------------------------------*/
/*  02.21 - Contact CSS
/*----------------------------------------*/
.contact-wrapper__title {
  color: #363636;
  font-size: 44px;
  font-weight: 400;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-wrapper__title {
    font-size: 26px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-wrapper__title {
    font-size: 24px;
  }
}
.contact-wrapper > * {
  margin-top: 20px;
}
.contact-wrapper > *:first-child {
  margin-top: 0;
}

.contact-form-style-1 {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
  padding: 40px 0 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-form-style-1 {
    padding: 0 0 30px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-form-style-1 {
    padding: 0 0 20px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-form-style-1 > * {
    margin-top: 20px;
  }
  .contact-form-style-1 > *:first-child {
    margin-top: 0;
  }
}
.contact-form-style-1__input {
  width: 40%;
  padding: 0 15px;
}
@media only screen and (max-width: 767px) {
  .contact-form-style-1__input {
    width: 100%;
  }
}
.contact-form-style-1__input input {
  width: 100%;
  color: #363636;
  font-size: 20px;
  line-height: 20px;
  padding: 10px 0 10px 0;
  border: 0;
  border-bottom: 2px solid #494949;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-form-style-1__input input {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-form-style-1__input input {
    border-bottom: 1px solid #494949;
    font-size: 16px;
  }
}
.contact-form-style-1__input input::-webkit-input-placeholder {
  color: #363636;
}
.contact-form-style-1__input input:-moz-placeholder {
  color: #363636;
}
.contact-form-style-1__input input::-moz-placeholder {
  color: #363636;
}
.contact-form-style-1__input input:-ms-input-placeholder {
  color: #363636;
}
.contact-form-style-1__btn {
  width: 20%;
  padding: 0 15px;
}
@media only screen and (max-width: 767px) {
  .contact-form-style-1__btn {
    width: 100%;
  }
}
.contact-form-style-1__btn .btn {
  padding: 25px 49px;
  margin-left: auto;
  display: block;
}
.contact-form-style-1__btn .btn:hover {
  border-color: #d68d67;
  background-color: #d68d67;
  color: #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .contact-form-style-1__btn .btn {
    padding: 20px 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-form-style-1__btn .btn {
    padding: 15px 10px;
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-form-style-1__btn .btn {
    padding: 13px 20px;
    width: 100%;
    border-width: 1px;
  }
}

.contact-social {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}
@media only screen and (max-width: 575px) {
  .contact-social {
    gap: 20px;
  }
}
.contact-social__social {
  display: flex;
  margin-left: -15px;
  margin-right: -15px;
}
@media only screen and (max-width: 575px) {
  .contact-social__social {
    margin-left: -10px;
    margin-right: -10px;
  }
}
.contact-social__social li {
  padding: 0 15px;
}
@media only screen and (max-width: 575px) {
  .contact-social__social li {
    padding: 0 10px;
  }
}
.contact-social__social li a {
  font-size: 40px;
  color: #494949;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-social__social li a {
    font-size: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-social__social li a {
    font-size: 24px;
  }
}
.contact-social__social li a:hover {
  color: #363636;
}

.contact-us__title {
  color: #363636;
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-us__title {
    font-size: 28px;
    margin-bottom: 8px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-us__title {
    font-size: 24px;
    margin-bottom: 8px;
  }
}

.contact-us-form {
  padding-right: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .contact-us-form {
    padding-right: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-us-form {
    padding-right: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-us-form {
    padding-right: 0;
  }
}

.contact-us-info > * {
  border-top: 1px solid #e5e5e5;
  padding-top: 40px;
  padding-bottom: 35px;
}
@media only screen and (max-width: 767px) {
  .contact-us-info > * {
    padding-top: 25px;
    padding-bottom: 20px;
  }
}
.contact-us-info > *:first-child {
  border-top: 0;
}
.contact-us-info > *:last-child {
  padding-bottom: 0;
}

.contact-info-item__title {
  color: #363636;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-info-item__title {
    font-size: 20px;
    margin-bottom: 10px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-info-item__title {
    font-size: 18px;
    margin-bottom: 10px;
  }
}
.contact-info-item__service {
  margin-top: 20px;
}
.contact-info-item__service--title {
  color: #363636;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .contact-info-item__service--title {
    font-size: 16px;
  }
}
.contact-info-item__service p a {
  margin-right: 30px;
}
.contact-info-item__service p a:last-child {
  margin-right: 0;
}
.contact-info-item p {
  color: #494949;
  font-size: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .contact-info-item p {
    font-size: 16px;
  }
}
.contact-info-item p a {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.contact-info-item p a:hover {
  color: #d68d67;
}
.contact-info-item__direction {
  color: #363636;
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin-top: 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .contact-info-item__direction {
    font-size: 16px;
  }
}
.contact-info-item__direction:hover {
  color: #d68d67;
}

/*----------------------------------------*/
/*  02.22 - Coming Soon CSS
/*----------------------------------------*/
.coming-soon-container {
  max-width: 1250px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .coming-soon-container {
    padding: 0 80px;
  }
}

.coming-soon-image {
  width: 93%;
}
@media only screen and (max-width: 767px) {
  .coming-soon-image {
    width: 250px;
    margin: 0 auto;
  }
}
.coming-soon-image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.coming-soon-content > * {
  margin-top: 35px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .coming-soon-content > * {
    margin-top: 25px;
  }
}
.coming-soon-content > *:first-child {
  margin-top: 0;
}
.coming-soon-content__title {
  color: #363636;
  font-size: 20px;
  font-weight: 400;
}
.coming-soon-content__btn {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
  padding: 14px 45px 13px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .coming-soon-content__btn {
    padding: 13px 30px 12px;
  }
}
.coming-soon-content__btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.23 - Newsletter CSS
/*----------------------------------------*/
.newsletter-section {
  display: flex;
  flex-wrap: wrap;
}

.newsletter-left,
.newsletter-right {
  width: 50%;
  height: 420px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-left,
.newsletter-right {
    height: 320px;
    padding: 0 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-left,
.newsletter-right {
    height: 320px;
    padding: 0 20px;
    width: 100%;
  }
}
@media only screen and (max-width: 767px) {
  .newsletter-left,
.newsletter-right {
    width: 100%;
    height: auto;
    padding: 80px 20px;
  }
}

.newsletter-wrapper {
  max-width: 550px;
  margin: 0 auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .newsletter-wrapper {
    max-width: 440px;
  }
}
.newsletter-wrapper__title {
  font-size: 34px;
  font-weight: 700;
  text-transform: uppercase;
  color: #363636;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-wrapper__title {
    font-size: 26px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-wrapper__title {
    font-size: 22px;
  }
}
@media only screen and (max-width: 767px) {
  .newsletter-wrapper__title {
    font-size: 24px;
  }
}
.newsletter-wrapper__title-2 {
  font-size: 34px;
  font-weight: 400;
  color: #363636;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-wrapper__title-2 {
    font-size: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .newsletter-wrapper__title-2 {
    font-size: 24px;
    text-transform: uppercase;
    font-weight: 700;
  }
}
.newsletter-wrapper p {
  font-size: 20px;
  font-weight: 400;
  font-family: "Playfair Display", serif;
  line-height: 1.5;
  margin: 10px 0 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .newsletter-wrapper p {
    font-size: 16px;
  }
}

.newsletter-social {
  text-align: center;
}
.newsletter-social__label {
  font-size: 34px;
  font-weight: 400;
  color: #363636;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-social__label {
    font-size: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .newsletter-social__label {
    font-size: 24px;
    text-transform: uppercase;
    font-weight: 700;
  }
}
.newsletter-social__list {
  display: flex;
  justify-content: center;
  margin-left: calc(-25px / 2);
  margin-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .newsletter-social__list {
    margin-left: calc(-10px / 2);
  }
}
.newsletter-social__list li {
  padding: 0 calc(25px / 2);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .newsletter-social__list li {
    padding: 0 calc(10px / 2);
  }
}
.newsletter-social__list li a {
  font-size: 24px;
  height: 48px;
  width: 48px;
  color: #363636;
  background-color: #ffffff;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .newsletter-social__list li a {
    font-size: 20px;
    width: 40px;
    height: 40px;
  }
}
.newsletter-social__list li a:hover {
  background-color: #d68d67;
  color: #ffffff;
}

.newsletter-form-style-1 {
  position: relative;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 10px;
}
.newsletter-form-style-1 input {
  padding: 0;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.5;
  border: 0;
  border-bottom: 2px solid #363636;
  flex: 1 1 auto;
  width: 100px;
  height: 50px;
  padding-right: 110px;
  background: none;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .newsletter-form-style-1 input {
    font-size: 16px;
  }
}
.newsletter-form-style-1 button {
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 20px;
  padding: 0;
  border: 0;
  background: none;
  color: #494949;
  white-space: nowrap;
  height: 50px;
  position: absolute;
  bottom: 0;
  right: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-form-style-1 button {
    font-size: 14px;
  }
}
@media only screen and (max-width: 575px) {
  .newsletter-form-style-1 button {
    position: relative;
    width: 100%;
    background-color: #363636;
    color: #ffffff;
    padding: 12px 10px 9px;
    font-size: 16px;
    font-weight: 700;
    margin-top: 10px;
    height: auto;
    line-height: 1.2;
  }
}

/*----------------------------------------*/
/*  02.24 - Breadcrumb CSS
/*----------------------------------------*/
.breadcrumb-section {
  padding: 90px 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-section {
    padding-top: 70px;
    padding-bottom: 70px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .breadcrumb-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.breadcrumb-wrapper__title {
  font-size: 54px;
  font-family: "Josefin Sans", sans-serif;
  font-weight: 600;
  line-height: 1;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-wrapper__title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .breadcrumb-wrapper__title {
    font-size: 34px;
  }
}
@media only screen and (max-width: 767px) {
  .breadcrumb-wrapper__title {
    font-size: 28px;
  }
}
.breadcrumb-wrapper__items {
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;
}
.breadcrumb-wrapper__items li {
  position: relative;
}
.breadcrumb-wrapper__items li:not(:last-child)::after {
  content: ">";
  font-size: 20px;
  line-height: 1;
  color: #886e6e;
  margin: 0 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .breadcrumb-wrapper__items li:not(:last-child)::after {
    font-size: 16px;
  }
}
.breadcrumb-wrapper__items li span, .breadcrumb-wrapper__items li a {
  font-size: 20px;
  line-height: 1;
  color: #886e6e;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .breadcrumb-wrapper__items li span, .breadcrumb-wrapper__items li a {
    font-size: 16px;
  }
}
.breadcrumb-wrapper__items li a:hover {
  color: #d68d67;
}
.breadcrumb-wrapper.breadcrumb-white .breadcrumb-wrapper__title {
  color: #ffffff;
}
.breadcrumb-wrapper.breadcrumb-white .breadcrumb-wrapper__items li:not(:last-child)::after {
  color: #ffffff;
}
.breadcrumb-wrapper.breadcrumb-white .breadcrumb-wrapper__items li span, .breadcrumb-wrapper.breadcrumb-white .breadcrumb-wrapper__items li a {
  color: #ffffff;
}
.breadcrumb-wrapper.breadcrumb-white .breadcrumb-wrapper__items li a:hover {
  color: #d68d67;
}

.single-breadcrumbs {
  padding: 30px 0;
}

.single-breadcrumbs-list {
  display: flex;
  flex-wrap: wrap;
}
.single-breadcrumbs-list li {
  position: relative;
}
.single-breadcrumbs-list li:not(:last-child)::after {
  content: "/";
  font-size: 14px;
  line-height: 1;
  color: #494949;
  margin: 0 5px;
}
.single-breadcrumbs-list li span, .single-breadcrumbs-list li a {
  font-size: 14px;
  line-height: 1;
  color: #494949;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.single-breadcrumbs-list li span:hover, .single-breadcrumbs-list li a:hover {
  color: #d68d67;
}
.single-breadcrumbs-list li span {
  color: #363636;
}

/*----------------------------------------*/
/*  02.25 - Our Team CSS
/*----------------------------------------*/
.our-team-wrapper {
  margin-top: -60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .our-team-wrapper {
    margin-top: -50px;
  }
}
@media only screen and (max-width: 767px) {
  .our-team-wrapper {
    margin-top: -30px;
  }
}

.our-team-item {
  margin-top: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .our-team-item {
    margin-top: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .our-team-item {
    margin-top: 30px;
  }
}
.our-team-item__image {
  padding-bottom: 94%;
  position: relative;
  z-index: 1;
}
.our-team-item__image::before {
  position: absolute;
  content: "";
  opacity: 0;
  background-color: #d68d67;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  z-index: 1;
}
.our-team-item__image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.our-team-item__content {
  margin-top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .our-team-item__content {
    margin-top: 25px;
  }
}
.our-team-item__name {
  color: #363636;
  font-size: 28px;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .our-team-item__name {
    font-size: 22px;
  }
}
.our-team-item__position {
  color: #494949;
  font-size: 18px;
  font-weight: 600;
  margin-top: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .our-team-item__position {
    font-size: 16px;
  }
}
.our-team-item__social {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
}
.our-team-item__social li {
  margin-right: 20px;
  line-height: 0.8;
}
.our-team-item__social li:last-child {
  margin-right: 0;
}
.our-team-item__social li a {
  color: #d1d1d1;
  font-size: 22px;
  line-height: 0.8;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .our-team-item__social li a {
    font-size: 18px;
  }
}
.our-team-item__social li a:hover {
  color: #363636;
}
.our-team-item:hover .our-team-item__image::before {
  opacity: 0.2;
}

/*----------------------------------------*/
/*  02.26 - FAQ’s CSS
/*----------------------------------------*/
.faq-container {
  max-width: 1220px;
}

.faq-wrapper > * {
  margin-top: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .faq-wrapper > * {
    margin-top: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .faq-wrapper > * {
    margin-top: 60px;
  }
}
.faq-wrapper > *:first-child {
  margin-top: 0;
}

.faq-accordion__title {
  font-size: 42px;
  font-weight: 600;
  line-height: 1em;
  padding-bottom: 20px;
  color: #363636;
  border-bottom: 2px solid #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .faq-accordion__title {
    font-size: 32px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-accordion__title {
    font-size: 28px;
    padding-bottom: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-accordion__title {
    font-size: 24px;
    padding-bottom: 10px;
  }
}
.faq-accordion .accordion {
  margin-top: 20px;
  border-radius: 0;
}
.faq-accordion .accordion-item {
  color: #363636;
  background-color: transparent;
  border: 0;
  border-bottom: 1px solid #d4d4d4;
  border-radius: 0;
}
.faq-accordion .accordion-item:last-child {
  padding-bottom: 0;
}
.faq-accordion .accordion-item button {
  display: block;
  width: 100%;
  text-align: left;
  border: 0;
  font-size: 22px;
  font-weight: 600;
  line-height: 1;
  padding: 20px 0;
  background: none;
  padding-right: 10px;
  display: flex;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-accordion .accordion-item button {
    font-size: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-accordion .accordion-item button {
    font-size: 16px;
    padding: 12px 0;
  }
}
.faq-accordion .accordion-item button .text {
  flex: 1;
}
.faq-accordion .accordion-item button .icon {
  flex-shrink: 0;
  font-size: 18px;
  line-height: 0.8;
  transform: rotate(180deg);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.faq-accordion .accordion-item button.collapsed .icon {
  transform: rotate(0deg);
}
.faq-accordion .accordion-collapse {
  padding-bottom: 50px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (max-width: 767px) {
  .faq-accordion .accordion-collapse {
    padding-bottom: 40px;
  }
}
.faq-accordion .accordion-collapse p {
  font-size: 22px;
  line-height: 1.6;
  color: #494949;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-accordion .accordion-collapse p {
    font-size: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-accordion .accordion-collapse p {
    font-size: 16px;
  }
}

.faq-button__btn {
  font-size: 22px;
  line-height: 40px;
  color: #363636;
  background-color: #ffffff;
  border: 1px solid #bababa;
  padding: 30px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  width: 100%;
  display: block;
  text-align: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-button__btn {
    font-size: 20px;
    padding: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-button__btn {
    font-size: 16px;
    padding: 12px;
  }
}
.faq-button__btn:hover {
  color: #ffffff;
  background-color: #363636;
  border-color: #363636;
}

/*----------------------------------------*/
/*   02.27 - Term Of Use CSS
/*----------------------------------------*/
.term-use-wrapper > * {
  margin-top: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .term-use-wrapper > * {
    margin-top: 70px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .term-use-wrapper > * {
    margin-top: 60px;
  }
}
.term-use-wrapper > *:first-child {
  margin-top: 0;
}

.term-use-item__title {
  font-size: 42px;
  font-weight: 600;
  line-height: 1;
  padding-bottom: 20px;
  border-bottom: 1px solid #eaeaea;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .term-use-item__title {
    font-size: 32px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .term-use-item__title {
    font-size: 28px;
    padding-bottom: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .term-use-item__title {
    font-size: 28px;
    padding-bottom: 15px;
  }
}
.term-use-item__list {
  margin-top: 35px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .term-use-item__list {
    margin-top: 25px;
  }
}
.term-use-item__list li {
  font-size: 22px;
  line-height: 1.5;
  margin-top: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .term-use-item__list li {
    font-size: 18px;
  }
}
.term-use-item__list li:first-child {
  margin-top: 0;
}

/*----------------------------------------*/
/*  02.28 - 404 CSS
/*----------------------------------------*/
.error-section {
  padding: 200px 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .error-section {
    padding: 120px 0;
  }
}
@media only screen and (max-width: 767px) {
  .error-section {
    padding: 100px 0;
  }
}

.error-content {
  max-width: 750px;
  margin: 0 auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .error-content {
    max-width: 600px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .error-content {
    max-width: 500px;
  }
}
.error-content__icon {
  width: 63px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .error-content__icon {
    width: 50px;
  }
}
.error-content__title {
  color: #363636;
  font-size: 62px;
  font-weight: 600;
  line-height: 1;
  margin: 40px 0px 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .error-content__title {
    font-size: 46px;
    margin: 20px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .error-content__title {
    font-size: 36px;
    margin: 20px 0;
  }
}
@media only screen and (max-width: 767px) {
  .error-content__title {
    font-size: 28px;
    margin: 20px 0;
  }
}
.error-content p {
  color: #494949;
  font-size: 24px;
  line-height: 1.7;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .error-content p {
    font-size: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .error-content p {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .error-content p {
    font-size: 16px;
  }
}
.error-content__btn {
  margin-top: 60px;
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .error-content__btn {
    margin-top: 50px;
  }
}
.error-content__btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.29 Comment CSS
/*----------------------------------------*/
.comment-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
  color: #363636;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .comment-title {
    font-size: 22px;
  }
}

.comment-items {
  padding-top: 10px;
}
.comment-items > * {
  margin-top: 40px;
}

.comment-item {
  display: flex;
}
.comment-item__author {
  width: 90px;
  height: 90px;
  min-width: 90px;
  border-radius: 50%;
  margin-right: 40px;
  overflow: hidden;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .comment-item__author {
    width: 70px;
    height: 70px;
    min-width: 70px;
    margin-right: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .comment-item__author {
    width: 50px;
    height: 50px;
    min-width: 50px;
    margin-right: 15px;
  }
}
.comment-item__author img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.comment-item__content {
  position: relative;
  border: 1px solid #ccc;
  padding: 30px;
  flex-grow: 1;
}
@media only screen and (max-width: 767px) {
  .comment-item__content {
    padding: 20px;
  }
}
.comment-item__content::before {
  content: "“";
  position: absolute;
  top: -0.325em;
  left: 30px;
  font-size: 45px;
  font-family: serif;
  font-weight: 700;
  line-height: 1;
  background-color: #ffffff;
  padding-left: 0.2em;
  padding-right: 0.2em;
}
.comment-item__rating {
  margin-bottom: 10px;
}
.comment-item__rating span {
  overflow: hidden;
  position: relative;
  height: 1em;
  line-height: 1;
  font-size: 14px;
  color: #b3bdbc;
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 0.2em;
}
.comment-item__rating span::before {
  content: "\ea5e\ea5e\ea5e\ea5e\ea5e";
  display: inline-block;
  font: normal normal normal 1em/1 LaStudioIcons;
  font-size: 14px;
  speak: none;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.comment-item__rating .star-rating > span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  color: #f5a623;
}
.comment-item__description {
  margin-bottom: 0.8em;
}
.comment-item__meta {
  font-size: 14px;
  color: #494949;
  line-height: 1.6;
}
.comment-item__meta strong {
  font-weight: 600;
  color: #363636;
}

.comment-reply {
  padding-left: 130px;
  padding-top: 10px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .comment-reply {
    padding-left: 25px;
  }
}
.comment-reply > * {
  margin-top: 40px;
}
.comment-reply .comment-item__author {
  width: 50px;
  height: 50px;
  min-width: 50px;
  margin-right: 25px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .comment-reply .comment-item__author {
    width: 45px;
    height: 45px;
    min-width: 45px;
    margin-right: 15px;
  }
}
.comment-reply .comment-reply {
  padding-left: 75px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .comment-reply .comment-reply {
    padding-left: 25px;
  }
}

.comment-form {
  margin-top: 30px;
}
.comment-form__notes {
  font-size: 14px;
  font-weight: 400;
  font-style: italic;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 10px;
  margin-bottom: 20px;
}
.comment-form__rating {
  margin-bottom: 10px;
}
.comment-form .single-form__btn {
  min-width: 180px;
  font-size: 16px;
}

/*----------------------------------------*/
/*  02.30 - Cart Sidebar CSS
/*----------------------------------------*/
.cart-offcanvas.offcanvas-end {
  width: 450px;
}
@media only screen and (max-width: 575px) {
  .cart-offcanvas.offcanvas-end {
    width: 320px;
  }
}
.cart-offcanvas .offcanvas-header {
  padding: 40px 40px 15px;
  position: relative;
}
@media only screen and (max-width: 575px) {
  .cart-offcanvas .offcanvas-header {
    padding: 20px 20px 10px;
  }
}
.cart-offcanvas .offcanvas-header .offcanvas-title {
  font-size: 24px;
  font-weight: 600;
  color: #363636;
  line-height: 1.2;
}
@media only screen and (max-width: 767px) {
  .cart-offcanvas .offcanvas-header .offcanvas-title {
    font-size: 20px;
  }
}
.cart-offcanvas .offcanvas-header .btn-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #363636;
  border-radius: 50%;
  font-size: 18px;
  color: #363636;
  padding: 0;
  margin: 0;
  background: none;
  box-shadow: none;
  position: absolute;
  top: 20px;
  right: 20px;
}
@media only screen and (max-width: 767px) {
  .cart-offcanvas .offcanvas-header .btn-close {
    width: 28px;
    height: 28px;
  }
}
.cart-offcanvas .offcanvas-body {
  padding: 15px 40px;
}
@media only screen and (max-width: 575px) {
  .cart-offcanvas .offcanvas-body {
    padding: 10px 20px;
  }
}
.cart-offcanvas .offcanvas-footer {
  padding: 40px 40px 15px;
}
@media only screen and (max-width: 575px) {
  .cart-offcanvas .offcanvas-footer {
    padding: 20px 20px 10px;
  }
}

.offcanvas-cart-list li + li {
  margin-top: 30px;
}

.offcanvas-cart-item {
  display: flex;
  position: relative;
  padding-right: 20px;
}
.offcanvas-cart-item__thumbnail {
  margin-right: 20px;
  border: 1px solid #dedede;
  flex-shrink: 0;
}
.offcanvas-cart-item__thumbnail a {
  display: block;
}
.offcanvas-cart-item__thumbnail img {
  aspect-ratio: 1/1;
  width: 70px;
  height: 84px;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.offcanvas-cart-item__content {
  flex-grow: 1;
}
.offcanvas-cart-item__title {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.3;
  margin-bottom: 5px;
  color: #363636;
}
.offcanvas-cart-item__title a {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.offcanvas-cart-item__title a:hover {
  color: #d68d67;
}
.offcanvas-cart-item__quantity {
  font-size: 15px;
  color: #494949;
  display: block;
}
.offcanvas-cart-item__remove {
  width: 20px;
  height: 20px;
  line-height: 17px;
  font-size: 18px;
  font-weight: 400;
  display: block;
  overflow: hidden;
  position: absolute;
  right: 0;
  text-align: right;
  color: #363636;
}

.cart-totals-table .table {
  margin-bottom: 0;
}
.cart-totals-table .table tbody tr {
  display: flex;
  justify-content: space-between;
}
.cart-totals-table .table tbody tr th, .cart-totals-table .table tbody tr td {
  font-size: 18px;
  font-weight: 600;
  color: #494949;
  vertical-align: top;
  padding: 5px 0;
  border: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-totals-table .table tbody tr th, .cart-totals-table .table tbody tr td {
    font-size: 16px;
  }
}
.cart-totals-table .table tbody tr th strong, .cart-totals-table .table tbody tr td strong {
  font-weight: 400;
}
.cart-totals-table .table tbody tr td {
  color: #363636;
  font-size: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-totals-table .table tbody tr td {
    font-size: 18px;
  }
}
.cart-totals-table .shipping-methods li {
  margin-top: 0px;
}
.cart-totals-table .shipping-methods li:first-child {
  margin-top: 0;
}
.cart-totals-table .shipping-methods li label {
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-totals-table .shipping-methods li label {
    font-size: 16px;
  }
}
.cart-totals-table .shipping-methods li label .price {
  color: #494949;
}

.cart-buttons > * {
  margin-top: 10px;
}
.cart-buttons .btn {
  padding: 13px 20px;
  font-size: 18px;
  font-weight: 600;
  display: block;
  width: 100%;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-buttons .btn {
    font-size: 16px;
  }
}
.cart-buttons__btn-1 {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
.cart-buttons__btn-1:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}
.cart-buttons__btn-2 {
  background-color: #ffffff;
  border-color: #363636;
  color: #363636;
}
.cart-buttons__btn-2:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

/*----------------------------------------*/
/*  02.31 - Quick View Modal CSS
/*----------------------------------------*/
.quickview-modal.fade {
  display: block !important;
  opacity: 0;
  visibility: hidden;
}
.quickview-modal.fade.show {
  display: block !important;
  opacity: 1;
  visibility: visible;
}
.quickview-modal .modal-dialog {
  max-width: 1500px;
  max-height: 80vh;
  position: relative;
}
.quickview-modal .modal-content {
  color: #363636;
  border: 0;
  border-radius: 0;
  outline: 0;
  max-height: calc(80vh - 60px);
  max-width: calc(100vw - 60px);
}
.quickview-modal .modal-body {
  overflow: auto;
  padding: 0;
}
.quickview-modal .btn-close {
  position: absolute;
  top: -25px;
  right: -25px;
  width: 50px;
  height: 50px;
  font-size: 18px;
  cursor: pointer;
  text-align: center;
  color: #ffffff;
  border: 0;
  padding: 0;
  background: #363636;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  z-index: 9;
  box-shadow: none;
  opacity: 1;
}
.quickview-modal .btn-close i {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 1;
}
.quickview-modal .btn-close:hover {
  background: #d68d67;
  color: #ffffff;
}
.quickview-modal .btn-close:hover i {
  transform: rotate(90deg);
}

.quick-view-product-slide .swiper-button-next {
  right: 0;
}
.quick-view-product-slide .swiper-button-prev {
  left: 0;
}

.quick-view-product-content {
  padding: 5%;
}
.quick-view-product-content .product-single-content__title {
  font-size: 30px;
  margin-bottom: 20px;
}
.quick-view-product-content .product-single-content__price-stock {
  margin-bottom: 15px;
}
.quick-view-product-content .product-single-content__price {
  font-size: 26px;
}
.quick-view-product-content .product-single-content__price del {
  font-size: 22px;
}
.quick-view-product-content .product-single-content__short-description {
  margin-bottom: 30px;
}
.quick-view-product-content .product-single-content__countdown {
  margin-bottom: 20px;
}
.quick-view-product-content .product-single-content__countdown--title {
  font-size: 16px;
}
.quick-view-product-content .product-single-content__variable {
  margin-bottom: 15px;
}
.quick-view-product-content .product-single-content__add-to-cart-wrapper {
  margin-bottom: 40px;
}
.quick-view-product-content .product-single-content__quantity-add-to-cart {
  margin-bottom: 10px;
}
.quick-view-product-content .product-single-content__quantity input {
  height: 56px;
}
.quick-view-product-content .product-single-content__add-to-cart {
  font-size: 14px;
  padding: 17px 40px;
}
.quick-view-product-content .product-single-content__meta {
  margin-bottom: 30px;
}
.quick-view-product-content .product-single-content__meta--item {
  padding: 0;
}
.quick-view-product-content .product-single-content__meta--item .label {
  font-size: 16px;
}
.quick-view-product-content .product-single-content__meta--item .content {
  font-size: 16px;
}

/*----------------------------------------*/
/*  02.32 - Popup Modal CSS
/*----------------------------------------*/
.popup-modal {
  position: fixed;
  height: 100%;
  width: 100%;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: all;
  z-index: 999;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: all;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.popup-modal.open {
  opacity: 1;
  visibility: visible;
}
.popup-modal.open .popup-modal-content {
  -webkit-animation: ModalFadeInUp 0.5s ease-in-out both;
          animation: ModalFadeInUp 0.5s ease-in-out both;
}
.popup-modal.close {
  transition-delay: 0.5s;
}
.popup-modal.close .popup-modal-content {
  -webkit-animation: ModalFadeInDown 0.5s ease-in-out both;
          animation: ModalFadeInDown 0.5s ease-in-out both;
}

.popup-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(23, 23, 23, 0.73);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.popup-modal-overlay.open {
  opacity: 1;
  visibility: visible;
}
.popup-modal-overlay.close {
  transition-delay: 0.5s;
}

@-webkit-keyframes ModalFadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 50px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes ModalFadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 50px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes ModalFadeInDown {
  0% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  100% {
    opacity: 0;
    transform: translate3d(0, -50px, 0);
  }
}
@keyframes ModalFadeInDown {
  0% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  100% {
    opacity: 0;
    transform: translate3d(0, -50px, 0);
  }
}
.popup-modal-content {
  background-color: #ffffff;
  width: 1170px;
  height: auto;
  position: relative;
  z-index: 99;
  pointer-events: all;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .popup-modal-content {
    width: 800px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-modal-content {
    width: 700px;
  }
}
@media only screen and (max-width: 767px) {
  .popup-modal-content {
    width: 320px;
  }
}

.popup-modal-row {
  display: flex;
  flex-wrap: wrap;
}

.popup-modal-col-1 {
  width: 60%;
  flex: 0 0 auto;
}
@media only screen and (max-width: 767px) {
  .popup-modal-col-1 {
    width: 100%;
  }
}

.popup-modal-col-2 {
  width: 40%;
  flex: 0 0 auto;
}
@media only screen and (max-width: 767px) {
  .popup-modal-col-2 {
    width: 100%;
  }
}

.popup-discount-content {
  padding: 70px 70px 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .popup-discount-content {
    padding: 50px 50px 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content {
    padding: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .popup-discount-content {
    padding: 20px;
  }
}
.popup-discount-content__title {
  color: #363636;
  font-size: 46px;
  font-weight: 600;
  line-height: 1;
  margin-top: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content__title {
    font-size: 32px;
    margin-top: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .popup-discount-content__title {
    font-size: 26px;
    margin-top: 0;
  }
}
.popup-discount-content__sub-title {
  font-size: 22px;
  color: #494949;
  line-height: 1;
  margin-top: 5px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .popup-discount-content__sub-title {
    font-size: 16px;
  }
}
.popup-discount-content__form {
  margin: 50px 0 20px;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content__form {
    margin: 30px 0 20px;
  }
}
@media only screen and (max-width: 767px) {
  .popup-discount-content__form {
    margin: 20px 0;
  }
}
.popup-discount-content__form input {
  padding: 0 110px 0 20px;
  color: #494949;
  font-size: 18px;
  height: 58px;
  width: 100%;
  border: 1px solid #cbcbcb;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .popup-discount-content__form input {
    height: 50px;
    font-size: 16px;
  }
}
.popup-discount-content__form button {
  position: absolute;
  top: 0;
  right: 0;
  height: 58px;
  font-size: 18px;
  font-weight: 600;
  background: none;
  border: 0;
  color: #363636;
  padding: 0 20px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .popup-discount-content__form button {
    height: 50px;
    font-size: 16px;
  }
}
.popup-discount-content__form button:hover {
  color: #d68d67;
}
.popup-discount-content__text {
  color: #b4b4b4;
  font-size: 16px;
  line-height: 1.4;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .popup-discount-content__text {
    font-size: 13px;
  }
}
.popup-discount-content__wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-top: 70px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .popup-discount-content__wrapper {
    margin-top: 30px;
  }
}
.popup-discount-content__dont-show a {
  font-size: 18px;
  color: #494949;
  line-height: 1.2;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content__dont-show a {
    font-size: 14px;
  }
}
@media only screen and (max-width: 767px) {
  .popup-discount-content__dont-show a {
    font-size: 15px;
  }
}
.popup-discount-content__dont-show a:hover {
  color: #363636;
}
.popup-discount-content__social {
  display: flex;
  margin-left: -8px;
  margin-right: -8px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content__social {
    margin-left: -5px;
    margin-right: -5px;
  }
}
.popup-discount-content__social li {
  padding: 0 8px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content__social li {
    padding: 0 5px;
  }
}
.popup-discount-content__social li a {
  font-size: 15px;
  width: 39px;
  height: 39px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  color: #363636;
  border-radius: 50%;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .popup-discount-content__social li a {
    width: 32px;
    height: 32px;
  }
}
@media only screen and (max-width: 767px) {
  .popup-discount-content__social li a {
    width: 34px;
    height: 34px;
  }
}
.popup-discount-content__social li a:hover {
  background-color: #363636;
  color: #ffffff;
}

.popup-discount-background {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}
@media only screen and (max-width: 767px) {
  .popup-discount-background {
    height: 220px;
    background-position: top center;
  }
}

.popup-close {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 9;
}
.popup-close__btn {
  font-size: 24px;
  padding: 0;
  border: 0;
  background: none;
  color: #ffffff;
}
.popup-close__btn svg {
  fill: currentColor;
  height: 1em;
  width: 1em;
}

/*===== 03 - Pages =====*/
/*----------------------------------------*/
/*  03.01 - Shop Page CSS
/*----------------------------------------*/
.shop-filter {
  display: flex;
  padding-bottom: 20px;
  position: relative;
}

.shop-filter-default {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  margin-left: -10px;
  margin-right: -10px;
  margin-top: -10px;
  margin-bottom: -10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .shop-filter-default {
    margin-left: -5px;
    margin-right: -5px;
    margin-top: -5px;
    margin-bottom: -5px;
  }
}
.shop-filter-default > * {
  padding: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .shop-filter-default > * {
    padding: 5px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-filter-default > * {
    width: 50%;
  }
}
@media only screen and (max-width: 575px) {
  .shop-filter-default > * {
    width: 100%;
  }
}

.shop-filter-count {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.8;
  color: #363636;
  white-space: nowrap;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-filter-count {
    font-size: 16px;
  }
}

.shop-filter-sort-by {
  position: relative;
}
.shop-filter-sort-by__label {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.8;
  color: #363636;
  cursor: pointer;
  white-space: nowrap;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-filter-sort-by__label {
    font-size: 16px;
  }
}
.shop-filter-sort-by__label i {
  font-size: 12px;
  margin-left: 10px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.shop-filter-sort-by__dropdown {
  right: 0;
  position: absolute;
  background-color: #ffffff;
  z-index: 3;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border: 1px solid #dedede;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}
.shop-filter-sort-by__dropdown li {
  display: block;
  border-bottom: 1px solid #dedede;
}
.shop-filter-sort-by__dropdown li a {
  padding: 7px 15px;
  white-space: nowrap;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.867;
  max-width: 100%;
  display: flex;
  align-items: center;
}
.shop-filter-sort-by__dropdown li a i {
  margin-left: 10px;
}
.shop-filter-sort-by__dropdown li:hover a, .shop-filter-sort-by__dropdown li.active a {
  background-color: #f1f0f0;
}
.shop-filter-sort-by:hover .shop-filter-sort-by__label i {
  transform: rotate(180deg);
}
.shop-filter-sort-by:hover .shop-filter-sort-by__dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.shop-filter-button {
  flex-shrink: 0;
  padding-left: 20px;
}
.shop-filter-button__btn {
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  color: #363636;
  padding: 4px 12px 2px;
  border: 1px solid #d9d9d9;
  background-color: transparent;
}
.shop-filter-button__btn i {
  margin-left: 3px;
}
.shop-filter-button__btn:focus {
  color: #363636;
}

.shop-filter-widget {
  position: absolute;
  right: 0;
  top: 100%;
  width: 100%;
  z-index: 4;
  padding: 40px;
  background-color: #ffffff;
  box-shadow: 0 0 32px 0 rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-filter-widget {
    position: fixed;
    left: 0;
    right: auto;
    width: 320px;
    height: 100%;
    top: 0;
    z-index: 111;
    overflow-y: auto;
    overflow-x: hidden;
    opacity: 1;
    visibility: visible;
    transform: translateX(-100%);
  }
}
.shop-filter-widget.open {
  opacity: 1;
  visibility: visible;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-filter-widget.open {
    transform: translateX(0);
  }
}

.filter-widget-row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -13px;
  margin-right: -13px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .filter-widget-row {
    margin: 0;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .filter-widget-row > * {
    margin-top: 30px;
  }
  .filter-widget-row > *:first-child {
    margin-top: 0;
  }
}

.filter-widget-col {
  width: 20%;
  padding: 0 13px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .filter-widget-col {
    width: 100%;
    padding: 0;
  }
}

.widget-item__title {
  font-size: 20px;
  font-weight: 500;
  color: #363636;
  margin-bottom: 20px;
}
.widget-item__filter {
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 225px;
  scrollbar-width: auto;
  scrollbar-color: #363636 #dedede;
}
.widget-item__filter::-webkit-scrollbar {
  width: 2px;
}
.widget-item__filter::-webkit-scrollbar-track {
  background-color: transparent;
}
.widget-item__filter::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.widget-item__filter:hover::-webkit-scrollbar-track {
  background-color: #dedede;
}
.widget-item__filter:hover::-webkit-scrollbar-thumb {
  background-color: #363636;
}
.widget-item__list li {
  margin-top: 8px;
  position: relative;
}
.widget-item__list li:first-child {
  margin-top: 0;
}
.widget-item__list li label {
  font-size: 16px;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: block;
}
.widget-item__list li label span {
  position: relative;
}
.widget-item__list li label span::before {
  content: "";
  height: 16px;
  width: 16px;
  background-color: #e2e2e2;
  margin-right: 0.6em;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  vertical-align: middle;
  position: relative;
  top: -2px;
}
.widget-item__list li label span::after {
  content: "\ea20";
  font-family: LaStudioIcons;
  font-size: 10px;
  position: absolute;
  left: 3.5px;
  line-height: 1;
  top: 2px;
  color: #ffffff;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.widget-item__list li input[type=checkbox] {
  display: none;
}
.widget-item__list li input[type=checkbox]:checked + label, .widget-item__list li input[type=checkbox]:hover + label {
  color: #d68d67;
}
.widget-item__list li input[type=checkbox]:checked + label span::before, .widget-item__list li input[type=checkbox]:hover + label span::before {
  background-color: #363636;
}
.widget-item__list li input[type=checkbox]:checked + label span::after, .widget-item__list li input[type=checkbox]:hover + label span::after {
  opacity: 1;
}
.widget-item__list li .narrow {
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 24px;
  text-align: center;
  cursor: pointer;
  padding: 0;
  background: none;
  border: 0;
}
.widget-item__list li .narrow i {
  font-size: 12px;
  line-height: 1;
}
.widget-item__list li .children {
  margin-left: 16px;
  margin-top: 6px;
}
.widget-item__color {
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -5px;
  display: flex;
  flex-wrap: wrap;
}
.widget-item__color li {
  padding: 5px;
  width: 50%;
}
.widget-item__color li label {
  font-size: 16px;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: block;
}
.widget-item__color li label span {
  position: relative;
}
.widget-item__color li label span::before {
  content: "";
  height: 20px;
  width: 20px;
  margin-right: 0.6em;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  vertical-align: middle;
  position: relative;
  top: -2px;
  border-radius: 50%;
}
.widget-item__color li label span::after {
  content: "\ea20";
  font-family: LaStudioIcons;
  font-size: 10px;
  position: absolute;
  left: 5px;
  line-height: 1;
  top: 2.5px;
  color: #ffffff;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.widget-item__color li input[type=checkbox] {
  display: none;
}
.widget-item__color li input[type=checkbox]:checked + label, .widget-item__color li input[type=checkbox]:hover + label {
  color: #d68d67;
}
.widget-item__color li input[type=checkbox]:checked + label span::after, .widget-item__color li input[type=checkbox]:hover + label span::after {
  opacity: 1;
}
.widget-item__color li .blue::before {
  background: linear-gradient(to right, #1e73be 50%, #8224e3 50%);
}
.widget-item__color li .blue::after {
  color: #ffffff;
}
.widget-item__color li .bronze::before {
  background: #dbd5bc;
}
.widget-item__color li .bronze::after {
  color: #ffffff;
}
.widget-item__color li .green::before {
  background: linear-gradient(to right, #a4dd7b 50%, #81d742 50%);
}
.widget-item__color li .green::after {
  color: #ffffff;
}
.widget-item__color li .pink::before {
  background: #ffafce;
}
.widget-item__color li .pink::after {
  color: #ffffff;
}
.widget-item__color li .polar-blue::before {
  background: #b8d3cb;
}
.widget-item__color li .polar-blue::after {
  color: #363636;
}
.widget-item__color li .red::before {
  background: linear-gradient(to right, #dd0000 50%, #c60000 50%);
}
.widget-item__color li .red::after {
  color: #ffffff;
}
.widget-item__color li .white::before {
  background: #ffffff;
  border: 1px solid #dedede;
}
.widget-item__color li .white::after {
  color: #363636;
}
.widget-item__color li .yellow::before {
  background: #efd465;
}
.widget-item__color li .yellow::after {
  color: #ffffff;
}
.widget-item__list-2 {
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -5px;
  display: flex;
  flex-wrap: wrap;
}
.widget-item__list-2 li {
  padding: 5px;
}
.widget-item__list-2 li a, .widget-item__list-2 li label {
  font-size: 14px;
  line-height: 20px;
  border: 1px solid #dedede;
  padding: 10px 14px 8px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  cursor: pointer;
}
.widget-item__list-2 li a:hover, .widget-item__list-2 li label:hover {
  border-color: #d68d67;
  background-color: #d68d67;
  color: #ffffff;
}
.widget-item__list-2 li input[type=checkbox] {
  display: none;
}
.widget-item__list-2 li input[type=checkbox]:checked + label, .widget-item__list-2 li input[type=checkbox]:hover + label {
  border-color: #d68d67;
  background-color: #d68d67;
  color: #ffffff;
}

.sidebar-shop-filter-widget > * {
  margin-top: 30px;
}
.sidebar-shop-filter-widget > *:first-child {
  margin-top: 0;
}

.sidebar-widget-item {
  padding: 30px;
  border: 1px solid #dedede;
}
.sidebar-widget-item__title {
  font-size: 24px;
  font-weight: 600;
  color: #363636;
  padding-bottom: 12px;
  margin-bottom: 30px;
  position: relative;
}
.sidebar-widget-item__title::before {
  position: absolute;
  content: "";
  width: 32px;
  height: 2px;
  background-color: #363636;
  left: 0;
  bottom: 0;
}
.sidebar-widget-item__filter {
  scrollbar-width: auto;
  scrollbar-color: #363636 #dedede;
}
.sidebar-widget-item__filter::-webkit-scrollbar {
  width: 2px;
}
.sidebar-widget-item__filter::-webkit-scrollbar-track {
  background-color: transparent;
}
.sidebar-widget-item__filter::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.sidebar-widget-item__filter:hover::-webkit-scrollbar-track {
  background-color: #dedede;
}
.sidebar-widget-item__filter:hover::-webkit-scrollbar-thumb {
  background-color: #363636;
}
.sidebar-widget-item__list li {
  margin-top: 8px;
  position: relative;
}
.sidebar-widget-item__list li:first-child {
  margin-top: 0;
}
.sidebar-widget-item__list li label {
  position: relative;
  font-size: 16px;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: block;
}
.sidebar-widget-item__list li label span::before {
  content: "";
  height: 16px;
  width: 16px;
  background-color: #e2e2e2;
  margin-right: 0.6em;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  vertical-align: middle;
  position: relative;
  top: -2px;
}
.sidebar-widget-item__list li label span::after {
  content: "\ea20";
  font-family: LaStudioIcons;
  font-size: 10px;
  position: absolute;
  left: 3.5px;
  line-height: 1;
  top: 6px;
  color: #ffffff;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.sidebar-widget-item__list li input[type=checkbox] {
  display: none;
}
.sidebar-widget-item__list li input[type=checkbox]:checked + label, .sidebar-widget-item__list li input[type=checkbox]:hover + label {
  color: #d68d67;
}
.sidebar-widget-item__list li input[type=checkbox]:checked + label span::before, .sidebar-widget-item__list li input[type=checkbox]:hover + label span::before {
  background-color: #363636;
}
.sidebar-widget-item__list li input[type=checkbox]:checked + label span::after, .sidebar-widget-item__list li input[type=checkbox]:hover + label span::after {
  opacity: 1;
}
.sidebar-widget-item__list li .narrow {
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 24px;
  text-align: center;
  cursor: pointer;
  padding: 0;
  background: none;
  border: 0;
}
.sidebar-widget-item__list li .narrow i {
  font-size: 12px;
  line-height: 1;
}
.sidebar-widget-item__list li .children {
  margin-left: 16px;
  margin-top: 6px;
}
.sidebar-widget-item__color {
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -5px;
  display: flex;
  flex-wrap: wrap;
}
.sidebar-widget-item__color li {
  padding: 5px;
  width: 50%;
}
.sidebar-widget-item__color li label {
  position: relative;
  font-size: 16px;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  display: block;
}
.sidebar-widget-item__color li label span::before {
  content: "";
  height: 30px;
  width: 30px;
  margin-right: 0.6em;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  vertical-align: middle;
  position: relative;
  top: -2px;
  border-radius: 50%;
}
.sidebar-widget-item__color li label span::after {
  content: "\ea20";
  font-family: LaStudioIcons;
  font-size: 10px;
  position: absolute;
  left: 10px;
  line-height: 1;
  top: 8px;
  color: #ffffff;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.sidebar-widget-item__color li input[type=checkbox] {
  display: none;
}
.sidebar-widget-item__color li input[type=checkbox]:checked + label, .sidebar-widget-item__color li input[type=checkbox]:hover + label {
  color: #d68d67;
}
.sidebar-widget-item__color li input[type=checkbox]:checked + label span::after, .sidebar-widget-item__color li input[type=checkbox]:hover + label span::after {
  opacity: 1;
}
.sidebar-widget-item__color li .blue::before {
  background: linear-gradient(to right, #1e73be 50%, #8224e3 50%);
}
.sidebar-widget-item__color li .blue::after {
  color: #ffffff;
}
.sidebar-widget-item__color li .bronze::before {
  background: #dbd5bc;
}
.sidebar-widget-item__color li .bronze::after {
  color: #ffffff;
}
.sidebar-widget-item__color li .green::before {
  background: linear-gradient(to right, #a4dd7b 50%, #81d742 50%);
}
.sidebar-widget-item__color li .green::after {
  color: #ffffff;
}
.sidebar-widget-item__color li .pink::before {
  background: #ffafce;
}
.sidebar-widget-item__color li .pink::after {
  color: #ffffff;
}
.sidebar-widget-item__color li .polar-blue::before {
  background: #b8d3cb;
}
.sidebar-widget-item__color li .polar-blue::after {
  color: #363636;
}
.sidebar-widget-item__color li .red::before {
  background: linear-gradient(to right, #dd0000 50%, #c60000 50%);
}
.sidebar-widget-item__color li .red::after {
  color: #ffffff;
}
.sidebar-widget-item__color li .white::before {
  background: #ffffff;
  border: 1px solid #dedede;
}
.sidebar-widget-item__color li .white::after {
  color: #363636;
}
.sidebar-widget-item__color li .yellow::before {
  background: #efd465;
}
.sidebar-widget-item__color li .yellow::after {
  color: #ffffff;
}
.sidebar-widget-item__list-2 {
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -5px;
  display: flex;
  flex-wrap: wrap;
}
.sidebar-widget-item__list-2 li {
  padding: 5px;
}
.sidebar-widget-item__list-2 li a, .sidebar-widget-item__list-2 li label {
  font-size: 14px;
  line-height: 20px;
  border: 1px solid #dedede;
  padding: 10px 14px 8px 14px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  cursor: pointer;
}
.sidebar-widget-item__list-2 li a:hover, .sidebar-widget-item__list-2 li label:hover {
  border-color: #d68d67;
  background-color: #d68d67;
  color: #ffffff;
}
.sidebar-widget-item__list-2 li input[type=checkbox] {
  display: none;
}
.sidebar-widget-item__list-2 li input[type=checkbox]:checked + label, .sidebar-widget-item__list-2 li input[type=checkbox]:hover + label {
  border-color: #d68d67;
  background-color: #d68d67;
  color: #ffffff;
}
.sidebar-widget-item .filter-slider {
  height: 2px;
  position: relative;
  background: #d8d8d8;
  border-radius: 5px;
}
.sidebar-widget-item .filter-slider .filter-progress {
  height: 100%;
  position: absolute;
  border-radius: 5px;
  background: #d68d67;
}
.sidebar-widget-item .filter-range-input {
  position: relative;
}
.sidebar-widget-item .filter-range-input input {
  position: absolute;
  width: 100%;
  height: 2px;
  top: -2px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}
.sidebar-widget-item .filter-range-input input[type=range]::-webkit-slider-thumb {
  z-index: 2;
  width: 8px;
  height: 8px;
  box-shadow: 0 0 0 4px #d68d67;
  background-color: #ffffff;
  cursor: ew-resize;
  border-radius: 50%;
  pointer-events: auto;
  -webkit-appearance: none;
}
.sidebar-widget-item .filter-range-input input[type=range]::-moz-range-thumb {
  border: none;
  z-index: 2;
  width: 8px;
  height: 8px;
  box-shadow: 0 0 0 4px #d68d67;
  background-color: #ffffff;
  cursor: ew-resize;
  border-radius: 50%;
  pointer-events: auto;
  -webkit-appearance: none;
}
.sidebar-widget-item .filter-price-value {
  margin-top: 15px;
  font-size: 14px;
  margin-bottom: 10px;
  color: #494949;
  display: flex;
  align-items: center;
}
.sidebar-widget-item .filter-price-value input {
  width: 40px;
  text-align: center;
  border: 0;
  background: none;
  padding: 0;
  font-size: 14px;
  color: #494949;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.sidebar-widget-item .filter-price-value span {
  margin: 0 2px;
}
.sidebar-widget-item .filter-price-btn {
  font-size: 14px;
  display: inline-block;
  border: 1px solid #dedede;
  background: transparent;
  color: #363636;
  margin: 0;
  cursor: pointer;
  line-height: 1;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  padding: 10px 27px;
}
.sidebar-widget-item .filter-price-btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

.sidebar-widget-banner {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 110px 40px 55px;
}
.sidebar-widget-banner__title {
  color: rgba(198, 32, 32, 0);
  font-size: 80px;
  font-weight: 400;
  line-height: 0.9em;
  -webkit-text-stroke-width: 1px;
  stroke-width: 1px;
  -webkit-text-stroke-color: #c62020;
  stroke: #c62020;
}
.sidebar-widget-banner__sub-title {
  color: #363636;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.5em;
  margin-bottom: 10px;
}
.sidebar-widget-banner__btn {
  font-size: 14px;
  line-height: 35px;
  color: #363636;
  background-color: #ffffff;
  box-shadow: 5px 5px 0 0 #363636;
  padding: 0 40px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.sidebar-widget-banner__btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

.shop-masonry .single-product {
  height: 545px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-masonry .single-product {
    height: auto;
  }
}
.shop-masonry .single-product__thumbnail--holder a {
  padding-bottom: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-masonry .single-product__thumbnail--holder a {
    padding-bottom: 108%;
  }
}

.shop-filter-dropdown__btn {
  font-size: 18px;
  font-weight: 500;
  line-height: 20px;
  padding: 15px 20px;
  border: 1px solid #d6d6d6;
  width: 100%;
  min-width: 200px;
  text-align: left;
  background: none;
  color: #363636;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .shop-filter-dropdown__btn {
    font-size: 16px;
    padding: 13px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .shop-filter-dropdown__btn {
    min-width: none;
    font-size: 16px;
    padding: 13px;
  }
}
.shop-filter-dropdown__btn .icon {
  margin-left: 10px;
}
.shop-filter-dropdown .widget-item {
  border-radius: 0;
  border: 0;
  padding: 0;
}
.shop-filter-dropdown .widget-item__filter {
  min-width: 300px;
  overflow-y: auto;
  max-height: 265px;
  background-color: #fff;
  padding: 25px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}

/*----------------------------------------*/
/*  03.02 Product Single CSS
/*----------------------------------------*/
.product-single-wrapper {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: -30px;
}

.product-single-col-1 {
  width: 48%;
  margin-top: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-single-col-1 {
    width: 100%;
  }
}

.product-single-col-2 {
  width: 47%;
  margin-top: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-single-col-2 {
    width: 100%;
  }
}

.product-single-carousel .swiper-button-next,
.product-single-slide .swiper-button-next {
  right: 0;
}
.product-single-carousel .swiper-button-prev,
.product-single-slide .swiper-button-prev {
  left: 0;
}

.product-single-slide-item {
  padding-bottom: 105%;
  position: relative;
}
.product-single-slide-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
  background-position: top;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  background-color: #f9f9f9;
}

.product-single-zoom .zoom {
  position: absolute;
  font-size: 2em;
  z-index: 1;
  text-indent: -9999px;
  border-radius: 100%;
  box-sizing: content-box;
  bottom: 30px;
  right: 30px;
  width: 46px;
  height: 46px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.328' height='16.328'%3E%3Cg fill='none' stroke='%23212121' stroke-miterlimit='10'%3E%3Cpath d='m15.5.828-6 6'/%3E%3Cpath d='M9.697.5h6.131v6.131' stroke-linecap='square'/%3E%3Cpath d='m.5 15.828 6-6'/%3E%3Cpath d='M.5 9.697v6.131h6.131' stroke-linecap='square'/%3E%3C/g%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-color: #fff;
  box-shadow: 0 0 6px 0 #00000021;
  cursor: pointer;
}
.product-single-zoom a {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
}

.product-single-thumb {
  padding: 30px 15px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-single-thumb {
    padding: 10px 5px 0;
  }
}
.product-single-thumb .swiper-slide {
  opacity: 0.5;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border: 1px solid transparent;
}
.product-single-thumb .swiper-slide.swiper-slide-thumb-active, .product-single-thumb .swiper-slide:hover {
  opacity: 1;
  border-color: #d68d67;
}

.product-single-thumb-item {
  padding-bottom: 23%;
  position: relative;
  cursor: pointer;
}
.product-single-thumb-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
  background-position: top;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  background-color: #f9f9f9;
}

.product-single-carousel .product-single-slide-item {
  padding-bottom: 0;
}
.product-single-carousel .product-single-slide-item img {
  position: relative;
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.product-single-content__title {
  font-size: 36px;
  font-weight: 600;
  font-family: "Playfair Display", serif;
  line-height: 1.15;
  color: #363636;
  margin-bottom: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__title {
    font-size: 26px;
    margin-bottom: 20px;
  }
}
.product-single-content__price-stock {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}
.product-single-content__price-stock > * {
  margin-right: 20px;
}
.product-single-content__price-stock > *:last-child {
  margin-right: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__price-stock {
    margin-bottom: 15px;
  }
}
.product-single-content__price {
  color: #363636;
  font-size: 36px;
  font-weight: 400;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__price {
    font-size: 26px;
  }
}
.product-single-content__price del {
  font-size: 28px;
  color: #494949;
  text-decoration: line-through;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__price del {
    font-size: 22px;
  }
}
.product-single-content__price ins {
  text-decoration: none;
}
.product-single-content__stock {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #363636;
  font-size: 14px;
  line-height: 1;
}
.product-single-content__stock .stock-icon {
  color: #80c685;
  font-size: 20px;
}
.product-single-content__stock.out-of-stock .stock-icon {
  color: #e01a1a;
  font-size: 20px;
}
.product-single-content__short-description {
  margin-bottom: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__short-description {
    margin-bottom: 30px;
  }
}
.product-single-content__countdown {
  max-width: 470px;
  margin-bottom: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__countdown {
    margin-bottom: 20px;
  }
}
.product-single-content__countdown > * {
  margin-top: 10px;
}
.product-single-content__countdown > *:first-child {
  margin-top: 0;
}
.product-single-content__countdown--title {
  color: #363636;
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__countdown--title {
    font-size: 16px;
  }
}
.product-single-content__countdown--stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-single-content__countdown--stock .stock-text {
  font-size: 16px;
  color: #494949;
}
.product-single-content__variable {
  margin-bottom: 25px;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__variable {
    margin-bottom: 15px;
  }
}
.product-single-content__variable .table {
  margin-bottom: 0;
}
.product-single-content__variable .table > :not(caption) > * > * {
  border: 0;
  padding: 4px 0;
  font-weight: 400;
  font-size: 16px;
  color: #494949;
}
.product-single-content__add-to-cart-wrapper {
  margin-bottom: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__add-to-cart-wrapper {
    margin-bottom: 40px;
  }
}
.product-single-content__add-to-cart-wrapper .product-add-compare, .product-single-content__add-to-cart-wrapper .product-add-wishlist {
  background: none;
  color: #494949;
  padding: 0;
  margin: 0 2em 0.5em 0;
  font-size: 14px;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
}
.product-single-content__quantity-add-to-cart {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__quantity-add-to-cart {
    margin-bottom: 10px;
  }
}
.product-single-content__quantity {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 100px;
  text-align: center;
  margin-right: 10px;
}
.product-single-content__quantity button {
  width: 20px;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  font-size: 20px;
  line-height: 1;
  padding: 0;
  border: 0;
  background: none;
}
.product-single-content__quantity input {
  width: 33px;
  display: inline-block;
  height: 58px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  color: inherit;
  text-align: center;
  padding: 0px;
  border: 0;
  background: none;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__quantity input {
    height: 56px;
  }
}
@media only screen and (max-width: 575px) {
  .product-single-content__quantity input {
    height: 50px;
  }
}
.product-single-content__add-to-cart {
  font-size: 16px;
  padding: 22px 50px;
  line-height: 1;
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__add-to-cart {
    font-size: 14px;
  }
}
@media only screen and (max-width: 575px) {
  .product-single-content__add-to-cart {
    padding: 17px 40px;
  }
}
.product-single-content__add-to-cart:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}
.product-single-content__meta {
  margin-bottom: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__meta {
    margin-bottom: 30px;
  }
}
.product-single-content__meta--item {
  display: flex;
  align-items: center;
  padding: calc(5px / 2) 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__meta--item {
    padding: 0;
  }
}
.product-single-content__meta--item:first-child {
  padding-top: 0;
}
.product-single-content__meta--item:last-child {
  padding-bottom: 0;
}
.product-single-content__meta--item .label {
  font-size: 18px;
  line-height: 1.8;
  color: #363636;
  white-space: nowrap;
  margin-right: 4px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-content__meta--item .label {
    font-size: 16px;
  }
}
.product-single-content__meta--item .content {
  font-size: 18px;
  color: #363636;
}
.product-single-content__meta--item .content a {
  color: #494949;
}
.product-single-content__meta--item .content a:hover {
  color: #d68d67;
}
.product-single-content__social {
  display: flex;
  align-items: center;
  gap: 20px;
}
.product-single-content__social .label {
  font-size: 18px;
  line-height: 1.8;
  color: #363636;
  white-space: nowrap;
}
.product-single-content__social .socail-icon {
  display: flex;
}
.product-single-content__social .socail-icon li {
  margin-right: 15px;
}
.product-single-content__social .socail-icon li:last-child {
  margin-right: 0;
}
.product-single-content__social .socail-icon li a {
  width: 40px;
  height: 40px;
  line-height: 42px;
  border-radius: 50%;
  text-align: center;
  background-color: #ffffff;
  font-size: 18px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.product-single-content__social .socail-icon li a:hover {
  background-color: #363636;
  color: #ffffff;
}

.variable-color {
  display: flex;
  flex-wrap: wrap;
}
.variable-color > * {
  margin: 0 4px;
  border-radius: 50%;
}
.variable-color > *:first-child {
  margin-left: 0;
}
.variable-color > *:last-child {
  margin-right: 0;
}
.variable-color > * span {
  display: block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border-radius: 50%;
}
.variable-color__color {
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.variable-color__color.active {
  box-shadow: 0 0 0 1px #d68d67;
}
.variable-color__color.active span {
  transform: scale(0.85);
}
.variable-color .blue {
  background: linear-gradient(to right, #1e73be 50%, #8224e3 50%);
}
.variable-color .green {
  background: linear-gradient(to right, #a4dd7b 50%, #81d742 50%);
}
.variable-color .red {
  background: linear-gradient(to right, #c60000 50%, #dd0000 50%);
}

.variable-size {
  display: flex;
  flex-wrap: wrap;
}
.variable-size > * {
  margin: 0 4px;
  border-radius: 50%;
}
.variable-size > *:first-child {
  margin-left: 0;
}
.variable-size > *:last-child {
  margin-right: 0;
}
.variable-size > * span {
  display: block;
  width: 30px;
  height: 30px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border-radius: 50%;
}
.variable-size__size {
  box-shadow: 0 0 1px;
}
.variable-size__size.active {
  box-shadow: 0 0 0 1px #d68d67;
}

.product-single-countdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
}

.product-single-countdown-item {
  text-align: center;
  margin: 5px;
}
.product-single-countdown-item__value {
  color: #363636;
  font-size: 40px;
  font-weight: 600;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (max-width: 767px) {
  .product-single-countdown-item__value {
    font-size: 36px;
  }
}
.product-single-countdown-item__label {
  color: #494949;
  font-size: 16px;
  line-height: 1;
}

.product-progress {
  position: relative;
  background-color: #e0e0e0;
  border-radius: 2px;
  height: 15px;
  overflow: hidden;
  margin: 10px 0 5px;
}
.product-progress__bar {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #e5643e;
  height: 100%;
  border-radius: 2px;
}

.reset-variable {
  font-size: 12px;
  position: absolute;
  right: 0;
  top: 0;
  padding: 0;
  margin: 0;
  background: none;
  border: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.reset-variable::before {
  content: "\ea3a";
  font-family: LaStudioIcons;
  padding-right: 5px;
  speak: none;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  vertical-align: middle;
  font-weight: 400;
  display: inline-block;
  line-height: 1;
  position: relative;
  top: -1px;
}
.reset-variable.visible {
  opacity: 1;
  visibility: visible;
}

.product-single-tabs .nav {
  border-bottom: 1px solid #cbcbcb;
}
.product-single-tabs .nav li button {
  padding: 10px 45px;
  font-size: 28px;
  color: #494949;
  border: 0;
  background: none;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (max-width: 767px) {
  .product-single-tabs .nav li button {
    font-size: 22px;
  }
}
.product-single-tabs .nav li button:hover, .product-single-tabs .nav li button.active {
  color: #363636;
}
.product-single-tabs .tab-content {
  max-width: 980px;
  width: 100%;
  margin: 0 auto;
  padding-top: 70px;
}

.product-single-accordion .accordion {
  border-radius: 0;
}
.product-single-accordion .accordion .accordion-item {
  border-radius: 0;
  margin-top: 30px;
  border: 1px solid #cbcbcb;
}
.product-single-accordion .accordion .accordion-item > button {
  font-size: 20px;
  font-weight: 400;
  line-height: 1;
  padding: 20px 30px;
  color: #363636;
  display: block;
  width: 100%;
  border: 0;
  background: none;
  text-align: left;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-single-accordion .accordion .accordion-item > button {
    font-size: 18px;
    padding: 18px;
  }
}
.product-single-accordion .accordion .accordion-item > button::after {
  position: absolute;
  content: "−";
  top: 50%;
  transform: translateY(-50%);
  right: 30px;
  color: #363636;
  font-size: 20px;
  font-weight: 400;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-single-accordion .accordion .accordion-item > button::after {
    font-size: 18px;
    right: 18px;
  }
}
.product-single-accordion .accordion .accordion-item > button.collapsed {
  color: #494949;
}
.product-single-accordion .accordion .accordion-item > button.collapsed::after {
  content: "+";
  color: #494949;
}
.product-single-accordion .accordion .accordion-item:first-child {
  margin-top: 0;
}
.product-single-accordion .accordion .accordion-item .accordion-collapse {
  padding: 20px 30px 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .product-single-accordion .accordion .accordion-item .accordion-collapse {
    padding: 18px 18px 40px;
  }
}

.product-single-tab-description-item {
  margin-top: 26px;
}
.product-single-tab-description-item:first-child {
  margin-top: 0;
}
.product-single-tab-description-item__title {
  font-size: 16px;
  font-weight: 600;
  color: #363636;
  margin-bottom: 16px;
}
.product-single-tab-description-item p {
  margin-top: 16px;
}
.product-single-tab-description-item p:first-child {
  margin-top: 0;
}
.product-single-tab-description-item p img {
  width: 150px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.product-single-tab-image {
  text-align: center;
}
.product-single-tab-image__title {
  font-size: 14px;
  font-weight: 400;
  color: #494949;
  margin-bottom: 16px;
}
.product-single-tab-image__image {
  position: relative;
  overflow: hidden;
  max-width: 350px;
  margin: 0 auto;
}
.product-single-tab-image__image img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transform: scale(1);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.product-single-tab-image__image a {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M356.5 235.5C363.6 239.9 368 247.6 368 256C368 264.4 363.6 272.1 356.5 276.5L212.5 364.5C205.1 369 195.8 369.2 188.3 364.9C180.7 360.7 176 352.7 176 344V167.1C176 159.3 180.7 151.3 188.3 147.1C195.8 142.8 205.1 142.1 212.5 147.5L356.5 235.5zM192 168V344C192 346.9 193.6 349.6 196.1 350.1C198.6 352.4 201.7 352.3 204.2 350.8L348.2 262.8C350.6 261.4 352 258.8 352 256C352 253.2 350.6 250.6 348.2 249.2L204.2 161.2C201.7 159.7 198.6 159.6 196.1 161C193.6 162.4 192 165.1 192 168V168zM0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 496C388.5 496 496 388.5 496 256C496 123.5 388.5 16 256 16C123.5 16 16 123.5 16 256C16 388.5 123.5 496 256 496z' fill='%23fff'/%3E%3C/svg%3E");
  width: 50px;
  height: 50px;
  position: absolute;
  background-size: contain;
  background-position: center;
  left: calc(50% - 25px);
  top: calc(50% - 25px);
  z-index: 1;
}
.product-single-tab-image__image:hover img {
  transform: scale(1.1);
}

.product-single-table .table {
  margin-bottom: 0;
}
.product-single-table .table > :not(caption) > * > * {
  border: 1px solid #dedede;
  padding: 10px;
  font-weight: 400;
  font-size: 16px;
  color: #494949;
}

.product-single-review > * {
  margin-top: 80px;
}
.product-single-review > *:first-child {
  margin-top: 0;
}

.related-product-active {
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .related-product-active {
    margin-top: 10px;
  }
}

/*----------------------------------------*/
/*  03.03 - My Account CSS
/*----------------------------------------*/
.my-account-tab address, .my-account-tab p {
  margin-top: 16px;
  font-size: 18px;
  line-height: 1.8;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .my-account-tab address, .my-account-tab p {
    font-size: 16px;
  }
}
.my-account-tab address:first-child, .my-account-tab p:first-child {
  margin-top: 0;
}
.my-account-tab address strong, .my-account-tab p strong {
  font-weight: 600;
}
.my-account-tab__menu {
  width: 100%;
  background: #f7f7f7;
  margin-bottom: 2em;
  text-align: center;
}
.my-account-tab__menu .nav li .account-btn {
  background-color: transparent;
  border: 0;
  display: block;
  padding: 10px 30px;
  font-size: 18px;
  line-height: 1.8;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.my-account-tab__menu .nav li .account-btn.active, .my-account-tab__menu .nav li .account-btn:hover {
  background-color: #d68d67;
  color: #ffffff;
}

.my-account-address__content {
  margin-top: 16px;
}
.my-account-address__title {
  font-size: 28px;
  font-weight: 600;
  color: #363636;
  line-height: 1.2;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .my-account-address__title {
    font-size: 24px;
  }
}
.my-account-address__edit {
  font-size: 18px;
  color: #494949;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .my-account-address__edit {
    font-size: 16px;
  }
}
.my-account-address__edit:hover {
  color: #d68d67;
}

.my-account-detail__legend {
  font-size: 18px;
  margin-bottom: 0.3em;
  padding: 0.3em 0;
  color: #333;
  border-bottom: 1px solid #dedede;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .my-account-detail__legend {
    font-size: 16px;
  }
}
.my-account-detail .btn {
  font-size: 16px;
  border-radius: 50px;
}

.my-account-table .table thead tr th, .my-account-table .table thead tr td, .my-account-table .table tbody tr th, .my-account-table .table tbody tr td {
  border-bottom: 1px solid #dedede;
  padding: 10px;
  font-size: 18px;
  font-weight: 400;
  color: #494949;
  vertical-align: middle;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .my-account-table .table thead tr th, .my-account-table .table thead tr td, .my-account-table .table tbody tr th, .my-account-table .table tbody tr td {
    font-size: 16px;
  }
}
.my-account-table .table thead tr th .btn, .my-account-table .table thead tr td .btn, .my-account-table .table tbody tr th .btn, .my-account-table .table tbody tr td .btn {
  font-size: 14px;
  font-weight: 600;
  background-color: #363636;
  color: #ffffff;
  padding: 18px 30px;
  line-height: 1;
  border: 0;
}
.my-account-table .table thead tr th .btn:hover, .my-account-table .table thead tr td .btn:hover, .my-account-table .table tbody tr th .btn:hover, .my-account-table .table tbody tr td .btn:hover {
  background-color: #d68d67;
  color: #ffffff;
}

/*----------------------------------------*/
/*  30.04 - Log In & Register CSS
/*----------------------------------------*/
.login-register {
  background: #ffffff;
  box-shadow: 0 0 30px rgba(141, 141, 141, 0.149);
  padding: 50px;
  position: relative;
}
.login-register__title {
  font-size: 36px;
  font-weight: 600;
  color: #363636;
  line-height: 1.2;
}
.login-register__social {
  display: flex;
  flex-wrap: wrap;
  margin-left: -5px;
  margin-right: -5px;
  margin-top: 25px;
}
.login-register__social li {
  padding: 0 5px;
}
.login-register__social li a {
  display: flex;
  align-items: center;
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 5px;
}
.login-register__social li a .social-icon {
  display: block;
  width: 25px;
  height: 25px;
  position: relative;
  margin-right: 10px;
}
.login-register__social li a .social-icon img {
  position: relative;
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.login-register__social li a .social-text {
  font-size: 18px;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .login-register__social li a .social-text {
    font-size: 16px;
  }
}
.login-register__social li a.social-facebook {
  background: #2660cc;
}
.login-register__social li a.social-facebook .social-icon {
  background-color: #ffffff;
  border-radius: 50%;
  overflow: hidden;
}
.login-register__social li a.social-facebook .social-icon img {
  margin-bottom: -6px;
  transform: scale(1.4);
}
.login-register__social li a.social-google {
  background: #db422e;
}
.login-register .single-form .lost-password {
  align-self: flex-end;
  text-align: right;
  margin: 0 0 -1.4em;
  line-height: 1.4;
  position: relative;
  z-index: 1;
  width: 48%;
  float: right;
  clear: none;
}
.login-register .single-form .privacy-policy-text {
  font-size: 14px;
}
.login-register .single-form__btn {
  width: 180px;
  padding: 17px 30px;
  font-size: 18px;
}

/*----------------------------------------*/
/*  03.05 - Cart CSS
/*----------------------------------------*/
.cart-empty img {
  width: 230px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-empty img {
    width: 150px;
  }
}
.cart-empty p {
  font-size: 34px;
  margin-top: 50px;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-empty p {
    font-size: 20px;
    margin-top: 30px;
  }
}
.cart-empty__btn {
  padding: 18px 25px;
  line-height: 20px;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  text-transform: uppercase;
  border: 1px solid #dedede;
  color: #363636;
  background-color: transparent;
  margin-top: 2em;
}
.cart-empty__btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

.cart-form {
  display: table-cell;
  vertical-align: top;
  width: 100%;
  padding-right: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-form {
    display: block;
    padding-right: 0;
  }
}

.cart-collaterals {
  display: table-cell;
  vertical-align: top;
  min-width: 370px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-collaterals {
    min-width: 100%;
    display: block;
    margin-top: 30px;
  }
}

.free-shipping-goal__label {
  position: relative;
  font-size: 18px;
  line-height: 1.2;
  color: #494949;
}
.free-shipping-goal__label strong {
  font-weight: 600;
}
.free-shipping-goal__loading-bar {
  width: 100%;
  height: 6px;
  background-color: #ebebeb;
  position: relative;
  margin-top: 0.7em;
  margin-bottom: 0.7em;
}
.free-shipping-goal__loading-bar .load-percent {
  display: block;
  height: 100%;
  position: absolute;
  max-width: 100%;
  background-color: #e6992f;
  color: #e6992f;
}
.free-shipping-goal__loading-bar .load-percent::after {
  content: "\ea5f";
  font-size: 20px;
  font-family: "LaStudioIcons";
  border: 1px solid #e6992f;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  right: 0;
  display: block;
  height: auto;
  line-height: 1;
  padding: 0.15em;
  transform: translateY(-50%);
  background: #ffffff;
}

.cart-meta {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-size: 16px;
  margin-bottom: 10px;
}
.cart-meta li {
  position: relative;
  flex: 1;
}
.cart-meta li + li::before {
  content: "";
  border-left: 1px solid #494949;
  height: 20px;
  left: 0;
  position: absolute;
  opacity: 0.5;
  top: 50%;
  transform: translateY(-50%);
}
.cart-meta li a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  gap: 8px;
  position: relative;
  color: #494949;
}
.cart-meta li a svg {
  width: 1rem;
  height: 1rem;
}
.cart-meta li a:hover {
  color: #d68d67;
}

.cart-table {
  margin-top: 36px;
}
.cart-table .table {
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .cart-table .table thead {
    display: none;
  }
}
.cart-table .table thead tr th {
  color: #363636;
  border: 0;
  border-bottom: 1px solid #dbdbdb;
  padding: 10px 0;
  vertical-align: middle;
  font-size: 18px;
  font-weight: 400;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-table .table thead tr th {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .cart-table .table tbody tr.cart-item {
    position: relative;
    display: block;
    padding-top: 10px;
    padding-bottom: 20px;
    padding-left: 100px;
    padding-right: 25px;
    border: 1px solid #eee;
    margin-bottom: 17px;
    overflow: hidden;
  }
}
.cart-table .table tbody tr td {
  color: #494949;
  border: 0;
  padding: 20px 0;
  vertical-align: middle;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .cart-table .table tbody tr td {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .cart-table .table tbody tr td {
    padding: 0;
    width: auto;
    border: none;
    display: block;
    min-width: 0;
    font-size: 14px;
  }
  .cart-table .table tbody tr td[data-title]::before {
    content: attr(data-title) ": ";
    float: left;
    font-size: 13px;
    color: #363636;
    padding-right: 15px;
  }
}
.cart-table .cart-product-remove {
  width: 30px;
}
@media only screen and (max-width: 767px) {
  .cart-table .cart-product-remove {
    position: absolute;
    top: 15px;
    right: 10px;
    z-index: 2;
    width: auto;
  }
}
.cart-table .cart-product-remove .remove {
  font-size: 0;
}
.cart-table .cart-product-remove .remove::before {
  font-family: "LaStudioIcons";
  display: inline-block;
  vertical-align: middle;
  font-weight: normal;
  font-style: normal;
  letter-spacing: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\ea3a";
  font-size: 14px;
  line-height: 30px;
  color: #494949;
}
.cart-table .cart-product-thumbnail {
  width: 90px;
}
@media only screen and (max-width: 767px) {
  .cart-table .cart-product-thumbnail {
    position: absolute;
    width: 70px !important;
    display: inline-block;
    top: 15px;
    left: 15px;
    bottom: 15px;
    overflow: hidden;
  }
}
.cart-table .cart-product-thumbnail a {
  display: block;
  padding-right: 20px;
}
@media only screen and (max-width: 767px) {
  .cart-table .cart-product-thumbnail a {
    padding-right: 0;
  }
}
.cart-table .cart-product-thumbnail img {
  width: 100%;
  height: auto;
}
.cart-table .cart-product-price .price-amount del {
  text-decoration: line-through;
  color: #b2b2b2;
}
.cart-table .cart-product-price .price-amount ins {
  text-decoration: none;
}
.cart-table .cart-product-quantity {
  width: 100px;
}
.cart-table .cart-product-subtotal .price-amount {
  color: #363636;
}
.cart-table__quantity {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  width: 80px;
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .cart-table__quantity {
    width: auto;
    justify-content: flex-start;
  }
}
.cart-table__quantity button {
  width: 18px;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  font-size: 18px;
  line-height: 1;
  padding: 0;
  border: 0;
  background: none;
}
@media only screen and (max-width: 767px) {
  .cart-table__quantity button {
    display: none;
  }
}
.cart-table__quantity input {
  width: 40px;
  display: inline-block;
  height: 30px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  color: inherit;
  text-align: center;
  padding: 0px;
  border: 0;
  background: none;
}
@media only screen and (max-width: 767px) {
  .cart-table__quantity input {
    font-size: 14px;
    height: 24px;
  }
}

.cart-coupon {
  width: 50%;
  position: relative;
}
@media only screen and (max-width: 767px) {
  .cart-coupon {
    width: 100%;
  }
}
.cart-coupon__input {
  display: inline-block;
  border: 0;
  width: 100%;
  font-size: 16px;
  font-weight: 400;
  color: #494949;
  height: 34px;
  border-bottom: 1px solid #d8d8d8;
  padding-right: 100px;
}
@media only screen and (max-width: 767px) {
  .cart-coupon__input {
    padding: 10px 20px;
    border: 1px solid #d8d8d8;
    line-height: 1.8;
    height: auto;
    font-size: 16px;
  }
}
.cart-coupon__btn {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: 0;
  padding: 0;
  height: auto;
  font-size: 14px;
  font-weight: 600;
  line-height: 34px;
  color: #363636;
  white-space: nowrap;
}
@media only screen and (max-width: 767px) {
  .cart-coupon__btn {
    position: relative;
    width: 100%;
    background-color: #363636;
    margin-top: 10px;
    color: #ffffff;
    padding: 18px 30px;
    line-height: 1.2;
  }
  .cart-coupon__btn:hover {
    background-color: #d68d67;
    color: #ffffff;
  }
}

.cart-update-btn {
  background: none;
  border: 0;
  padding: 0;
  height: auto;
  font-size: 14px;
  font-weight: 600;
  color: #363636;
  white-space: nowrap;
  align-self: flex-end;
  text-align: right;
  margin-top: -27px;
  line-height: 1.4;
  position: relative;
  z-index: 1;
  float: right;
  clear: none;
}
@media only screen and (max-width: 767px) {
  .cart-update-btn {
    float: none;
    position: relative;
    width: 100%;
    background-color: #363636;
    margin-top: 10px;
    color: #ffffff;
    padding: 18px 30px;
    line-height: 1.2;
    text-align: center;
  }
  .cart-update-btn:hover {
    background-color: #d68d67;
    color: #ffffff;
  }
}

.shipping-methods li {
  margin-top: 0px;
}
.shipping-methods li:first-child {
  margin-top: 0;
}
.shipping-methods li label {
  color: #494949;
  font-weight: 400;
}
.shipping-methods li label .price {
  color: #363636;
}

.cart-totals {
  background-color: #f9f9f9;
  padding: 30px 30px 0;
  position: relative;
  position: sticky;
  top: calc(30px + 100px + 0);
}
.cart-totals__title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
  color: #363636;
  margin-bottom: 20px;
}
.cart-totals__checkout {
  margin-left: -30px;
  margin-right: -30px;
  text-align: center;
}
.cart-totals__checkout a {
  display: block;
  cursor: pointer;
  padding: 20px 30px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  border: 0;
  line-height: 1;
  font-size: 20px;
  font-weight: 400;
  background-color: #363636;
  color: #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-totals__checkout a {
    font-size: 16px;
  }
}
.cart-totals__checkout a:hover {
  background-color: #d68d67;
  color: #ffffff;
}
.cart-totals .table {
  margin-bottom: 0;
}
.cart-totals .table tbody tr {
  display: flex;
  justify-content: space-between;
}
.cart-totals .table tbody tr th, .cart-totals .table tbody tr td {
  font-size: 18px;
  font-weight: 600;
  color: #363636;
  vertical-align: top;
  padding: 5px 0;
  border: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .cart-totals .table tbody tr th, .cart-totals .table tbody tr td {
    font-size: 16px;
  }
}
.cart-totals .table tbody tr th strong, .cart-totals .table tbody tr td strong {
  font-weight: 400;
}
.cart-totals .order-total {
  border-top: 1px solid #dbdbdb;
  padding-top: 10px;
  margin-top: 10px;
}

/*----------------------------------------*/
/*  03.06 - Order Tracking CSS
/*----------------------------------------*/
.order-tracking-banner {
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 840px;
  padding: 0 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .order-tracking-banner {
    height: 640px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .order-tracking-banner {
    height: 540px;
  }
}
@media only screen and (max-width: 767px) {
  .order-tracking-banner {
    height: 340px;
  }
}
.order-tracking-banner__sub-title {
  font-size: 32px;
  font-weight: 600;
  line-height: 1;
  color: #ffffff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .order-tracking-banner__sub-title {
    font-size: 20px;
  }
}
.order-tracking-banner__title {
  color: #ffffff;
  font-size: 46px;
  font-weight: 600;
  line-height: 1;
  margin-top: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .order-tracking-banner__title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .order-tracking-banner__title {
    font-size: 24px;
  }
}
.order-tracking-banner__btn {
  margin-top: 50px;
  background-color: #ffffff;
  border-color: #ffffff;
  font-size: 20px;
  font-weight: 400;
  line-height: 55px;
  color: #363636;
  padding: 0 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .order-tracking-banner__btn {
    font-size: 16px;
    line-height: 46px;
    padding: 0 30px;
  }
}
@media only screen and (max-width: 767px) {
  .order-tracking-banner__btn {
    margin-top: 30px;
  }
}
.order-tracking-banner__btn:hover {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}

.order-tracking-form {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 840px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .order-tracking-form {
    height: 640px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .order-tracking-form {
    height: 540px;
  }
}
@media only screen and (max-width: 767px) {
  .order-tracking-form {
    height: auto;
    padding: 40px 20px;
  }
}
.order-tracking-form__wrapper {
  max-width: 620px;
  width: 100%;
  margin: 0 auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .order-tracking-form__wrapper {
    width: 80%;
  }
}
.order-tracking-form__wrapper p {
  color: #363636;
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .order-tracking-form__wrapper p {
    font-size: 16px;
  }
}
.order-tracking-form .single-form__label {
  font-size: 18px;
  color: #363636;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .order-tracking-form .single-form__label {
    font-size: 16px;
  }
}
.order-tracking-form .single-form__btn {
  min-width: 160px;
  font-size: 14px;
  line-height: 1.6;
}
@media only screen and (max-width: 767px) {
  .order-tracking-form .single-form__btn {
    width: 100%;
    display: block;
  }
}

/*----------------------------------------*/
/*  03.07 - Wishlist CSS
/*----------------------------------------*/
.wishlist-table .table {
  margin-bottom: 0;
}
.wishlist-table .table thead {
  background: #f9f9f9;
}
@media only screen and (max-width: 767px) {
  .wishlist-table .table thead {
    display: none;
  }
}
.wishlist-table .table thead tr th {
  font-weight: 600;
  padding: 15px 20px;
  color: #363636;
  border: 0;
  vertical-align: middle;
  font-size: 18px;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .wishlist-table .table thead tr th {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .wishlist-table .table tbody tr.wishlist-item {
    position: relative;
    display: block;
    padding-top: 10px;
    padding-bottom: 20px;
    padding-left: 110px;
    padding-right: 25px;
    border-bottom: 1px solid #eee;
    margin-bottom: 17px;
    overflow: hidden;
  }
}
.wishlist-table .table tbody tr td {
  border: 0;
  border-bottom: 1px solid #eee;
  padding: 40px 20px;
  vertical-align: middle;
  font-size: 18px;
  font-weight: 400;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .wishlist-table .table tbody tr td {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .wishlist-table .table tbody tr td {
    padding: 0;
    width: auto;
    border: none;
    display: block;
    min-width: 0;
    font-size: 16px;
  }
  .wishlist-table .table tbody tr td[data-title]::before {
    content: attr(data-title) ": ";
    float: left;
    font-size: 15px;
    color: #363636;
    padding-right: 15px;
  }
}
.wishlist-table .product-remove {
  width: 30px;
}
@media only screen and (max-width: 767px) {
  .wishlist-table .product-remove {
    position: absolute;
    top: 15px;
    right: 10px;
    z-index: 2;
    width: auto;
  }
}
.wishlist-table .product-remove .remove {
  font-size: 0;
}
.wishlist-table .product-remove .remove::before {
  font-family: "LaStudioIcons";
  display: inline-block;
  vertical-align: middle;
  font-weight: normal;
  font-style: normal;
  letter-spacing: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\ea3a";
  font-size: 14px;
  line-height: 30px;
  color: #494949;
}
.wishlist-table .product-thumbnail {
  width: 130px;
  padding: 0 10px;
}
@media only screen and (max-width: 767px) {
  .wishlist-table .product-thumbnail {
    position: absolute;
    width: 90px !important;
    display: inline-block;
    top: 15px;
    left: 0;
    bottom: 15px;
    overflow: hidden;
  }
}
.wishlist-table .product-thumbnail a {
  display: block;
}
.wishlist-table .product-thumbnail img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.wishlist-table .product-name a {
  font-weight: 600;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.wishlist-table .product-name a:hover {
  color: #d68d67;
}
.wishlist-table .product-price span del {
  text-decoration: line-through;
}
.wishlist-table .product-price span ins {
  text-decoration: none;
  color: #d68d67;
}
.wishlist-table .product-stock .in-stock::before {
  color: #10b705;
  content: "\ea20";
  font-family: "LaStudioIcons";
  display: block;
  text-transform: none;
}
@media only screen and (max-width: 767px) {
  .wishlist-table .product-stock .in-stock::before {
    display: none;
  }
}
.wishlist-table__btn {
  padding: 14px 22px;
  font-size: 14px;
  line-height: 1;
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
@media only screen and (max-width: 767px) {
  .wishlist-table__btn {
    font-size: 13px;
    padding: 9px 18px;
    margin-top: 5px;
  }
}
.wishlist-table__btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}

/*----------------------------------------*/
/*  03.08 - Compare CSS
/*----------------------------------------*/
.compare-table .table {
  display: block;
  margin: 0;
  width: 100%;
  overflow: auto;
  padding: 0;
  color: #363636;
  border: 0;
}
.compare-table .table::-webkit-scrollbar {
  height: 6px;
  border-radius: 50px;
}
.compare-table .table::-webkit-scrollbar-track {
  background-color: transparent;
}
.compare-table .table::-webkit-scrollbar-thumb {
  background-color: #eee;
  border-radius: 50px;
}
.compare-table .table tbody tr th {
  min-width: 268px;
  background-color: #ffffff;
  vertical-align: middle;
  padding: 10px;
  position: sticky;
  left: 0;
  color: #363636;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  border-right: none;
  z-index: 1;
  border: 0;
  border-bottom: 1px solid #eee;
  line-height: 1.8;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .compare-table .table tbody tr th {
    font-size: 16px;
    min-width: 230px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .compare-table .table tbody tr th {
    font-size: 16px;
    min-width: 150px;
  }
}
@media only screen and (max-width: 767px) {
  .compare-table .table tbody tr th {
    font-size: 14px;
    min-width: 100px;
  }
}
.compare-table .table tbody tr td {
  min-width: 300px;
  max-width: 100%;
  text-align: center;
  padding: 10px 20px;
  vertical-align: middle;
  font-weight: 600;
  border: 0;
  border-bottom: 1px solid #eee;
  font-size: 18px;
  line-height: 1.8;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .compare-table .table tbody tr td {
    font-size: 16px;
    min-width: 260px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .compare-table .table tbody tr td {
    font-size: 16px;
    min-width: 200px;
  }
}
@media only screen and (max-width: 767px) {
  .compare-table .table tbody tr td {
    font-size: 14px;
    min-width: 200px;
  }
}
.compare-table .table tbody tr td.td-placeholder::before {
  content: "";
  display: inline-block;
  width: 60px;
  height: 8px;
  background-color: #eee;
  border-radius: 2px;
}
.compare-table .table tbody tr:first-child td, .compare-table .table tbody tr:first-child th {
  padding-top: 0;
}
.compare-table .table tbody tr.compare-tr-info {
  height: 250px;
  text-align: center;
}
.compare-table .table tbody tr.compare-tr-info .remove {
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 50%;
  opacity: 0.8;
  font-size: 12px;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  margin: 0 auto 10px;
}
.compare-table .table tbody tr.compare-tr-info .remove:hover {
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
.compare-table .table tbody tr.compare-tr-info .product-name > a {
  display: block;
}
.compare-table .table tbody tr.compare-tr-info .product-name img {
  width: 120px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  margin: 0 auto;
}
.compare-table .table tbody tr.compare-tr-info .product-name .name {
  margin: 15px 0 10px;
  font-weight: 400;
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .compare-table .table tbody tr.compare-tr-info .product-name .name {
    font-size: 16px;
  }
}
.compare-table .table tbody tr.compare-tr-info .product-action .btn {
  font-weight: 500;
  padding: 8px 15px;
  line-height: 1;
  font-size: 12px;
  background-color: #363636;
  border-color: #363636;
  color: #ffffff;
}
.compare-table .table tbody tr.compare-tr-info .product-action .btn:hover {
  background-color: #d68d67;
  border-color: #d68d67;
  color: #ffffff;
}
.compare-table .table tbody tr.compare-tr-info .td-placeholder {
  background-image: url(../images/placeholder.png);
  background-repeat: no-repeat;
  background-position: center;
}
.compare-table .table tbody tr.compare-tr-info .td-placeholder::before {
  display: none;
}
.compare-table .table tbody tr.compare-tr-price span del {
  text-decoration: line-through;
}
.compare-table .table tbody tr.compare-tr-price span ins {
  text-decoration: none;
  color: #d68d67;
}
.compare-table .table tbody tr.compare-tr-stock .in-stock {
  color: #10b705;
}
/*----------------------------------------*/
/*  03.09 - Checkout CSS
/*----------------------------------------*/
.checkout-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.checkout-col-1 {
  width: 62%;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-col-1 {
    width: 100%;
  }
}

.checkout-col-2 {
  width: 35%;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-col-2 {
    width: 100%;
  }
}

.checkout-info__title {
  padding: 16px 30px 16px 60px;
  position: relative;
  background-color: #f9f9f9;
  color: #363636;
  line-height: 1.6;
  font-size: 18px;
  font-weight: 400;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-info__title {
    font-size: 16px;
    padding: 10px 40px 10px 50px;
  }
}
.checkout-info__title img, .checkout-info__title i {
  position: absolute;
  top: 22px;
  left: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-info__title img, .checkout-info__title i {
    left: 20px;
  }
}
.checkout-info__title button {
  padding: 0;
  border: 0;
  background: none;
  display: contents;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.checkout-info__title button:hover {
  color: #d68d67;
}
.checkout-info__body {
  margin-top: 20px;
}
.checkout-info__body p {
  font-size: 18px;
  color: #494949;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-info__body p {
    font-size: 16px;
  }
}
.checkout-info__body .single-form__label {
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-info__body .single-form__label {
    font-size: 16px;
  }
}
.checkout-info__body .single-form__input {
  padding: 14px 20px;
  max-width: 350px;
}

.checkout-coupon-form {
  display: flex;
  gap: 5px;
}
.checkout-coupon-form .single-form__btn {
  font-size: 14px;
  padding: 15px 30px;
}

.checkout-login-form p {
  margin-top: 20px;
}
.checkout-login-form .login-register__social {
  margin-top: 5px;
}
.checkout-login-form .single-form__btn {
  min-width: 180px;
  font-size: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-login-form .single-form__btn {
    font-size: 16px;
  }
}

.checkout-details {
  margin-top: 50px;
}
.checkout-details__billing {
  margin-top: -25px;
}
.checkout-details__title {
  font-size: 32px;
  font-weight: 600;
  color: #363636;
  margin-bottom: 30px;
  padding-bottom: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-details__title {
    font-size: 24px;
  }
}
.checkout-details__order-review {
  border: 1px solid #9b9b9b;
  padding: 15px 40px 0;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-details__order-review {
    padding: 15px 30px 0;
  }
}
.checkout-details__order-review .table {
  margin-bottom: 0;
}
.checkout-details__order-review .table tfoot tr, .checkout-details__order-review .table thead tr {
  border-bottom: 1px solid #dedede;
  margin-bottom: 15px;
  margin-top: 10px;
  font-size: 20px;
  display: flex;
  justify-content: space-between;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-details__order-review .table tfoot tr, .checkout-details__order-review .table thead tr {
    font-size: 18px;
  }
}
.checkout-details__order-review .table tfoot tr.cart-subtotal, .checkout-details__order-review .table thead tr.cart-subtotal {
  margin-top: 20px;
}
.checkout-details__order-review .table tfoot tr.order-total, .checkout-details__order-review .table thead tr.order-total {
  margin-top: 0;
  padding-bottom: 6px;
}
.checkout-details__order-review .table tfoot tr td, .checkout-details__order-review .table tfoot tr th, .checkout-details__order-review .table thead tr td, .checkout-details__order-review .table thead tr th {
  font-weight: 400;
  padding: 3px 0;
  border: 0;
}
.checkout-details__order-review .table tfoot tr td strong, .checkout-details__order-review .table tfoot tr th strong, .checkout-details__order-review .table thead tr td strong, .checkout-details__order-review .table thead tr th strong {
  font-weight: 600;
}
.checkout-details__order-review .table tbody tr {
  display: flex;
  justify-content: space-between;
}
.checkout-details__order-review .table tbody tr td {
  padding: 2.5px 0;
  font-size: 18px;
  font-weight: 400;
  border-bottom: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-details__order-review .table tbody tr td {
    font-size: 16px;
  }
}
.checkout-details__order-review .table tbody tr td:first-child {
  padding-right: 10px;
}
.checkout-details__order-review .table tbody tr td strong {
  font-weight: 600;
}
.checkout-details__order-review .shipping-methods li {
  margin-top: 10px;
}
.checkout-details__order-review .shipping-methods li:first-child {
  margin-top: 0;
}
.checkout-details__order-review .shipping-methods li label {
  color: #494949;
  font-weight: 400;
}
.checkout-details__order-review .shipping-methods li label .price {
  color: #363636;
  font-weight: 600;
}
.checkout-details__payment-method {
  padding: 10px 0;
}
.checkout-details__payment-method .accordion-item {
  padding: 0;
  border: 0;
  border-radius: 0;
}
.checkout-details__payment-method .single-form {
  margin-top: 0;
}
.checkout-details__privacy-policy {
  font-size: 14px;
  color: #494949;
  padding-bottom: 25px;
}
.checkout-details__btn {
  margin-left: -40px;
  margin-right: -40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-details__btn {
    margin-left: -30px;
    margin-right: -30px;
  }
}
.checkout-details__btn .btn {
  padding: 20px 30px;
  border: 0;
  line-height: 20px;
  font-weight: 400;
  font-size: 20px;
  width: 100%;
  background-color: #363636;
  color: #ffffff;
}
.checkout-details__btn .btn:hover {
  background-color: #d68d67;
  color: #ffffff;
}
.checkout-details .checkout-account, .checkout-details .checkout-shipping {
  display: none;
}
.checkout-details .single-form__label {
  font-size: 18px;
  margin-bottom: 10px;
  display: block;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .checkout-details .single-form__label {
    font-size: 16px;
  }
}
.checkout-details .single-form__input + .single-form__input {
  margin-top: 20px;
}
.checkout-details .payment-method-body {
  padding: 10px 0;
  font-size: 14px;
  color: #494949;
}

/*----------------------------------------*/
/*  03.10 - Thank You CSS
/*----------------------------------------*/
.thank-you-notice {
  text-align: center;
  font-size: 22px;
  color: #008000;
  line-height: 1.8;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .thank-you-notice {
    font-size: 20px;
  }
}

.thank-you-order-overview {
  list-style-type: disc;
  padding-left: 17px;
}
.thank-you-order-overview li {
  font-size: 18px;
  font-weight: 400;
  color: #494949;
  margin-top: 8px;
  line-height: 1.8;
}
.thank-you-order-overview li strong {
  font-weight: 600;
}

.thank-you-title {
  font-size: 30px;
  font-weight: 600;
  color: #363636;
  line-height: 1.2;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .thank-you-title {
    font-size: 24px;
  }
}

.thank-you-order-details {
  margin-top: 40px;
}
.thank-you-order-details .table {
  margin-bottom: 0;
}
.thank-you-order-details .table tr th,
.thank-you-order-details .table tr td {
  font-size: 18px;
  font-weight: 400;
  color: #494949;
  padding: 12px 10px;
  vertical-align: top;
  border-bottom: 1px solid #dedede;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .thank-you-order-details .table tr th,
.thank-you-order-details .table tr td {
    font-size: 16px;
  }
}
.thank-you-order-details .table tr th strong,
.thank-you-order-details .table tr td strong {
  font-weight: 600;
}
.thank-you-order-details .table tr th small,
.thank-you-order-details .table tr td small {
  font-size: 14px;
}

.thank-you-customer-details {
  margin-top: 40px;
}
.thank-you-customer-details address {
  font-size: 18px;
  font-weight: 400;
  color: #494949;
  line-height: 1.8;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .thank-you-customer-details address {
    font-size: 16px;
  }
}

/*----------------------------------------*/
/*  03.11 - Blog CSS
/*----------------------------------------*/
.blog-container {
  max-width: 1080px;
}

.blog-menu-items {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: -10px;
  margin-bottom: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-menu-items {
    margin-bottom: 50px;
  }
}
.blog-menu-items li a {
  color: #494949;
  margin: 10px 40px 0;
  font-size: 20px;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-menu-items li a {
    font-size: 18px;
    margin: 10px 30px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-menu-items li a {
    font-size: 18px;
    margin: 10px 20px 0;
  }
}
@media only screen and (max-width: 767px) {
  .blog-menu-items li a {
    font-size: 18px;
    margin: 10px 15px 0;
  }
}
.blog-menu-items li a:hover {
  color: #d68d67;
}

.blog-items > * {
  margin-top: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-items > * {
    margin-top: 50px;
  }
}
.blog-items > *:first-child {
  margin-top: 0;
}

.blog-row {
  display: flex;
  flex-wrap: wrap;
}

.blog-col-1 {
  flex: 0 0 auto;
  width: 70%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-col-1 {
    width: 68%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-col-1 {
    width: 100%;
  }
}

.blog-col-2 {
  flex: 0 0 auto;
  width: 30%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-col-2 {
    width: 32%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-col-2 {
    width: 100%;
  }
}

.blog-widget-wrapper {
  padding-left: 120px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-widget-wrapper {
    padding-left: 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-widget-wrapper {
    padding-left: 0;
    padding-top: 50px;
  }
}
.blog-widget-wrapper.blog-widget-left {
  padding-left: 0;
  padding-right: 120px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-widget-wrapper.blog-widget-left {
    padding-right: 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-widget-wrapper.blog-widget-left {
    padding-right: 0;
  }
}
.blog-widget-wrapper > * {
  margin-top: 50px;
}
.blog-widget-wrapper > *:first-child {
  margin-top: 0;
}

.blog-widget-item__title {
  color: #363636;
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-widget-item__title {
    font-size: 22px;
    margin-bottom: 20px;
  }
}

.blog-widget-search {
  position: relative;
}
.blog-widget-search input {
  width: 100%;
  display: block;
  min-height: 20px;
  width: 100%;
  font-size: 16px;
  line-height: 1.8;
  padding: 10px 20px;
  background-color: transparent;
  border: 1px solid #cecece;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.blog-widget-search button {
  font-size: 18px;
  background-color: transparent;
  color: #363636;
  padding: 0 15px;
  border: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
  line-height: 1;
}
.blog-widget-search button:hover {
  color: #d68d67;
}

.blog-widget-author-box {
  display: flex;
  align-items: center;
}
.blog-widget-author-box__image {
  flex-shrink: 0;
}
.blog-widget-author-box__image img {
  width: 70px;
  height: 70px;
  border-radius: 70px;
  -o-object-fit: cover;
  object-fit: cover;
}
.blog-widget-author-box__content {
  padding-left: 20px;
  flex-grow: 1;
}
.blog-widget-author-box__content p {
  color: #494949;
  font-family: "Playfair Display", serif;
  font-size: 16px;
  font-weight: 400;
  font-style: italic;
  line-height: 1.375;
}

.blog-widget-list__categories {
  margin-top: -10px;
}
.blog-widget-list__categories li {
  padding: 7px 0;
  margin-bottom: 5px;
}
.blog-widget-list__categories li a {
  font-size: 16px;
  font-weight: 400;
  color: #494949;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.blog-widget-list__categories li a:hover {
  color: #d68d67;
}

.blog-widget-post__list li {
  border-top: 1px solid #e0e0e0;
  padding: 30px 0;
}
.blog-widget-post__list li:first-child {
  padding-top: 0;
  border-top: 0;
}
.blog-widget-post__list li:last-child {
  padding-bottom: 0;
}

.blog-widget-post-item {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}
.blog-widget-post-item__thumbnail {
  margin: 0;
  overflow: hidden;
  width: 70px;
}
.blog-widget-post-item__thumbnail a {
  display: block;
  padding-bottom: 115%;
  position: relative;
}
.blog-widget-post-item__thumbnail img {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  vertical-align: middle;
  transition: transform 0.6s, opacity 0.6s, visibility 0.6s;
}
.blog-widget-post-item__content {
  width: calc(100% - 70px - 20px);
}
.blog-widget-post-item__meta {
  color: #494949;
  font-size: 12px;
  font-weight: 400;
  padding-bottom: 4px;
}
.blog-widget-post-item__title {
  color: #363636;
  font-size: 16px;
  font-weight: 600;
}

.blog-widget-instagram-item a {
  display: block;
  position: relative;
}
.blog-widget-instagram-item__image {
  position: relative;
  padding-bottom: 100%;
}
.blog-widget-instagram-item__image::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #363636;
  transition: opacity 0.5s cubic-bezier(0.21, 0.75, 0.27, 0.96);
  pointer-events: none;
  opacity: 0;
}
.blog-widget-instagram-item__image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog-widget-instagram-item__icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.5s cubic-bezier(0.21, 0.75, 0.27, 0.96);
  z-index: 2;
  opacity: 0;
}
.blog-widget-instagram-item__icon i {
  font-size: 16px;
  color: #ffffff;
}
.blog-widget-instagram-item:hover .blog-widget-instagram-item__image::after {
  opacity: 0.6;
}
.blog-widget-instagram-item:hover .blog-widget-instagram-item__icon {
  opacity: 1;
}

.blog-widget-social {
  margin-left: -10px;
  margin-right: -10px;
  display: flex;
  flex-wrap: wrap;
}
.blog-widget-social li {
  padding: 0 10px;
}
.blog-widget-social li a {
  width: 52px;
  height: 52px;
  line-height: 54px;
  border-radius: 50%;
  background-color: #ffffff;
  font-size: 20px;
  color: #363636;
  text-align: center;
  box-shadow: 0 0 30px 0 rgba(186, 141, 141, 0.149);
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-widget-social li a {
    font-size: 18px;
    width: 46px;
    height: 46px;
    line-height: 48px;
  }
}
.blog-widget-social li a:hover {
  background-color: #363636;
  color: #ffffff;
}

/*----------------------------------------*/
/*  03.12 - Blog Single CSS
/*----------------------------------------*/
.blog-single-container {
  max-width: 1250px;
}

.blog-single-wrapper {
  max-width: 970px;
  margin-left: auto;
  margin-right: auto;
}

.blog-single__category {
  display: flex;
  flex-wrap: wrap;
}
.blog-single__category li {
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  line-height: 1;
  position: relative;
  color: #d68d67;
}
.blog-single__category li:not(:first-child)::before {
  content: "•";
}
.blog-single__category li a:hover {
  color: #d68d67;
}
.blog-single__title {
  font-size: 48px;
  font-weight: 600;
  line-height: 1;
  margin: 12px 0;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-single__title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single__title {
    font-size: 32px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single__title {
    font-size: 26px;
  }
}
.blog-single__meta {
  display: flex;
  flex-wrap: wrap;
}
.blog-single__meta li {
  font-size: 16px;
  line-height: 1.13;
  color: #494949;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-single__meta li {
    font-size: 14px;
  }
}
.blog-single__meta li:not(:first-child) {
  padding-left: 30px;
}
.blog-single__meta li:not(:first-child)::before {
  position: absolute;
  top: 50%;
  left: 12px;
  content: "";
  width: 6px;
  height: 6px;
  background-color: #494949;
  transform: translateY(-50%) rotate(45deg);
}
.blog-single__meta li a:hover {
  color: #d68d67;
}
.blog-single__image {
  margin-top: 60px;
  padding-bottom: 46%;
  position: relative;
}
.blog-single__image img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.blog-single__content {
  margin-top: 90px;
  margin-bottom: 90px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-single__content {
    margin-top: 50px;
    margin-bottom: 50px;
  }
}
.blog-single__content p {
  font-size: 18px;
  line-height: 1.8;
  margin-bottom: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-single__content p {
    font-size: 16px;
  }
}
.blog-single__content p:last-child {
  margin-bottom: 0;
}
.blog-single__content figure {
  margin-left: calc(-0.5 * (1170px - 100%));
  margin-right: calc(-0.5 * (1170px - 100%));
  margin-top: 40px;
  margin-bottom: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-single__content figure {
    margin-left: 0;
    margin-right: 0;
  }
}
.blog-single__content figure img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog-single__content blockquote {
  background-color: #ede2e2;
  background-image: url(../images/icon/quote-icon.svg);
  background-repeat: no-repeat;
  border: none;
  padding: 8% 14% 6%;
  background-position: 12% 25%;
  color: #363636;
  line-height: 1.4;
  margin-top: 1.5em;
  margin-bottom: 1.5em;
  margin-left: calc(-0.5 * (1170px - 100%));
  margin-right: calc(-0.5 * (1170px - 100%));
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single__content blockquote {
    margin-left: 0;
    margin-right: 0;
    padding: 6%;
    background-position: 5% 22%;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single__content blockquote {
    padding: 40px 30px;
    margin-left: 0;
    margin-right: 0;
    background-position: 10px 30px;
    background-size: 70px;
  }
}
.blog-single__content blockquote p {
  font-size: 28px;
  font-family: "Playfair Display", serif;
  line-height: 1.5;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single__content blockquote p {
    font-size: 26px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single__content blockquote p {
    font-size: 20px;
  }
}
.blog-single__content blockquote cite {
  display: inline-flex;
  flex-direction: column;
  font-style: normal;
  align-items: flex-end;
  line-height: 1.3;
  font-size: 0.875em;
}
.blog-single__content blockquote cite em {
  font-size: 14px;
}
.blog-single__heading {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #363636;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single__heading {
    font-size: 26px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single__heading {
    font-size: 24px;
  }
}
.blog-single__comment > * {
  margin-top: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-single__comment > * {
    margin-top: 50px;
  }
}

.blog-single-meta {
  display: flex;
  flex-wrap: wrap;
  padding: 40px 0 30px;
  border-top: 1px solid #a3a3a3;
  border-bottom: 1px solid #a3a3a3;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (max-width: 767px) {
  .blog-single-meta {
    padding: 20px 0 10px;
  }
}
.blog-single-meta__col {
  flex: 0 0 auto;
  width: 50%;
  display: flex;
  padding-bottom: 10px;
}
@media only screen and (max-width: 767px) {
  .blog-single-meta__col {
    width: 100%;
  }
}
.blog-single-meta__title {
  color: #a0a0a0;
  font-size: 18px;
  font-weight: 400;
  white-space: nowrap;
  line-height: 1.8;
  margin-right: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single-meta__title {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single-meta__title {
    font-size: 14px;
    margin-right: 15px;
  }
}
.blog-single-meta__item {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8px;
  margin-right: -8px;
}
.blog-single-meta__item li {
  padding: 0 8px;
}
.blog-single-meta__item li a {
  font-size: 18px;
  font-weight: 400;
  color: #363636;
  transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single-meta__item li a {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single-meta__item li a {
    font-size: 14px;
  }
}
.blog-single-meta__item li a:hover {
  color: #d68d67;
}