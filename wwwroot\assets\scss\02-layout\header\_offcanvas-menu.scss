/*----------------------------------------*/
/*           OffCanvas Menu CSS
/*----------------------------------------*/

// OffCanvas Menu
.offcanvas-sidebar {
    max-width: 450px;
    padding: 20px 0;

    &.offcanvas-end {
        width: 100%;
    }

    // OffCanvas Body
    .offcanvas-body {
        padding: 50px;
        display: flex;
        gap: 20px;
        flex-direction: column;

        &::-webkit-scrollbar-track {
            background-color: $white;
        }
        &::-webkit-scrollbar {
            width: 3px;
        }
        &::-webkit-scrollbar-thumb {
            background-color: $heading-color;
        }
    }

    &__close {
        position: absolute;
        border: 0;
        background: none;
        padding: 0;
        font-size: 26px;
        top: 30px;
        right: 30px;
        color: $body-color;
        transition: $transition-base;

        &:hover {
            color: $heading-color;
        }
    }

    &__menu {
        margin-bottom: 50px;
    }
    &__banner {
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        padding: 110px 40px 55px;
        position: relative;
        z-index: 1;

        &::before {
            position: absolute;
            content: "";
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: $black;
            z-index: -1;
            opacity: 0;
            visibility: hidden;
            transition: $transition-base;
        }

        &:hover {
            &::before {
                opacity: 0.2;
                visibility: visible;
            }
        }

        & .banner-title {
            color: rgb(198 32 32 / 0%);
            font-size: 80px;
            font-weight: 400;
            line-height: 0.9em;
            -webkit-text-stroke-width: 1px;
            stroke-width: 1px;
            -webkit-text-stroke-color: #c62020;
            stroke: #c62020;
        }
        & .banner-sub-title {
            color: $heading-color;
            font-size: 24px;
            font-weight: 400;
            line-height: 1.5em;
            margin-bottom: 10px;
        }
        & .banner-btn {
            font-size: 14px;
            line-height: 35px;
            fill: $heading-color;
            color: $heading-color;
            background-color: $white;
            box-shadow: 5px 5px 0 0 $heading-color;
            padding: 0 40px;
            transition: $transition-base;

            &:hover {
                color: $white;
                background-color: $brand-color;
            }
        }
    }

    &__info {
    }
    &__social {
    }
    &__copyright {
        & p {
            font-size: 18px;
            color: $body-color;
        }
    }
}

// Off Canvas Menu List
.offcanvas-menu-list {
    & li {
        padding: 5px 0;

        :first-child {
            padding-top: 0;
        }
        &:last-child {
            padding-bottom: 0;
        }

        & a {
            font-size: 18px;
            color: $heading-color;
            line-height: 1.8;
            transition: $transition-base;
        }

        &:hover {
            & a {
                color: $brand-color;
            }
        }
    }
}

// Off Canvas Info List
.offcanvas-info-list {
    margin-top: 30px;

    & li {
        padding: 5px 0;

        :first-child {
            padding-top: 0;
        }
        &:last-child {
            padding-bottom: 0;
        }

        & span,
        & a {
            font-size: 18px;
            line-height: 1.7;
            transition: $transition-base;
            color: $body-color;
        }
        &:hover {
            & a {
                color: $brand-color;
            }
        }
    }
}

// Off Canvas Social
.offcanvas-social {
    display: flex;
    gap: 15px;

    & li {
        & a {
            font-size: 18px;
            color: $body-color;
        }
    }
}
