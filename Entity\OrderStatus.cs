using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TabakGokturk.Entity
{
    public class OrderStatus
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Title { get; set; }
        
        // Navigation properties
        public virtual ICollection<Order> Orders { get; set; }
        
        public OrderStatus()
        {
            Orders = new HashSet<Order>();
        }
    }
}
