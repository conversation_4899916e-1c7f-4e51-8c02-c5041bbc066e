using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TabakGokturk.Entity
{
    public class SKUCategory
    {
        [Key]
        public int Id { get; set; }
        
        public int CategoryId { get; set; }
        
        public int SKUId { get; set; }
        
        // Navigation properties
        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }
        
        [ForeignKey("SKUId")]
        public virtual SKU SKU { get; set; }
    }
}
