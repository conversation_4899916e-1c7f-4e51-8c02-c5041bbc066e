/*----------------------------------------*/
/*  02.08 - CountDown CSS
/*----------------------------------------*/

// CountDown Image
.countdown-section {
}

// CountDown Content
.countdown-content {
    &__countdown {
        display: flex;
        padding-bottom: 20px;

        @media #{$large-mobile} {
            justify-content: center;
            padding-bottom: 0;
        }

        & > * {
            padding: 0 30px;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                padding: 0 15px;
            }
            @media #{$large-mobile} {
                padding: 0 5px;
            }
        }
    }
    &__timer-item {
        display: flex;
        flex-flow: column nowrap;
        align-items: stretch;
        text-align: center;
        margin: 5px;

        @media #{$large-mobile} {
            justify-content: center;
        }

        &--value {
            color: $heading-color;
            font-size: 56px;
            font-weight: 600;
            line-height: 1;

            @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
                font-size: 36px;
            }
        }
        &--label {
            font-size: 24px;
            font-weight: 400;
            line-height: 1;
            margin-top: 10px;

            @media #{$laptop-device, $desktop-device, $tablet-device} {
                font-size: 18px;
            }
            @media #{$large-mobile} {
                font-size: 16px;
            }
        }
    }
}

// CountDown Content Two
.countdown-content-two {
    display: flex;
    margin: 35px 0 35px -10%;

    @media #{$tablet-device} {
        margin: 20px 0 20px -8%;
    }
    @media #{$large-mobile} {
        margin: 30px 0;
    }

    & > * {
    }

    &__timer-item {
        text-align: center;
        width: 24%;
        height: 35px;
        border-right: 2px solid #e0e0e0;
        display: flex;
        flex-direction: column;
        justify-content: center;

        &:last-child {
            border-right: 0;
        }

        &--value {
            color: $heading-color;
            font-family: $headings-font-family-02;
            font-size: 36px;
            font-weight: 400;
            line-height: 1.2;

            @media #{$laptop-device, $desktop-device} {
                font-size: 28px;
            }
            @media #{$tablet-device, $large-mobile} {
                font-size: 24px;
            }
            @media #{$large-mobile} {
                font-size: 28px;
            }
        }
        &--label {
            color: $heading-color;
            font-family: $headings-font-family-02;
            font-size: 18px;
            font-weight: 400;
            line-height: 1.2;

            @media #{$laptop-device, $desktop-device} {
                font-size: 16px;
            }
            @media #{$tablet-device, $large-mobile} {
                font-size: 16px;
            }
        }
    }
}
