/*----------------------------------------*/
/*  02.06 - Features CSS
/*----------------------------------------*/

// Features Text
.features-text {
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;

    & .swiper-slide {
        flex-shrink: inherit;
        width: auto;
    }
}

// Features Text Item
.features-text-item {
    & span {
        font-family: $headings-font-family-02;
        font-size: 86px;
        font-weight: 400;
        line-height: 1;
        color: #d6936b;
        white-space: nowrap;
        display: block;

        @media #{$laptop-device, $desktop-device, $tablet-device} {
            font-size: 56px;
        }
        @media #{$large-mobile} {
            font-size: 30px;
        }
    }
}
