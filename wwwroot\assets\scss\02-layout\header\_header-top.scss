/*----------------------------------------*/
/*             Header Top CSS
/*----------------------------------------*/

// Header Top
.header {
    &__top {
        background-color: #f8ece5;
        padding: 12px 0;

        &--wrapper {
            display: flex;

            @media #{$large-mobile} {
                display: block;
                text-align: center;
            }

            & p {
                color: $heading-color;
                font-size: 16px;
                font-weight: 700;
                line-height: 1.25;
                padding: 3px 0;

                @media #{$tablet-device} {
                    font-size: 14px;
                }

                @media #{$large-mobile} {
                    font-size: 13px;
                }
            }
        }

        &--left {
        }
        &--right {
        }

        &--items {
            margin-right: calc(-30px / 2);
            margin-left: calc(-30px / 2);
            display: flex;
            flex-wrap: wrap;

            @media #{$tablet-device} {
                margin-right: calc(-20px / 2);
                margin-left: calc(-20px / 2);
            }

            @media #{$large-mobile} {
                justify-content: space-between;
            }

            & li {
                padding-right: calc(30px / 2);
                padding-left: calc(30px / 2);

                @media #{$tablet-device} {
                    padding-right: calc(20px / 2);
                    padding-left: calc(20px / 2);
                }

                & a {
                    & i {
                        font-size: 14px;
                        color: $heading-color;
                    }
                    & span {
                        font-size: 14px;
                        color: $body-color;
                        padding-left: 5px;
                        transition: $transition-base;

                        @media #{$extraBig-device} {
                            font-size: 18px;
                        }
                    }

                    &:hover {
                        & span {
                            color: $brand-color;
                        }
                    }
                }
            }
        }
    }
}
