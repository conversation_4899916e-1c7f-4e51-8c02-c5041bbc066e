/*----------------------------------------*/
/*  02.29 Comment CSS
/*----------------------------------------*/

// Comment Items
.comment-title {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2;
    color: $heading-color;

    @media #{$tablet-device, $large-mobile} {
        font-size: 22px;
    }
}

// Comment Items
.comment-items {
    padding-top: 10px;

    & > * {
        margin-top: 40px;
    }
}

// Comment Item
.comment-item {
    display: flex;

    &__author {
        width: 90px;
        height: 90px;
        min-width: 90px;
        border-radius: 50%;
        margin-right: 40px;
        overflow: hidden;

        @media #{$laptop-device, $desktop-device} {
            width: 70px;
            height: 70px;
            min-width: 70px;
            margin-right: 30px;
        }
        @media #{$tablet-device, $large-mobile} {
            width: 50px;
            height: 50px;
            min-width: 50px;
            margin-right: 15px;
        }

        & img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
    &__content {
        position: relative;
        border: 1px solid #ccc;
        padding: 30px;
        flex-grow: 1;

        @media #{$large-mobile} {
            padding: 20px;
        }

        &::before {
            content: "“";
            position: absolute;
            top: -0.325em;
            left: 30px;
            font-size: 45px;
            font-family: serif;
            font-weight: 700;
            line-height: 1;
            background-color: $white;
            padding-left: 0.2em;
            padding-right: 0.2em;
        }
    }
    &__rating {
        margin-bottom: 10px;

        & span {
            overflow: hidden;
            position: relative;
            height: 1em;
            line-height: 1;
            font-size: 14px;
            color: #b3bdbc;
            display: inline-block;
            vertical-align: middle;
            letter-spacing: 0.2em;

            &::before {
                content: "\ea5e \ea5e \ea5e \ea5e \ea5e";
                display: inline-block;
                font: normal normal normal 1em/1 LaStudioIcons;
                font-size: 14px;
                speak: none;
                text-transform: none;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        & .star-rating {
            & > span {
                overflow: hidden;
                float: left;
                top: 0;
                left: 0;
                height: 100%;
                position: absolute;
                color: #f5a623;
            }
        }
    }
    &__description {
        margin-bottom: 0.8em;
    }
    &__meta {
        font-size: 14px;
        color: $body-color;
        line-height: 1.6;

        & strong {
            font-weight: 600;
            color: $heading-color;
        }
    }
}

// Comment Reply
.comment-reply {
    padding-left: 130px;
    padding-top: 10px;

    @media #{$tablet-device, $large-mobile} {
        padding-left: 25px;
    }

    & > * {
        margin-top: 40px;
    }

    & .comment-item {
        &__author {
            width: 50px;
            height: 50px;
            min-width: 50px;
            margin-right: 25px;

            @media #{$tablet-device, $large-mobile} {
                width: 45px;
                height: 45px;
                min-width: 45px;
                margin-right: 15px;
            }
        }
    }

    // Comment Reply
    & .comment-reply {
        padding-left: 75px;

        @media #{$tablet-device, $large-mobile} {
            padding-left: 25px;
        }
    }
}

// Comment Form
.comment-form {
    margin-top: 30px;

    &__notes {
        font-size: 14px;
        font-weight: 400;
        font-style: italic;
        border-bottom: 1px solid rgba($black, 0.1);
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    &__rating {
        margin-bottom: 10px;
    }

    // Single Form
    .single-form {
        &__btn {
            min-width: 180px;
            font-size: 16px;
        }
    }
}
